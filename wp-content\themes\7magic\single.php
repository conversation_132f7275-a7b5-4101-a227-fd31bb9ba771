<?php
/**
 * The template for displaying all single posts
 *
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('single-post'); ?>>
                
                <header class="entry-header">
                    <h1 class="entry-title"><?php the_title(); ?></h1>
                    
                    <div class="entry-meta">
                        <span class="posted-on">
                            <time datetime="<?php echo get_the_date('c'); ?>">
                                <?php echo get_the_date(); ?>
                            </time>
                        </span>
                        
                        <span class="byline">
                            by <?php the_author(); ?>
                        </span>
                        
                        <?php if (has_category()) : ?>
                            <span class="cat-links">
                                in <?php the_category(', '); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </header>
                
                <?php if (has_post_thumbnail()) : ?>
                    <div class="post-thumbnail">
                        <?php the_post_thumbnail('large'); ?>
                    </div>
                <?php endif; ?>
                
                <div class="entry-content">
                    <?php
                    the_content();
                    
                    wp_link_pages(array(
                        'before' => '<div class="page-links">' . esc_html__('Pages:', '7magic'),
                        'after'  => '</div>',
                    ));
                    ?>
                </div>
                
                <footer class="entry-footer">
                    <?php if (has_tag()) : ?>
                        <div class="tag-links">
                            <strong>Tags:</strong> <?php the_tags('', ', '); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="post-navigation">
                        <?php
                        the_post_navigation(array(
                            'prev_text' => '<span class="nav-subtitle">' . esc_html__('Previous:', '7magic') . '</span> <span class="nav-title">%title</span>',
                            'next_text' => '<span class="nav-subtitle">' . esc_html__('Next:', '7magic') . '</span> <span class="nav-title">%title</span>',
                        ));
                        ?>
                    </div>
                </footer>
                
            </article>
            
            <?php if (comments_open() || get_comments_number()) : ?>
                <div class="comments-section">
                    <?php comments_template(); ?>
                </div>
            <?php endif; ?>
            
        <?php endwhile; ?>
        
    </div>
</main>

<style>
/* Single post styles */
.single-post {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.entry-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.entry-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
    font-size: 0.9rem;
    color: #666;
}

.entry-meta span {
    display: flex;
    align-items: center;
}

.entry-meta a {
    color: #ff6b6b;
    text-decoration: none;
}

.entry-meta a:hover {
    text-decoration: underline;
}

.post-thumbnail {
    margin-bottom: 2rem;
    text-align: center;
}

.post-thumbnail img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.entry-content {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
    color: #333;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.entry-content h2 {
    font-size: 2rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.entry-content h3 {
    font-size: 1.5rem;
    color: #ff6b6b;
}

.entry-content p {
    margin-bottom: 1.5rem;
}

.entry-content ul,
.entry-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.entry-content li {
    margin-bottom: 0.5rem;
}

.entry-content blockquote {
    background: #f9f9f9;
    border-left: 4px solid #ff6b6b;
    padding: 1rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #666;
}

.entry-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 1rem 0;
}

.entry-footer {
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
}

.tag-links {
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.tag-links a {
    display: inline-block;
    background: #f0f0f0;
    color: #666;
    padding: 4px 12px;
    margin: 2px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.tag-links a:hover {
    background: #ff6b6b;
    color: white;
}

.post-navigation {
    margin-top: 2rem;
}

.post-navigation .nav-links {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
}

.post-navigation a {
    flex: 1;
    padding: 1rem;
    background: #f9f9f9;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.post-navigation a:hover {
    background: #ff6b6b;
    color: white;
}

.nav-subtitle {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.nav-title {
    display: block;
    font-weight: 600;
}

.page-links {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
    text-align: center;
}

.page-links a {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 4px;
    background: #f0f0f0;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-links a:hover {
    background: #ff6b6b;
    color: white;
}

.comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
}

/* Responsive */
@media (max-width: 768px) {
    .single-post {
        padding: 1rem;
    }
    
    .entry-title {
        font-size: 2rem;
    }
    
    .entry-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .entry-content {
        font-size: 1rem;
    }
    
    .entry-content h2 {
        font-size: 1.5rem;
    }
    
    .entry-content h3 {
        font-size: 1.3rem;
    }
    
    .post-navigation .nav-links {
        flex-direction: column;
    }
}
</style>

<?php get_footer(); ?>
