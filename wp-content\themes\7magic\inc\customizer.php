<?php
/**
 * Theme Customizer
 * 
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Customizer Settings
 */
function sevenmagic_customize_register($wp_customize) {
    
    // Add 7Magic Panel
    $wp_customize->add_panel('sevenmagic_panel', array(
        'title' => '7Magic Theme Options',
        'description' => 'Customize your 7Magic theme settings',
        'priority' => 30,
    ));
    
    // Colors Section
    $wp_customize->add_section('sevenmagic_colors', array(
        'title' => 'Colors',
        'panel' => 'sevenmagic_panel',
        'priority' => 10,
    ));
    
    // Primary Color
    $wp_customize->add_setting('sevenmagic_primary_color', array(
        'default' => '#ff6b6b',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'sevenmagic_primary_color', array(
        'label' => 'Primary Color',
        'section' => 'sevenmagic_colors',
        'description' => 'Main brand color used for buttons and accents',
    )));
    
    // Secondary Color
    $wp_customize->add_setting('sevenmagic_secondary_color', array(
        'default' => '#ff8e8e',
        'sanitize_callback' => 'sanitize_hex_color',
    ));
    
    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'sevenmagic_secondary_color', array(
        'label' => 'Secondary Color',
        'section' => 'sevenmagic_colors',
        'description' => 'Secondary color for gradients and highlights',
    )));
    
    // Hero Section
    $wp_customize->add_section('sevenmagic_hero', array(
        'title' => 'Hero Section',
        'panel' => 'sevenmagic_panel',
        'priority' => 20,
    ));
    
    // Hero Title
    $wp_customize->add_setting('sevenmagic_hero_title', array(
        'default' => 'Best IPL Laser Hair Removal Devices at Home',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('sevenmagic_hero_title', array(
        'label' => 'Hero Title',
        'section' => 'sevenmagic_hero',
        'type' => 'text',
    ));
    
    // Hero Subtitle
    $wp_customize->add_setting('sevenmagic_hero_subtitle', array(
        'default' => 'Professional IPL technology for safe, nearly painless treatment on legs, arms, underarms, and bikini area. Trusted by millions worldwide.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('sevenmagic_hero_subtitle', array(
        'label' => 'Hero Subtitle',
        'section' => 'sevenmagic_hero',
        'type' => 'textarea',
    ));
    
    // Hero Button Text
    $wp_customize->add_setting('sevenmagic_hero_button_text', array(
        'default' => 'Shop on Amazon',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('sevenmagic_hero_button_text', array(
        'label' => 'Hero Button Text',
        'section' => 'sevenmagic_hero',
        'type' => 'text',
    ));
    
    // Hero Button URL
    $wp_customize->add_setting('sevenmagic_hero_button_url', array(
        'default' => '#products',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('sevenmagic_hero_button_url', array(
        'label' => 'Hero Button URL',
        'section' => 'sevenmagic_hero',
        'type' => 'url',
    ));
    
    // Footer Section
    $wp_customize->add_section('sevenmagic_footer', array(
        'title' => 'Footer',
        'panel' => 'sevenmagic_panel',
        'priority' => 30,
    ));
    
    // Footer Text
    $wp_customize->add_setting('sevenmagic_footer_text', array(
        'default' => 'Leading the revolution in at-home IPL hair removal technology. Trusted by millions worldwide for safe, effective, and professional results.',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('sevenmagic_footer_text', array(
        'label' => 'Footer Description',
        'section' => 'sevenmagic_footer',
        'type' => 'textarea',
    ));
    
    // Social Links
    $social_networks = array(
        'facebook' => 'Facebook',
        'instagram' => 'Instagram',
        'twitter' => 'Twitter',
        'youtube' => 'YouTube',
    );
    
    foreach ($social_networks as $network => $label) {
        $wp_customize->add_setting("sevenmagic_social_{$network}", array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));
        
        $wp_customize->add_control("sevenmagic_social_{$network}", array(
            'label' => "{$label} URL",
            'section' => 'sevenmagic_footer',
            'type' => 'url',
        ));
    }
    
    // Typography Section
    $wp_customize->add_section('sevenmagic_typography', array(
        'title' => 'Typography',
        'panel' => 'sevenmagic_panel',
        'priority' => 40,
    ));
    
    // Body Font Size
    $wp_customize->add_setting('sevenmagic_body_font_size', array(
        'default' => '16',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('sevenmagic_body_font_size', array(
        'label' => 'Body Font Size (px)',
        'section' => 'sevenmagic_typography',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 12,
            'max' => 24,
            'step' => 1,
        ),
    ));
    
    // Heading Font Weight
    $wp_customize->add_setting('sevenmagic_heading_font_weight', array(
        'default' => '700',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('sevenmagic_heading_font_weight', array(
        'label' => 'Heading Font Weight',
        'section' => 'sevenmagic_typography',
        'type' => 'select',
        'choices' => array(
            '400' => 'Normal (400)',
            '500' => 'Medium (500)',
            '600' => 'Semi Bold (600)',
            '700' => 'Bold (700)',
            '800' => 'Extra Bold (800)',
        ),
    ));
    
    // Layout Section
    $wp_customize->add_section('sevenmagic_layout', array(
        'title' => 'Layout',
        'panel' => 'sevenmagic_panel',
        'priority' => 50,
    ));
    
    // Container Width
    $wp_customize->add_setting('sevenmagic_container_width', array(
        'default' => '1200',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('sevenmagic_container_width', array(
        'label' => 'Container Max Width (px)',
        'section' => 'sevenmagic_layout',
        'type' => 'number',
        'input_attrs' => array(
            'min' => 960,
            'max' => 1400,
            'step' => 20,
        ),
    ));
    
    // Products Per Row
    $wp_customize->add_setting('sevenmagic_products_per_row', array(
        'default' => '4',
        'sanitize_callback' => 'absint',
    ));
    
    $wp_customize->add_control('sevenmagic_products_per_row', array(
        'label' => 'Products Per Row (Desktop)',
        'section' => 'sevenmagic_layout',
        'type' => 'select',
        'choices' => array(
            '2' => '2 Products',
            '3' => '3 Products',
            '4' => '4 Products',
            '5' => '5 Products',
        ),
    ));
}
add_action('customize_register', 'sevenmagic_customize_register');

/**
 * Output Customizer CSS
 */
function sevenmagic_customizer_css() {
    $primary_color = get_theme_mod('sevenmagic_primary_color', '#ff6b6b');
    $secondary_color = get_theme_mod('sevenmagic_secondary_color', '#ff8e8e');
    $body_font_size = get_theme_mod('sevenmagic_body_font_size', '16');
    $heading_font_weight = get_theme_mod('sevenmagic_heading_font_weight', '700');
    $container_width = get_theme_mod('sevenmagic_container_width', '1200');
    $products_per_row = get_theme_mod('sevenmagic_products_per_row', '4');
    
    ?>
    <style type="text/css">
        :root {
            --primary-color: <?php echo esc_attr($primary_color); ?>;
            --secondary-color: <?php echo esc_attr($secondary_color); ?>;
        }
        
        body {
            font-size: <?php echo esc_attr($body_font_size); ?>px;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-weight: <?php echo esc_attr($heading_font_weight); ?>;
        }
        
        .container {
            max-width: <?php echo esc_attr($container_width); ?>px;
        }
        
        .product-grid {
            grid-template-columns: repeat(<?php echo esc_attr($products_per_row); ?>, 1fr);
        }
        
        .amazon-buy-btn {
            background: linear-gradient(135deg, <?php echo esc_attr($primary_color); ?>, <?php echo esc_attr($secondary_color); ?>);
        }
        
        .amazon-buy-btn:hover {
            background: linear-gradient(135deg, <?php echo esc_attr($primary_color); ?>dd, <?php echo esc_attr($secondary_color); ?>dd);
        }
        
        .section-title::after {
            background: linear-gradient(135deg, <?php echo esc_attr($primary_color); ?>, <?php echo esc_attr($secondary_color); ?>);
        }
        
        .product-price {
            color: <?php echo esc_attr($primary_color); ?>;
        }
        
        .stats-section h3 {
            background: linear-gradient(135deg, <?php echo esc_attr($primary_color); ?>, <?php echo esc_attr($secondary_color); ?>);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        @media (max-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 480px) {
            .product-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <?php
}
add_action('wp_head', 'sevenmagic_customizer_css');
