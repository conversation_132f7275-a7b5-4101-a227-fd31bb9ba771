if(!window.location.href.includes("ulikeofficial.myshopify")){let isElementInViewport2=function(element,index){const bounding=element.getBoundingClientRect();return bounding.bottom>=0&&bounding.top<=threshold},handleScroll2=function(){document.querySelectorAll(".to-left,.to-edge,.to-top,.slide-to-left,.to-opacity,.to-top1,.text-top,.text-top-btn,.slide-to-left-white,.line-top").forEach((element,index)=>{!element.classList.contains("appear")&&isElementInViewport2(element,index)&&element.classList.add("appear")})};var isElementInViewport=isElementInViewport2,handleScroll=handleScroll2;const threshold=window.innerHeight+100;window.initialCheck=function(){document.querySelectorAll(".to-left,.to-edge,.to-top,.slide-to-left,.to-opacity,.to-top1,.text-top,.text-top-btn,.slide-to-left-white,.line-top").forEach((element,index)=>{isElementInViewport2(element,index)&&element.classList.add("appear")})},window.addEventListener("load",initialCheck),window.addEventListener("scroll",handleScroll2)}(function(){if(typeof window.$>"u"){const script=document.createElement("script");script.src="https://www.ulike.com/cdn/shop/t/56/assets/jquery.min.js",script.type="text/javascript",script.onload=function(){},document.head.appendChild(script)}})(),window.location.href.includes("ulikeofficial.myshopify")&&function(){const callback=(entries,observer2)=>{entries.forEach(entry=>{entry.isIntersecting&&(entry.target.classList.add("appear"),(element=>{element.querySelectorAll(".to-left,.to-edge,.to-top,.slide-to-left,.to-opacity,.to-top1,.text-top,.text-top-btn,.slide-to-left-white,.line-top").forEach(child=>{child.classList.add("appear")})})(entry.target),observer2.unobserve(entry.target))})},observer=new IntersectionObserver(callback,{root:null,rootMargin:"0px",threshold:.1}),observeElements=()=>{document.querySelectorAll(".to-left,.to-edge,.to-top,.slide-to-left,.to-opacity,.to-top1,.text-top,.text-top-btn,.slide-to-left-white,.line-top").forEach(element=>{observer.observe(element)})};observeElements();const mutationCallback=(mutationsList,mutationObserver2)=>{for(let mutation of mutationsList)mutation.type==="childList"&&observeElements()};new MutationObserver(mutationCallback).observe(document.body,{childList:!0,subtree:!0})}();function wrapWordsWithSpanAndAddClass(node){if(node.nodeType===Node.TEXT_NODE){const words=node.textContent.split(/\s+/);if(words.length>1){const spanFragment=document.createDocumentFragment();words.forEach((word,index)=>{if(word){const wordNode=document.createElement("span");wordNode.textContent=word,spanFragment.appendChild(wordNode),index<words.length-1&&spanFragment.appendChild(document.createTextNode(" "))}}),node.parentNode.insertBefore(spanFragment,node),node.remove()}}else node.nodeType===Node.ELEMENT_NODE&&(node.childNodes.length===0?node.classList.add("text-oth"):Array.from(node.childNodes).forEach(childNode=>{wrapWordsWithSpanAndAddClass(childNode)}))}function getInitialTransitionDelay(node){if(node.nodeType===Node.ELEMENT_NODE){const transitionDelay=window.getComputedStyle(node).transitionDelay;if(transitionDelay!=="0s")return parseFloat(transitionDelay)||0}return 0}let transitionDelayCounter=0;function addTransitionDelay(node,initialDelay){if(node.nodeType===Node.ELEMENT_NODE){(node.tagName==="SPAN"||node.classList.contains("text-oth"))&&(node.style.transitionDelay=`${initialDelay+transitionDelayCounter*.1}s`,transitionDelayCounter+=.5);for(const childNode of node.childNodes)addTransitionDelay(childNode,initialDelay)}}function wrapWordsWithSpanAndAddClass1(node,width){if(node.nodeType===Node.TEXT_NODE){const words=node.textContent.split(/\b/);if(words.length>1){const newContainer=document.createDocumentFragment();let startIndex=0,line="";const testElement=document.createElement("span");testElement.style.display="inline-block",testElement.style.whiteSpace="nowrap",testElement.className="test-span-ulike";for(let i=0;i<words.length;i++){const char=words[i];testElement.textContent=line+char,node.parentNode.appendChild(testElement),parseFloat(window.getComputedStyle(testElement).width)>width?(node.parentNode.removeChild(testElement),newContainer.appendChild(document.createElement("span")).textContent=line,line=char,startIndex=i):line=line+char}line.trim()!==""&&(newContainer.appendChild(document.createElement("span")).textContent=line.trim(),node.parentNode.querySelector(".test-span-ulike")&&node.parentNode.removeChild(testElement)),node.parentNode.insertBefore(newContainer,node),node.remove()}}else node.nodeType===Node.ELEMENT_NODE&&(node.childNodes.length===0?node.classList.add("text-oth"):Array.from(node.childNodes).forEach(childNode=>{wrapWordsWithSpanAndAddClass1(childNode,width)}))}document.querySelectorAll(".text-top").forEach(el=>{transitionDelayCounter=0,wrapWordsWithSpanAndAddClass(el),addTransitionDelay(el,getInitialTransitionDelay(el))}),document.querySelectorAll(".line-top").forEach(el=>{const containerWidth=parseFloat(window.getComputedStyle(el).width);transitionDelayCounter=0,wrapWordsWithSpanAndAddClass1(el,containerWidth),addTransitionDelay(el,getInitialTransitionDelay(el))});function animateNumbersVisibleAnime(name){let elements=document.querySelectorAll(name);const observer=new IntersectionObserver(entries=>{entries.forEach(entry=>{if(entry.isIntersecting){const element=entry.target,start=0,end=parseInt(element.textContent.match(/\d+/)[0]),duration=Math.max(800,Math.min(2e3,Math.abs(end-start)*20));animateNumber(element,start,end,duration),observer.unobserve(element)}})});elements.forEach(element=>{observer.observe(element)})}function animateNumber(element,start,end,duration){const initialValue=element.textContent;let startTime=null;const step=timestamp=>{startTime||(startTime=timestamp);const progress=Math.min((timestamp-startTime)/duration,1),value=Math.floor(progress*(end-start)+start),newValue=initialValue.replace(/\d+/,value.toString().padStart(initialValue.match(/\d+/)[0].length,"0"));element.textContent=newValue,progress<1&&requestAnimationFrame(step)};requestAnimationFrame(step)}
//# sourceMappingURL=/cdn/shop/t/56/assets/scroll-animation.js.map?v=54254479390510808771724206816
