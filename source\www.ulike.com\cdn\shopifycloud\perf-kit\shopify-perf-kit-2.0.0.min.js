!function(){"use strict";class t{t;o=0;i=[];u(t){if(t.hadRecentInput)return;const e=this.i[0],n=this.i.at(-1);this.o&&e&&n&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(this.o+=t.value,this.i.push(t)):(this.o=t.value,this.i=[t]),this.t?.(t)}}const e=()=>{const t=performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},n=t=>{if("loading"===document.readyState)return"loading";{const n=e();if(n){if(t<n.domInteractive)return"loading";if(0===n.domContentLoadedEventStart||t<n.domContentLoadedEventStart)return"dom-interactive";if(0===n.domComplete||t<n.domComplete)return"dom-content-loaded"}}return"complete"},i=t=>{const e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},o=t=>{let e="";try{for(;9!==t?.nodeType;){const n=t,o=n.id?"#"+n.id:[i(n),...Array.from(n.classList).sort()].join(".");if(e.length+o.length>99)return e||o;if(e=e?o+">"+e:o,n.id)break;t=n.parentNode}}catch{}return e},r=new WeakMap;function s(t,e){return r.get(t)||r.set(t,new e),r.get(t)}let a=-1;const c=()=>a,u=t=>{addEventListener("pageshow",e=>{e.persisted&&(a=e.timeStamp,t(e))},!0)},d=(t,e,n,i)=>{let o,r;return s=>{e.value>=0&&(s||i)&&(r=e.value-(o??0),(r||void 0===o)&&(o=e.value,e.delta=r,e.rating=((t,e)=>t>e[1]?"poor":t>e[0]?"needs-improvement":"good")(e.value,n),t(e)))}},l=t=>{requestAnimationFrame(()=>requestAnimationFrame(()=>t()))},h=()=>{const t=e();return t?.activationStart??0},p=(t,n=-1)=>{const i=e();let o="navigate";return c()>=0?o="back-forward-cache":i&&(document.prerendering||h()>0?o="prerender":document.wasDiscarded?o="restore":i.type&&(o=i.type.replace(/_/g,"-"))),{name:t,value:n,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:o}},m=(t,e,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const i=new PerformanceObserver(t=>{Promise.resolve().then(()=>{e(t.getEntries())})});return i.observe({type:t,buffered:!0,...n}),i}}catch{}},f=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let g=-1;const v=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,y=t=>{"hidden"===document.visibilityState&&g>-1&&(g="visibilitychange"===t.type?t.timeStamp:0,T())},S=()=>{addEventListener("visibilitychange",y,!0),addEventListener("prerenderingchange",y,!0)},T=()=>{removeEventListener("visibilitychange",y,!0),removeEventListener("prerenderingchange",y,!0)},w=()=>{if(g<0){const t=h(),e=document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(e=>"hidden"===e.name&&e.startTime>t)[0]?.startTime;g=e??v(),S(),u(()=>{setTimeout(()=>{g=v(),S()})})}return{get firstHiddenTime(){return g}}},_=t=>{document.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},b=[1800,3e3],k=(t,e={})=>{_(()=>{const n=w();let i,o=p("FCP");const r=m("paint",t=>{for(const e of t)"first-contentful-paint"===e.name&&(r.disconnect(),e.startTime<n.firstHiddenTime&&(o.value=Math.max(e.startTime-h(),0),o.entries.push(e),i(!0)))});r&&(i=d(t,o,b,e.reportAllChanges),u(n=>{o=p("FCP"),i=d(t,o,b,e.reportAllChanges),l(()=>{o.value=performance.now()-n.timeStamp,i(!0)})}))})},M=[.1,.25],E=t=>t.find(t=>1===t.node?.nodeType)||t[0];let C=0,I=1/0,x=0;const P=t=>{for(const e of t)e.interactionId&&(I=Math.min(I,e.interactionId),x=Math.max(x,e.interactionId),C=x?(x-I)/7+1:0)};let L;const D=()=>L?C:performance.interactionCount??0;let A=0;class R{l=[];h=new Map;m;p;v(){A=D(),this.l.length=0,this.h.clear()}M(){const t=Math.min(this.l.length-1,Math.floor((D()-A)/50));return this.l[t]}u(t){if(this.m?.(t),!t.interactionId&&"first-input"!==t.entryType)return;const e=this.l.at(-1);let n=this.h.get(t.interactionId);if(n||this.l.length<10||t.duration>e.T){if(n?t.duration>n.T?(n.entries=[t],n.T=t.duration):t.duration===n.T&&t.startTime===n.entries[0].startTime&&n.entries.push(t):(n={id:t.interactionId,entries:[t],T:t.duration},this.h.set(n.id,n),this.l.push(n)),this.l.sort((t,e)=>e.T-t.T),this.l.length>10){const t=this.l.splice(10);for(const e of t)this.h.delete(e.id)}this.p?.(n)}}}const B=t=>{const e=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?t():(t=f(t),document.addEventListener("visibilitychange",t,{once:!0}),e(()=>{t(),document.removeEventListener("visibilitychange",t)}))},N=[200,500];class O{m;u(t){this.m?.(t)}}const j=[2500,4e3],q=[800,1800],z=t=>{document.prerendering?_(()=>z(t)):"complete"!==document.readyState?addEventListener("load",()=>z(t),!0):setTimeout(t)},F="",H="1",U="0",J="p",$="a",V="m",W="t",K="m",X="a",G="p",Y="s";function Z(t){try{return decodeURIComponent(t)}catch(t){return""}}function Q(t,e=!1){const n=function(){try{return document.cookie}catch{return!1}}()?document.cookie.split("; "):[];for(let e=0;e<n.length;e++){const[i,o]=n[e].split("=");if(t===Z(i)){return Z(o)}}if(e&&"_tracking_consent"===t&&!window.localStorage.getItem("tracking_consent_fetched")){if("undefined"!=typeof __CtaTestEnv__&&"true"===__CtaTestEnv__)return;return console.debug("_tracking_consent missing"),function(t="/"){const e=new XMLHttpRequest;e.open("HEAD",t,!1),e.withCredentials=!0,e.send()}(),window.localStorage.setItem("tracking_consent_fetched","true"),Q(t,!1)}}function tt(){const t=new URLSearchParams(window.location.search).get("_cs")||Q("_tracking_consent");if(void 0!==t)return function(t){const e=t.slice(0,1);if("{"==e)return function(t){var e;let n;try{n=JSON.parse(t)}catch{return}if("2.1"!==n.v)return;if(null===(e=n.con)||void 0===e||!e.CMP)return;return n}(t);if("3"==e)return function(t){const e=t.slice(1).split("_"),[n,i,o,r,s]=e;let a,c;try{a=e[5]?JSON.parse(e.slice(5).join("_")):void 0}catch{}if(s){const t=s.replace(/\*/g,"/").replace(/-/g,"+"),e=Array.from(atob(t)).map(t=>t.charCodeAt(0).toString(16).padStart(2,"0")).join("");c=[8,13,18,23].reduce((t,e)=>t.slice(0,e)+"-"+t.slice(e),e)}function u(t){const e=n.split(".")[0];return e.includes(t.toLowerCase())?U:e.includes(t.toUpperCase())?H:F}function d(t){return n.includes(t.replace("t","s").toUpperCase())}return{v:"3",con:{CMP:{[X]:u(X),[G]:u(G),[K]:u(K),[Y]:u(Y)}},region:i||"",cus:a,purposes:{[$]:d($),[J]:d(J),[V]:d(V),[W]:d(W)},sale_of_data_region:"t"==r,display_banner:"t"==o,consent_id:c}}(t);return}(t)}function et(){return function(t){const e=tt();if(!e||!e.purposes)return!0;const n=e.purposes[t];return"boolean"!=typeof n||n}($)}function nt(){return et()}var it="2.0.0";const ot=Object.freeze({shop_domain:`${window.location.origin}/.well-known/shopify/monorail/v1/produce`,global:"https://monorail-edge.shopifysvc.com/v1/produce",canada:"https://monorail-edge-ca.shopifycloud.com/v1/produce",staging:"https://monorail-edge-staging.shopifycloud.com/v1/produce"}),rt=new RegExp(`^${window.location.origin}/((?:(?:[a-z]{2,3}|zh-hans|zh-hant)(?:-[a-zA-Z0-9]+)/)?cart/(add|change|update|clear))`),st=new RegExp("cart-performance:|add:|change:|clear:|note_update:");var at;function ct({monorailRegion:t,schema:e,rawData:n}){const i=Date.now(),o={schema_id:e,payload:e===at.OnUnload?ut(n):dt(n),metadata:{event_created_at_ms:i,event_sent_at_ms:i}},r=ot[t||""],s=JSON.stringify(o);if(!r)return void console.debug("📡 Monorail: ",JSON.stringify(o,null,2));try{const t=new window.Blob([s],{type:"text/plain"});if("function"==typeof window.navigator.sendBeacon&&"function"==typeof window.Blob&&!function(){const{userAgent:t}=window.navigator;return-1!==t.lastIndexOf("iPhone; CPU iPhone OS 12_")||-1!==t.lastIndexOf("iPad; CPU OS 12_")}()&&window.navigator.sendBeacon(r,t))return}catch(t){}const a=new XMLHttpRequest;a.open("POST",r),a.setRequestHeader("Content-type","text/plain"),a.send(s)}function ut(t){const e=lt(t,["domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","firstPaint","visuallyReady","initiatorType","redirectCount","cartAjaxResourceTimings","cartPerformanceMetrics"]);return{perf_kit_init:t.perfKitInit,perf_kit_version:t.perfKitVersion,url:t.url,page_type:t.pageType,shop_id:t.shopId,application:t.application,storefront_id:t.storefrontId,theme_instance_id:t.themeInstanceId,theme_name:t.themeName,theme_version:t.themeVersion,session_token:t.sessionToken,unique_token:t.uniqueToken,micro_session_id:t.microSessionId,micro_session_count:t.microSessionCount,cumulative_layout_shift:t.cumulativeLayoutShift,cumulative_layout_shift_target:t.cumulativeLayoutShiftTarget,first_contentful_paint:t.firstContentfulPaint,largest_contentful_paint:t.largestContentfulPaint,largest_contentful_paint_target:t.largestContentfulPaintTarget,time_to_first_byte:t.timeToFirstByte,seo_bot:t.seoBot,humanness_score:t.humannessScore,ja3_fingerprint:t.ja3Fingerprint,navigation_start:t.navigationStart,navigation_type:t.navigationType,navigation_bad:t.navigationBad,encoded_body_size:t.encodedBodySize,decoded_body_size:t.decodedBodySize,transfer_size:t.transferSize,first_interim_response_start:t.firstInterimResponseStart,final_response_headers_start:t.finalResponseHeadersStart,response_start:t.responseStart,response_end:t.responseEnd,worker_start:t.workerStart,connect_start:t.connectStart,connect_end:t.connectEnd,domain_lookup_start:t.domainLookupStart,domain_lookup_end:t.domainLookupEnd,fetch_start:t.fetchStart,redirect_start:t.redirectStart,redirect_end:t.redirectEnd,request_start:t.requestStart,secure_connection_start:t.secureConnectionStart,next_hop_protocol:t.nextHopProtocol,server_timing:t.serverTiming,paint_timing_hidden:t.paintTimingHidden,referrer:t.referrer,render_region:t.renderRegion,resource_timing:t.resourceTiming,other_metrics:JSON.stringify(e)}}function dt(t){const e=lt(t,["longAnimationFrame"]);return{url:t.url,page_type:t.pageType,shop_id:t.shopId,application:t.application,storefront_id:t.storefrontId,theme_instance_id:t.themeInstanceId,session_token:t.sessionToken,unique_token:t.uniqueToken,micro_session_id:t.microSessionId,micro_session_count:t.microSessionCount,interaction_to_next_paint:t.interactionToNextPaint,interaction_to_next_paint_target:t.interactionToNextPaintTarget,seo_bot:t.seoBot,humanness_score:t.humannessScore,ja3_fingerprint:t.ja3Fingerprint,referrer:t.referrer,worker_start:t.workerStart,next_hop_protocol:t.nextHopProtocol,navigation_bad:t.navigationBad,other_interaction_metrics:JSON.stringify(e)}}function lt(t,e){return e.reduce((e,n)=>{var i;return t[n]&&(e[(i=n,i.replace(/[A-Z]/g,t=>`_${t.toLowerCase()}`))]=t[n]||null),e},{})}!function(t){t.OnInteraction="perf_kit_on_interaction/3.1",t.OnUnload="perf_kit_on_unload/3.3"}(at||(at={}));const ht="xxxx-4xxx-xxxx-xxxxxxxxxxxx";function pt(){let t="";try{const e=window.crypto,n=new Uint16Array(31);e.getRandomValues(n);let i=0;t=ht.replace(/[x]/g,t=>{const e=n[i]%16;return i++,("x"===t?e:3&e|8).toString(16)}).toUpperCase()}catch(e){t=ht.replace(/[x]/g,t=>{const e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)}).toUpperCase()}return`${function(){let t=0,e=0;t=(new Date).getTime()>>>0;try{e=performance.now()>>>0}catch(t){e=0}return Math.abs(t+e).toString(16).toLowerCase().padStart(8,"0")}()}-${t}`}const mt=St;!function(t){const e=St,n=t();for(;;)try{if(473111===-parseInt(e(283))/1*(parseInt(e(248))/2)+parseInt(e(231))/3+parseInt(e(325))/4*(parseInt(e(347))/5)+parseInt(e(235))/6*(parseInt(e(279))/7)+parseInt(e(328))/8+-parseInt(e(275))/9+-parseInt(e(222))/10)break;n.push(n.shift())}catch(t){n.push(n.shift())}}(Mt);const ft=function(){let t=!0;return function(e,n){const i=t?function(){if(n){const t=n[St(353)](e,arguments);return n=null,t}}:function(){};return t=!1,i}}(),gt=ft(this,function(){const t=St;return gt.toString()[t(307)]("(((.+)+)+)+$")[t(360)]()[t(339)](gt)[t(307)](t(326))});gt();class vt{[mt(290)];options;constructor(t,e){const n=mt;this[n(290)]=t,this[n(358)]={path:"/",sameSite:n(323),debug:!1,...e}}async[mt(342)](){const t=mt,e=this.options[t(241)];if(this[t(290)][t(334)])try{const n=await this[t(290)][t(334)][t(342)]({name:e});return n?n[t(229)]:null}catch(e){return this[t(340)]()}return this[t(340)]()}async[mt(314)](t){const e=mt;if(this.browserAPI[e(334)])try{await this[e(290)][e(334)][e(314)]({name:this[e(358)].cookieName,value:t,path:this[e(358)].path,maxAge:this[e(358)][e(243)],sameSite:this[e(358)][e(368)]?.[e(318)]()})[e(250)](n=>{this[e(369)](t)})}catch(n){this[e(369)](t)}else this[e(369)](t)}getLegacy(){const t=mt,e=this[t(358)][t(241)],n=new RegExp(t(303)+e+t(266))[t(227)](this.browserAPI[t(320)][t(239)]);return n?n[2]:null}[mt(369)](t){const e=mt;this.browserAPI.document[e(239)]=this[e(358)].cookieName+"="+t+"; path="+this[e(358)][e(230)]+e(367)+this.options.cookieExpiry+e(260)+this[e(358)][e(368)]}log(t){const e=mt;this[e(358)][e(359)]&&console[e(277)]("[CookieJar] "+t)}}const yt={document:document,navigator:navigator,window:window,screen:screen,cookieStore:"undefined"!=typeof window&&"cookieStore"in window?window[mt(334)]:void 0,performance:typeof performance!==mt(237)?performance:void 0};function St(t,e){const n=Mt();return St=function(t,e){return n[t-=215]},St(t,e)}function Tt(t){const e=mt;if(t[e(245)]<2)return{speed:0,jitter:0};const n=t[e(261)]((t,e)=>t+e.s,0)/t[e(245)],i=t[e(261)]((t,e)=>t+(e.s-n)**2,0)/(t[e(245)]-1),o=Math[e(349)](i);return{speed:n,jitter:o}}function wt(t,e,n,i,o,r,s,a,c,u,d,l,h){const p=mt,m=Math.min(1e3,t),f=Math[p(285)](1e3,n),g=Math.min(1e3,r),v=Math[p(285)](1e3,a),y=Math[p(285)](1e3,s),S=Math[p(285)](1e3,c),T=[];if(e[p(245)]>=3)for(let t=1;t<e[p(245)];t++){const n=e[t].x-e[t-1].x,i=e[t].y-e[t-1].y,o=e[t].t-e[t-1].t;if(0===o)continue;const r=Math[p(349)](n*n+i*i)/o;T[p(351)]({s:r})}const w=function(t){const e=mt;if(t[e(245)]<2)return!1;let n=!1,i=null;for(const o of t)if(null!==i){if(Math[e(354)](o.s-i)>5e-4){n=!0;break}i=o.s}else i=o.s;return n}(T),_=function(t){const e=mt;if(t[e(245)]<3)return 0;let n=0,i=0;for(let o=1;o<t[e(245)];o++){const r=t[o-1],s=t[o],a=t[o+1];if(!r||!s||!a)continue;const c=Math.atan2(s.y-r.y,s.x-r.x),u=Math[e(308)](a.y-s.y,a.x-s.x),d=Math[e(354)](u-c);(d<Math.PI/12||d>2*Math.PI-Math.PI/12)&&n++,i++}return i>0?n/i:0}(e),{speed:b,jitter:k}=Tt(T);let M=0,E=0;if(i[p(245)]>1){M=new Set(i[p(242)](t=>Math.floor(t.x/20)+","+Math[p(337)](t.y/20)))[p(234)]>1?1:0,E=i[p(261)]((t,e)=>t+e[p(276)],0)/i[p(245)]}const C=o.reduce((t,e)=>{const n=p;return null===e[n(302)]||t[n(351)](e[n(302)]),t},[]),{rhythmConsistency:I,jitter:x}=function(t){const e=mt;if(t[e(245)]<2)return{rhythmConsistency:0,jitter:0};const n=t[e(261)]((t,e)=>t+e,0)/t.length,i=t.reduce((t,e)=>(t??0)+((e??0)-n)**2,0)/t.length,o=Math.sqrt(i);return{rhythmConsistency:0===o?1:Math[e(217)](0,1-o/n),jitter:o/n}}(C),P=C[p(245)]>0?C.reduce((t,e)=>t+e,0)/C[p(245)]:0,L=[];if(u.length>=3)for(let t=1;t<u[p(245)];t++){const e=Math[p(354)](u[t].p-u[t-1].p),n=Math[p(217)](1,u[t].t-u[t-1].t);if(0===n)continue;const i=e/n,o=u[t].p>u[t-1].p?"down":"up";L[p(351)]({s:i,dir:o,t:u[t].t})}const{speed:D,jitter:A}=Tt(L),R=function(t){const e=mt;if(t[e(245)]<2)return 0;let n=0;for(let i=1;i<t[e(245)];i++){const o=t[i-1],r=t[i];Math[e(354)](r.s-o.s)<=(r.s+o.s)/2*2&&n++,r[e(294)]===o[e(294)]&&n++}return n/(2*(t[e(245)]-1))}(L),B=Math[p(285)](1e3,function(t){if(t.length<2)return 0;let e=0,n=0;for(let i=1;i<t.length;i++)t[i].t-t[i-1].t>=500&&t[i].t-n>=500&&(e++,n=t[i].t);return e}(L)),{speed:N,jitter:O}=Tt(d),j=function(t){const e=mt;if(t.length<2)return 0;let n=0;for(let i=1;i<t[e(245)];i++){const e=t[i-1],o=t[i];Math.abs(o.s-e.s)<=50&&n++}return n/(t[e(245)]-1)}(d),q=Math[p(285)](1e3,function(t){if(t[mt(245)]<2)return 0;let e=0;for(let n=1;n<t.length;n++)t[n].t-t[n-1].t>=500&&e++;return e}(d)),z=Math[p(337)]((h-l)/1e3);return{ma:m,ca:f,ka:g,sa:v,ta:S,kba:y,t:Math[p(285)](3600,z),nm:w?1:0,ms:bt(_),mj:bt(k),msp:bt(b),vc:M,cp:bt(E),rc:bt(I),kj:bt(x),ki:bt(P),ss:bt(D),sj:bt(A),ssm:bt(R),sp:B,ts:bt(N),tj:bt(O),tp:q,tsm:bt(j)}}function _t(t,e){let n=0;return(...i)=>{const o=St,r=typeof performance!==o(237)?performance[o(273)]():Date.now();r-n>=e&&(n=r,t(...i))}}function bt(t){return Math[mt(220)](100*t)/100}class kt{config;[mt(293)];[mt(290)];[mt(267)];constructor(t,e=yt){const n=mt,i={cookieName:n(215),cookieExpiry:1800,updateInterval:5e3,debug:!1};this[n(306)]={...i,...t},this.browserAPI=e,this.cookieJar=new vt(this[n(290)],{cookieName:this.config[n(241)],cookieExpiry:this[n(306)][n(243)],debug:this[n(306)][n(359)]}),this[n(293)]={startTime:Date.now(),lastUpdateTime:null,environmentChecked:!1,sessionData:null,mouseActivity:0,clickActivity:0,keyActivity:0,keyboardBurstActivity:0,scrollActivity:0,touchActivity:0,mousePositions:[],clickPositions:[],keyEvents:[],scrollEvents:[],keyboardBursts:[],touchEvents:[],lastTouchEvent:null}}[mt(270)](){(async()=>{const t=St;await this[t(258)](),await this[t(350)](),await this[t(265)](!0),this[t(290)][t(259)].setTimeout(()=>{this[t(297)]()},100);const e=()=>{const n=t,i=Date[n(273)](),o=i-(this[n(293)][n(244)]||i),r=Math[n(217)](0,this[n(306)][n(332)]-o),s=()=>{const t=n;this[t(290)][t(259)][t(240)]?this[t(293)][t(255)]=this[t(290)][t(259)].requestIdleCallback(()=>{this.updateCookie().then(()=>{e()})},{timeout:2e3}):this.updateCookie()[t(251)](()=>{e()})};r>0?this[n(290)][n(259)][n(341)](s,r):s()};e()})()}[mt(319)](){const t=mt;this.state[t(329)]&&this[t(290)][t(259)][t(256)](this.state[t(329)]),this[t(293)][t(255)]&&this[t(290)].window.cancelIdleCallback&&this[t(290)].window[t(225)](this[t(293)][t(255)]),this.state.eventHandlers&&(this.browserAPI[t(320)].removeEventListener(t(286),this.state[t(278)][t(286)]),this[t(290)][t(320)][t(331)](t(357),this.state[t(278)][t(357)]),this[t(290)][t(320)][t(331)](t(263),this.state[t(278)][t(263)]),this[t(290)][t(320)][t(331)](t(363),this.state[t(278)][t(363)]),t(247)in window&&this[t(293)][t(278)][t(295)]&&this[t(293)].eventHandlers.touchmove&&this.state[t(278)].touchend&&(this[t(290)][t(320)][t(331)]("touchstart",this[t(293)][t(278)][t(295)]),this[t(290)].document[t(331)](t(313),this[t(293)][t(278)][t(313)]),this[t(290)][t(320)][t(331)]("touchend",this[t(293)].eventHandlers[t(355)])))}async[mt(258)](){const t=mt,e=function(t){const e=mt;if(!t)return null;try{const n=JSON[e(272)](atob(decodeURIComponent(t)));return n.v&&n.ts?n:null}catch(t){return null}}(await this[t(267)][t(342)]());e&&(this[t(293)].lastUpdateTime=e.ts,e[t(224)]&&(this[t(293)][t(284)]={pageCount:e[t(224)].p||1,sessionStart:e[t(224)].s||Date[t(273)](),duration:e[t(224)].d||0}))}[mt(350)](){const t=mt,e={wd:this[t(290)][t(264)][t(281)]?1:0,ua:/bot|crawler|spider|scraper/i[t(296)](this[t(290)][t(264)][t(312)])?0:1,cv:this[t(238)]()?1:0,br:this[t(361)]()?1:0};return this[t(293)][t(299)]=e,this[t(293)][t(348)]=!0,e}runCanvasTest(){const t=mt;try{const e=this[t(290)][t(320)].createElement(t(257));e.width=200,e[t(336)]=50;const n=e[t(280)]("2d");if(null===n)return!1;const i=n[t(274)](0,0,200,0);i[t(253)](0,t(335)),i[t(253)](1,t(364)),n.fillStyle=i,n[t(228)](0,0,200,50),n.fillStyle=t(252),n[t(233)]=t(300),n[t(301)](t(305),33,30);const o=e[t(330)]();return Boolean(o&&o[t(245)]>50)}catch(t){return!1}}checkBrowserConsistency(){const t=mt;let e=0;this.browserAPI[t(264)].languages&&this[t(290)].navigator[t(333)].length>0&&e++,(this[t(290)].navigator.plugins&&this[t(290)].navigator[t(268)][t(245)]>0||this[t(290)][t(264)][t(312)][t(218)]("Mobile"))&&e++,this.checkViewportRatio()&&e++,void 0!==this[t(290)][t(259)][t(249)]&&e++;try{const n=(new Date)[t(321)]();n>-840&&n<840&&e++}catch(t){}return e>=3}checkViewportRatio(){const t=mt,e=this.browserAPI[t(259)][t(292)]||this[t(290)][t(320)].documentElement[t(269)],n=this[t(290)][t(259)].innerHeight||this.browserAPI[t(320)].documentElement[t(232)];if(!e||!n)return!1;if(e<=2||n<=2)return!1;const i=e/n;return i>=.2&&i<=5}monitorBehavior(){const t=mt,e=_t(t=>{const e=St;this.state[e(343)]++,this.state[e(304)][e(245)]>=10&&this[e(293)].mousePositions.shift(),this[e(293)].mousePositions.push({x:t[e(287)],y:t[e(315)],t:t[e(246)]})},100),n=t=>{const e=St;this.state[e(345)]++,requestAnimationFrame(()=>setTimeout(()=>{const n=e;this[n(293)].clickPositions.length>=5&&this.state.clickPositions.shift(),this[n(293)].clickPositions[n(351)]({x:t[n(287)],y:t[n(315)],t:t[n(246)],pre:this[n(316)](t)})},0))},i=t=>{const e=St;this[e(293)][e(282)]++;const n=t[e(226)];if(["Shift",e(338),e(344),e(221)][e(218)](n))return;const i=this[e(293)][e(254)][this[e(293)][e(254)][e(245)]-1],o=t.timeStamp,r=i?o-i.t:null;this.state[e(254)][e(245)]>=10&&this[e(293)][e(254)][e(309)](),this.state[e(254)][e(351)]({k:n,t:o,int:r}),null!==r&&r<100?(this[e(293)][e(317)].push(r),this[e(293)][e(317)].length>=5&&(this.state[e(219)]++,this.state[e(317)]=[])):this[e(293)].keyboardBursts=[]},o=_t(t=>{const e=St;this[e(293)][e(262)]++;const n=t[e(246)],i=window.scrollY;this.state[e(327)][e(245)]>=10&&this[e(293)][e(327)][e(309)](),this[e(293)][e(327)][e(351)]({p:i,t:n})},200),r=t=>{const e=St;this.state[e(362)]++,this[e(293)][e(356)][e(245)]>=10&&this[e(293)][e(356)][e(309)](),this.state[e(366)]={x:t[e(346)][0][e(287)],y:t[e(346)][0][e(315)],t:t[e(246)],typ:e(352),s:0,d:0}},s=_t(t=>{const e=St,n={x:t[e(346)][0][e(287)],y:t.touches[0][e(315)]};if(this[e(293)].lastTouchEvent){const i=this.getDistance(this[e(293)][e(366)],n);this[e(293)][e(356)][e(245)]>=10&&this[e(293)].touchEvents[e(309)]();const o={x:t.touches[0].clientX,y:t.touches[0][e(315)],t:t.timeStamp,typ:e(311),d:i,s:i/(t[e(246)]-this[e(293)][e(366)].t)};this[e(293)][e(356)][e(351)](o),this[e(293)].lastTouchEvent=o}else{const n={x:t.touches[0][e(287)],y:t[e(346)][0].clientY,t:t[e(246)],typ:e(311),s:0,d:0};this.state.lastTouchEvent=n}},200),a=t=>{const e=St;this[e(293)][e(366)]=null};this[t(290)][t(320)][t(322)]("mousemove",e,{passive:!0}),this[t(290)][t(320)][t(322)](t(357),n,{passive:!0}),this[t(290)][t(320)][t(322)](t(263),i,{passive:!0}),this.browserAPI.document[t(322)]("scroll",o,{passive:!0}),t(247)in window&&(this[t(290)][t(320)][t(322)](t(295),r,{passive:!0}),this[t(290)][t(320)][t(322)](t(313),s,{passive:!0}),this[t(290)][t(320)].addEventListener(t(355),a,{passive:!0})),this[t(293)][t(278)]={mousemove:e,click:n,keydown:i,scroll:o,touchstart:r,touchmove:s,touchend:a}}async[mt(265)](t=!1){const e=mt;this[e(324)](t);const n=this[e(298)]();n&&(await this[e(267)][e(314)](n),this[e(293)][e(244)]=Date.now())}[mt(324)](t=!1){const e=mt,n=Date[e(273)]();let i=Math[e(337)]((n-this[e(293)][e(223)])/1e3);null!==this[e(293)][e(244)]&&(i=Math[e(337)]((n-this[e(293)][e(244)])/1e3)),this.state.sessionData?(this[e(293)].sessionData.pageCount+=t?1:0,this[e(293)][e(284)][e(236)]+=i):this.state[e(284)]={pageCount:1,sessionStart:this.state[e(223)],duration:0}}[mt(298)](){const t=mt;if(!this[t(293)][t(348)]&&0===this[t(293)][t(343)]&&0===this[t(293)].clickActivity)return null;const e=this[t(293)][t(299)]??{wd:0,ua:1,cv:0,br:0},n=wt(this.state[t(343)],this[t(293)].mousePositions,this.state.clickActivity,this.state[t(288)],this[t(293)][t(254)],this.state.keyActivity,this[t(293)][t(219)],this[t(293)].scrollActivity,this.state[t(362)],this[t(293)][t(327)],this.state.touchEvents,this[t(293)].startTime,Date.now()),i={p:this[t(293)][t(284)]?.pageCount??1,s:this[t(293)].sessionData?.sessionStart??this[t(293)][t(223)],d:this[t(293)][t(284)]?.duration??0},o={v:2,ts:Date[t(273)](),env:e,bhv:n,ses:i};return encodeURIComponent(btoa(JSON.stringify(o)))}[mt(277)](t){const e=mt;this[e(306)][e(359)]&&console[e(277)]("[SHS] "+t)}[mt(316)](t){const e=mt,n=t.target;if(!(n&&n instanceof Element&&n[e(216)]))return 0;const i=n[e(216)]();if(!i.width||!i[e(336)])return 0;const o=i.left+i.width/2,r=i.top+i.height/2,s=this[e(271)]({x:t.clientX,y:t.clientY},{x:o,y:r}),a=Math[e(349)](i[e(289)]**2+i.height**2)/2;return a>0?Math[e(217)](0,1-s/a):0}[mt(271)](t,e){return Math[mt(349)]((e.x-t.x)**2+(e.y-t.y)**2)}}function Mt(){const t=["value","path","803556udGoiX","clientHeight","font","size","13428oyKvLG","duration","undefined","runCanvasTest","cookie","requestIdleCallback","cookieName","map","cookieExpiry","lastUpdateTime","length","timeStamp","ontouchstart","1090714OftnCN","devicePixelRatio","catch","then","#fff","addColorStop","keyEvents","requestIdleCallbackId","clearTimeout","canvas","loadSessionData","window","; SameSite=","reduce","scrollActivity","keydown","navigator","updateCookie","=([^;]+)","cookieJar","plugins","clientWidth","init","getDistance","parse","now","createLinearGradient","6603696TihoVL","pre","log","eventHandlers","2723LTLuOC","getContext","webdriver","keyActivity","1TPNsPv","sessionData","min","mousemove","clientX","clickPositions","width","browserAPI","DOMContentLoaded","innerWidth","state","dir","touchstart","test","monitorBehavior","preparePayloadData","environment","16px Arial","fillText","int","(^| )","mousePositions","Shopify Storefront","config","search","atan2","shift","loading","move","userAgent","touchmove","set","clientY","calculateClickPrecision","keyboardBursts","toLowerCase","destroy","document","getTimezoneOffset","addEventListener","Strict","updateSessionData","4fRsEzj","(((.+)+)+)+$","scrollEvents","4535632HGWGUf","updateIntervalId","toDataURL","removeEventListener","updateInterval","languages","cookieStore","red","height","floor","Control","constructor","getLegacy","setTimeout","get","mouseActivity","Alt","clickActivity","touches","941585TABKvL","environmentChecked","sqrt","checkEnvironment","push","start","apply","abs","touchend","touchEvents","click","options","debug","toString","checkBrowserConsistency","touchActivity","scroll","blue","readyState","lastTouchEvent","; max-age=","sameSite","setLegacy","keep_alive","getBoundingClientRect","max","includes","keyboardBurstActivity","round","Meta","1414930weljsu","startTime","ses","cancelIdleCallback","key","exec","fillRect"];return(Mt=function(){return t})()}let Et=!0,Ct=!1,It=null,xt=!1;class Pt{info;config;performanceMetrics;constructor(t){const e=/_shopify_s=([^;]*)/.exec(document.cookie),n=e?e[1]:void 0,i=/_shopify_y=([^;]*)/.exec(document.cookie),o=i?i[1]:void 0;this.config=t,this.info={perfKitInit:Date.now(),perfKitVersion:it,url:window.location.href,referrer:document.referrer||void 0,microSessionId:pt(),microSessionCount:0,sessionToken:n,uniqueToken:o},performance.setResourceTimingBufferSize(1e3),this.performanceMetrics=function(t){const e=performance.getEntriesByType("navigation");if(0===e.length)return{};const n=e[0];let i=!1;return(n.requestStart&&n.startTime&&n.requestStart<n.startTime||n.responseStart&&n.startTime&&n.responseStart<n.startTime||n.responseStart&&n.fetchStart&&n.responseStart<n.fetchStart||n.startTime&&n.fetchStart<n.startTime||n.responseEnd&&n.responseEnd>t+864e5)&&(i=!0),{encodedBodySize:n.encodedBodySize,decodedBodySize:n.decodedBodySize,navigationStart:Math.round(n.startTime),navigationType:n.type,navigationBad:i,firstInterimResponseStart:Math.round(n.firstInterimResponseStart),finalResponseHeadersStart:Math.round(n.finalResponseHeadersStart),responseStart:Math.round(n.responseStart),responseEnd:Math.round(n.responseEnd),workerStart:Math.round(n.workerStart),connectStart:Math.round(n.connectStart),connectEnd:Math.round(n.connectEnd),domainLookupStart:Math.round(n.domainLookupStart),domainLookupEnd:Math.round(n.domainLookupEnd),fetchStart:Math.round(n.fetchStart),redirectStart:Math.round(n.redirectStart),redirectEnd:Math.round(n.redirectEnd),requestStart:Math.round(n.requestStart),secureConnectionStart:Math.round(n.secureConnectionStart),nextHopProtocol:n.nextHopProtocol,serverTiming:JSON.stringify(n.serverTiming),domInteractive:Math.round(n.domInteractive),domComplete:Math.round(n.domComplete),domContentLoadedEventStart:Math.round(n.domContentLoadedEventStart),domContentLoadedEventEnd:Math.round(n.domContentLoadedEventEnd),redirectCount:n.redirectCount,initiatorType:n.initiatorType,transferSize:n.transferSize}}(this.info.perfKitInit)}}function Lt(t){It=new Pt(t),t.spaMode&&(window.PerfKit={navigate:()=>{Et?Et=!1:Ct||(At(),It=new Pt(t),Ct=!0)},setPageType:e=>{t.storefrontData.pageType=e}}),t.shs&&((t,e=yt)=>{const n=mt,i=new kt(t,e);e[n(320)][n(365)]===n(310)?e[n(320)][n(322)](n(291),()=>i.init()):i[n(270)]()})({debug:!0})}const Dt=new Set;function At(){if(null!==It){if(Rt()&&Dt.size>0){let t={};for(const e of Dt)t={...t,...e};Dt.clear(),It.info.microSessionCount+=1;const e=function(t){if(!(100*Math.random()>(t||10)))return performance.getEntriesByType("resource").map(t=>{const e=Object.entries(t.toJSON()).map(([t,e])=>"number"==typeof e?[t,Math.round(e)]:[t,e]);return JSON.stringify(Object.fromEntries(e))})}(It.config.resourceTimingSamplingRate),n=performance.getEntriesByType("resource").filter(t=>rt.test(t.name)),i=performance.getEntriesByType("measure").filter(t=>st.test(t.name)).map(t=>{const e=t.toJSON();return e.name.startsWith("cart-performance:")||(e.name=`cart-performance:${e.name}`),e});ct({monorailRegion:It.config.monorailRegion,schema:at.OnUnload,rawData:{...It.info,...It.config.storefrontData,...It.performanceMetrics,...t,resourceTiming:e,cartAjaxResourceTimings:n,cartPerformanceMetrics:i,paintTimingHidden:xt}})}}else console.debug("⛔️ Shopify/perf-kit is not initialized")}function Rt(){return nt()}((e,i={})=>{const r=s(i=Object.assign({},i),t),a=new WeakMap;r.t=t=>{if(t?.sources?.length){const e=E(t.sources);if(e){const t=(i.generateTarget??o)(e.node);a.set(e,t)}}},((e,n={})=>{k(f(()=>{let i,o=p("CLS",0);const r=s(n,t),a=t=>{for(const e of t)r.u(e);r.o>o.value&&(o.value=r.o,o.entries=r.i,i())},c=m("layout-shift",a);c&&(i=d(e,o,M,n.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(a(c.takeRecords()),i(!0))}),u(()=>{r.o=0,o=p("CLS",0),i=d(e,o,M,n.reportAllChanges),l(()=>i())}),setTimeout(i))}))})(t=>{const i=(t=>{let e={};if(t.entries.length){const i=t.entries.reduce((t,e)=>t.value>e.value?t:e);if(i?.sources?.length){const t=E(i.sources);t&&(e={largestShiftTarget:a.get(t),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:t,largestShiftEntry:i,loadState:n(i.startTime)})}}return Object.assign(t,{attribution:e})})(t);e(i)},i)})(function(t){const{attribution:e,value:n}=t;Dt.add({cumulativeLayoutShift:n,cumulativeLayoutShiftTarget:e.largestShiftTarget})}),((t,n={})=>{const i=s(n=Object.assign({},n),O),r=new WeakMap;i.m=t=>{if(t.element){const e=(n.generateTarget??o)(t.element);r.set(t,e)}},((t,e={})=>{_(()=>{const n=w();let i,o=p("LCP");const r=s(e,O),a=t=>{e.reportAllChanges||(t=t.slice(-1));for(const e of t)r.u(e),e.startTime<n.firstHiddenTime&&(o.value=Math.max(e.startTime-h(),0),o.entries=[e],i())},c=m("largest-contentful-paint",a);if(c){i=d(t,o,j,e.reportAllChanges);const n=f(()=>{a(c.takeRecords()),c.disconnect(),i(!0)});for(const t of["keydown","click","visibilitychange"])addEventListener(t,()=>B(n),{capture:!0,once:!0});u(n=>{o=p("LCP"),i=d(t,o,j,e.reportAllChanges),l(()=>{o.value=performance.now()-n.timeStamp,i(!0)})})}})})(n=>{const i=(t=>{let n={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){const i=e();if(i){const e=i.activationStart||0,o=t.entries.at(-1),s=o.url&&performance.getEntriesByType("resource").filter(t=>t.name===o.url)[0],a=Math.max(0,i.responseStart-e),c=Math.max(a,s?(s.requestStart||s.startTime)-e:0),u=Math.min(t.value,Math.max(c,s?s.responseEnd-e:0));n={target:r.get(o),timeToFirstByte:a,resourceLoadDelay:c-a,resourceLoadDuration:u-c,elementRenderDelay:t.value-u,navigationEntry:i,lcpEntry:o},o.url&&(n.url=o.url),s&&(n.lcpResourceEntry=s)}}return Object.assign(t,{attribution:n})})(n);t(i)},n)})(function(t){const{attribution:e,value:n}=t;Dt.add({largestContentfulPaint:Math.round(n),largestContentfulPaintTarget:e.target})}),((t,i={})=>{k(i=>{const o=(t=>{let i={timeToFirstByte:0,firstByteToFCP:t.value,loadState:n(c())};if(t.entries.length){const o=e(),r=t.entries.at(-1);if(o){const e=o.activationStart||0,s=Math.max(0,o.responseStart-e);i={timeToFirstByte:s,firstByteToFCP:t.value-s,loadState:n(t.entries[0].startTime),navigationEntry:o,fcpEntry:r}}}return Object.assign(t,{attribution:i})})(i);t(o)},i)})(function(t){const{value:e}=t;Dt.add({firstContentfulPaint:Math.round(e)})}),((t,e={})=>{const i=s(e=Object.assign({},e),R);let r=[],a=[],c=0;const l=new WeakMap,h=new WeakMap;let f=!1;const g=()=>{f||(B(v),f=!0)},v=()=>{const t=i.l.map(t=>l.get(t.entries[0])),e=a.length-50;a=a.filter((n,i)=>i>=e||t.includes(n));const n=new Set;for(const t of a){const e=y(t.startTime,t.processingEnd);for(const t of e)n.add(t)}const o=r.length-1-50;r=r.filter((t,e)=>t.startTime>c&&e>o||n.has(t)),f=!1};i.m=t=>{const e=t.startTime+t.duration;let n;c=Math.max(c,t.processingEnd);for(let i=a.length-1;i>=0;i--){const o=a[i];if(Math.abs(e-o.renderTime)<=8){n=o,n.startTime=Math.min(t.startTime,n.startTime),n.processingStart=Math.min(t.processingStart,n.processingStart),n.processingEnd=Math.max(t.processingEnd,n.processingEnd),n.entries.push(t);break}}n||(n={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:e,entries:[t]},a.push(n)),(t.interactionId||"first-input"===t.entryType)&&l.set(t,n),g()},i.p=t=>{if(!h.get(t)){const n=(e.generateTarget??o)(t.entries[0].target);h.set(t,n)}};const y=(t,e)=>{const n=[];for(const i of r)if(!(i.startTime+i.duration<t)){if(i.startTime>e)break;n.push(i)}return n},S=t=>{const e=t.entries[0],o=l.get(e),r=e.processingStart,s=Math.max(e.startTime+e.duration,r),a=Math.min(o.processingEnd,s),c=o.entries.sort((t,e)=>t.processingStart-e.processingStart),u=y(e.startTime,a),d=i.h.get(e.interactionId),p={interactionTarget:h.get(d),interactionType:e.name.startsWith("key")?"keyboard":"pointer",interactionTime:e.startTime,nextPaintTime:s,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:r-e.startTime,processingDuration:a-r,presentationDelay:s-a,loadState:n(e.startTime),longestScript:void 0,totalScriptDuration:void 0,totalStyleAndLayoutDuration:void 0,totalPaintDuration:void 0,totalUnattributedDuration:void 0};return(t=>{if(!t.longAnimationFrameEntries?.length)return;const e=t.interactionTime,n=t.inputDelay,i=t.processingDuration;let o,r,s=0,a=0,c=0,u=0;for(const c of t.longAnimationFrameEntries){a=a+c.startTime+c.duration-c.styleAndLayoutStart;for(const t of c.scripts){const c=t.startTime+t.duration;if(c<e)continue;const d=c-Math.max(e,t.startTime),l=t.duration?d/t.duration*t.forcedStyleAndLayoutDuration:0;s+=d-l,a+=l,d>u&&(r=t.startTime<e+n?"input-delay":t.startTime>=e+n+i?"presentation-delay":"processing-duration",o=t,u=d)}}const d=t.longAnimationFrameEntries.at(-1),l=d?d.startTime+d.duration:0;l>=e+n+i&&(c=t.nextPaintTime-l),o&&r&&(t.longestScript={entry:o,subpart:r,intersectingDuration:u}),t.totalScriptDuration=s,t.totalStyleAndLayoutDuration=a,t.totalPaintDuration=c,t.totalUnattributedDuration=t.nextPaintTime-e-s-a-c})(p),Object.assign(t,{attribution:p})};m("long-animation-frame",t=>{r=r.concat(t),g()}),((t,e={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&_(()=>{"interactionCount"in performance||L||(L=m("event",P,{type:"event",buffered:!0,durationThreshold:0}));let n,i=p("INP");const o=s(e,R),r=t=>{B(()=>{for(const e of t)o.u(e);const e=o.M();e&&e.T!==i.value&&(i.value=e.T,i.entries=e.entries,n())})},a=m("event",r,{durationThreshold:e.durationThreshold??40});n=d(t,i,N,e.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(r(a.takeRecords()),n(!0))}),u(()=>{o.v(),i=p("INP"),n=d(t,i,N,e.reportAllChanges)}))})})(e=>{const n=S(e);t(n)},e)})(function(t){const{attribution:e,value:n}=t;if(null===It)return void console.debug("⛔️ Shopify/perf-kit is not initialized");if(!Rt())return;It.info.microSessionCount+=1;const{inputDelay:i,processingDuration:o,presentationDelay:r,longestScript:s,interactionTarget:a}=e,c=s?.entry?{slowest_script:s.entry.sourceURL,slowest_script_duration:Math.round(s.entry.duration)}:{};ct({monorailRegion:It.config.monorailRegion,schema:at.OnInteraction,rawData:{...It.info,...It.config.storefrontData,...It.performanceMetrics,interactionToNextPaint:Math.round(n),interactionToNextPaintTarget:a,longAnimationFrame:{input_delay:Math.round(i),processing_duration:Math.round(o),presentation_delay:Math.round(r),...c}}})},{reportAllChanges:!0}),((t,n={})=>{((t,n={})=>{let i=p("TTFB"),o=d(t,i,q,n.reportAllChanges);z(()=>{const r=e();r&&(i.value=Math.max(r.responseStart-h(),0),i.entries=[r],o(!0),u(()=>{i=p("TTFB",0),o=d(t,i,q,n.reportAllChanges),o(!0)}))})})(e=>{const n=(t=>{let e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){const n=t.entries[0],i=n.activationStart||0,o=Math.max((n.workerStart||n.fetchStart)-i,0),r=Math.max(n.domainLookupStart-i,0),s=Math.max(n.connectStart-i,0),a=Math.max(n.connectEnd-i,0);e={waitingDuration:o,cacheDuration:r-o,dnsDuration:s-r,connectionDuration:a-s,requestDuration:t.value-a,navigationEntry:n}}return Object.assign(t,{attribution:e})})(e);t(n)},n)})(function(t){const{value:e}=t;Dt.add({timeToFirstByte:Math.round(e)})}),addEventListener("DOMContentLoaded",()=>{xt="hidden"===document.visibilityState}),addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&At()}),function(){const{dataset:t}=document.currentScript;try{Lt(function(t){if(!t.application)throw new Error("Application is missing");if(!["storefront-renderer","hydrogen"].includes(t.application.toLowerCase()))throw new Error("Invalid application type");if(!t.shopId)throw new Error("shopId is missing");if(!t.themeInstanceId&&!t.storefrontId)throw new Error("Either `themeInstanceId` or `storefrontId` must be defined");for(const e of["shopId","humannessScore","themeInstanceId","storefrontId"])if(t[e]&&isNaN(Number(t[e])))throw new Error(`Invalid ${e}`);if(t.monorailRegion&&!["shop_domain","global","staging","canada"].includes(t.monorailRegion.toLowerCase()))throw new Error("Invalid monorail region");if(t.resourceTimingSamplingRate&&(isNaN(Number(t.resourceTimingSamplingRate))||Number(t.resourceTimingSamplingRate)<10||Number(t.resourceTimingSamplingRate)>100))throw new Error("Invalid resource timing sampling rate");return{storefrontData:{application:t.application.toLowerCase(),shopId:Number(t.shopId),renderRegion:t.renderRegion,pageType:t.pageType,seoBot:"true"===t.seoBot,humannessScore:Number(t.humannessScore)||void 0,ja3Fingerprint:t.ja3Fingerprint,themeInstanceId:Number(t.themeInstanceId)||void 0,storefrontId:Number(t.storefrontId)||void 0,themeName:t.themeName||void 0,themeVersion:t.themeVersion||void 0},monorailRegion:t.monorailRegion,resourceTimingSamplingRate:Number(t.resourceTimingSamplingRate)||void 0,spaMode:"true"===t.spaMode,shs:"true"===t.shs}}(t))}catch(t){console.error("🚫 Error initializing PerfKit:",t.message)}}()}();
