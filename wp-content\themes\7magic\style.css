/*
Theme Name: 7Magic
Description: A WordPress theme cloned from Ulike website with 1:1 design accuracy. Features modern IPL hair removal device showcase with responsive design and Elementor compatibility.
Author: jiang
Version: 1.0.0
License: GPL v2 or later
Text Domain: 7magic
Tags: e-commerce, responsive, modern, beauty, health, elementor-ready

This theme is designed to replicate the Ulike website design with WordPress functionality.
All product links redirect to Amazon for purchases.
*/

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Saans-Regular', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Import Ulike Original Styles */
@import url('./assets/css/theme.min.css');
@import url('./assets/css/common-ulike.min.css');
@import url('./assets/css/custom_css.css');
@import url('./assets/css/swiper-bundle.min.css');

/* WordPress Specific Styles */
.wp-block-group {
    margin: 0;
}

.alignwide {
    width: 100vw;
    max-width: 100vw;
    margin-left: calc(50% - 50vw);
}

.alignfull {
    width: 100vw;
    max-width: 100vw;
    margin-left: calc(50% - 50vw);
}

/* Header Styles */
.site-header {
    position: relative;
    z-index: 1000;
}

/* Navigation */
.main-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.site-logo img {
    max-height: 50px;
    width: auto;
}

/* Content Area */
.site-content {
    min-height: 70vh;
}

/* Footer */
.site-footer {
    background: #f8f9fa;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-navigation {
        flex-direction: column;
        padding: 1rem;
    }
    
    .site-logo {
        margin-bottom: 1rem;
    }
}

/* Elementor Compatibility */
.elementor-widget-container {
    margin: 0;
}

.elementor-section {
    position: relative;
}

/* Product Showcase Styles */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.product-card {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.product-description {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.amazon-buy-btn {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.amazon-buy-btn:hover {
    background: linear-gradient(135deg, #ff5252, #ff7979);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6rem 2rem;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.learn-more-btn {
    display: inline-block;
    padding: 12px 24px;
    border: 2px solid white;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.learn-more-btn:hover {
    background: white;
    color: #667eea;
}

.hero-image {
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 20px 40px rgba(0,0,0,0.3));
}

/* Features Section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 3rem 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    position: relative;
    margin-bottom: 3rem;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    margin: 1rem auto;
    border-radius: 2px;
}

/* Product Cards Enhanced */
.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

/* Results Section */
.results-section {
    background: #f8f9fa;
}

.result-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
}

.stats-section .stat-item {
    padding: 1rem;
}

.stats-section h3 {
    font-family: 'Saans-Medium', Arial, sans-serif;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Timeline */
.timeline-grid {
    margin-top: 2rem;
}

.timeline-item h4 {
    color: #333;
    margin: 1rem 0 0.5rem;
    font-size: 1.2rem;
}

.timeline-item p {
    color: #666;
    font-size: 0.9rem;
}

/* Technology Section */
.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.tech-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
}

.tech-item h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.tech-item p {
    color: #666;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-features {
        justify-content: center;
    }

    .feature-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .results-grid {
        grid-template-columns: 1fr !important;
    }

    .stats-section div {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 480px) {
    .hero-section {
        padding: 4rem 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .amazon-buy-btn,
    .learn-more-btn {
        width: 100%;
        text-align: center;
        max-width: 280px;
    }
}
