(function(){document.addEventListener("DOMContentLoaded",()=>{let hideTimer=null,locationUrl=`https://account.ulike.com/login?returnUrl=${window.location.href}`;function jumpLoginPage(){console.log("getLoginStatus",getLoginStatus),commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u6D4F\u89C8\u9875\u9762\uFF0C\u672A\u767B\u5F55\u70B9\u51FB","more-btn","no-login"),commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\u66DD\u5149"),$(".login-modal").fadeIn(200),$("body").addClass("no-scroll")}function isLoginPriont(name,type,val){getLoginStatus&&commonGtmEvent(name,type,val)}const onGtmEventList=[];function pblicStaFliter(name,type,val){let sendData={name,type,value:val};onGtmEventList.length==0?(onGtmEventList.push(sendData),commonGtmEvent(name,type,val)):onGtmEventList.some(item=>item.name===name)||(onGtmEventList.push(sendData),commonGtmEvent(name,type,val))}function copyTextToClipboard(copyText){if(!getLoginStatus){jumpLoginPage();return}const textToCopy=copyText;textToCopy&&navigator.clipboard.writeText(textToCopy).then(()=>{clearTimeout(hideTimer),$("#referfriend-invitation-content .modal-copysuccess").show(),hideTimer=setTimeout(()=>{$(".modal-copysuccess").fadeOut(300)},3e3)}).catch(err=>console.error("copyerr:",err))}const showError=error=>{clearTimeout(hideTimer),$(".dlio-error").text("error:"+error),$(".dlio-error").show(),hideTimer=setTimeout(()=>{$("#referfriend-invitation-content .dlio-error").fadeOut(300)},3e3)};$(".gameplay-box-copycode .copy-wrap").click(function(){const copyText=$(this).closest(".copy-code-top").find(".code-top").text();copyTextToClipboard(copyText),isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u590D\u5236\u9080\u8BF7\u7801","copy-code",""),isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u590D\u5236\u9080\u8BF7\u7801+\u590D\u5236\u94FE\u63A5","1","")}),$(".gameplay-box-copycode .invitation-product__copy-btn").click(function(){if(!getLoginStatus){jumpLoginPage();return}const url=$(this).data("url");copyTextFallback(url),$("#referfriend-invitation-content .modal-copysuccess").show(),setTimeout(()=>{$("#referfriend-invitation-content .modal-copysuccess").fadeOut(300)},3e3);var index=$(this).closest(".gameplay-box-item").index();switch(index){case 1:isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u70B9\u51FBa10","copy-url","");break;case 2:isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u70B9\u51FBA3","copy-url","");break;case 3:isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u70B9\u51FB\u9762\u7F69","copy-url","");break}isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u590D\u5236\u9080\u8BF7\u7801+\u590D\u5236\u94FE\u63A5","2","")});function bindClickEvent(selector,handler){$(selector).click(handler)}bindClickEvent(".withdraw-btn",event=>{if(!getLoginStatus){jumpLoginPage();return}event.stopPropagation(),$(".tabs").is(":visible")?isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u4F63\u91D1\u9875\uFF0C\u70B9\u51FB\u63D0\u73B0","withdraw-btn",""):isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u6211\u7684\u5956\u52B1\uFF0C\u70B9\u51FB\u63D0\u73B0","withdraw-btn","");let emailVal=$(".withdraw .ped-price-total").text().replace("$","");Number(emailVal)>0?$(".modal-mask,.modal-container").fadeIn(200):showError("The current amount does not support withdrawal")});function closeDeliBox(){$(".modal-mask, .modal-container").fadeOut(200),$(".modal-body").fadeOut(200)}bindClickEvent(".modal-close",()=>{$(".modal-body").is(":visible")&&renderWithdrawRecords(),closeDeliBox()});let isCooldown=!1;function startCountdown($button,duration){if(isCooldown)return;let timeLeft=duration;isCooldown=!0,$button.prop("disabled",!0);const countdownTimer=setInterval(()=>{$button.text(`sent\uFF08${timeLeft}\uFF09`),timeLeft--<=0&&(clearInterval(countdownTimer),isCooldown=!1,$button.text("Get code").prop("disabled",!1))},1e3)}$("#referfriend-invitation-content .getcode").click(function(){if($(".email-error").is(":visible"))return;const emailVal=$(".withdrawal-email").val().trim();sendUlikeApi("/user/sendCode",{userEmail:emailVal,since:"USER_WITHDRAW"}).then(()=>{startCountdown($(this),60)}).catch(error=>{console.log("error",error),showError(error)})});function validateEmail(email){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)}function toggleValidationState(selector,isValid){$(selector).toggleClass("valid",isValid).toggleClass("invalid",!isValid)}function validCodeFun(){const emailVal=$(".withdrawal-email").val().trim(),getcode=$(".withdrawal-code").val().trim(),isValid=getcode!==""&&getcode.length===6;toggleValidationState(".withdrawal-code",isValid),isValid&&sendUlikeApi("/user/me/withdraw",{withdrawAccountType:"PAYPAL_EMAIL",withdrawAccountId:emailVal,verifyCode:getcode}).then(res=>{res.message=="user_sys_verify_code_invalid"&&(toggleValidationState(".withdrawal-code",!1),$("#referfriend-invitation-content .code-error").show()),res.success&&($(".modal-header").hide(),$(".modal-container").css("height","344px"),$(".modal-body").fadeIn(200),$("#referfriend-invitation-content .withdraw .ped-price-total").text("$0"),isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u63D0\u73B0\u9875\uFF0C\u8FDB\u5165\u63D0\u73B0\u6210\u529F\u9875","info-successPage",""))}).catch(error=>{console.log("error",error)})}const showDatilPage=()=>{if(!getLoginStatus){jumpLoginPage();return}$("#refer-friend-banner").hide(),$(".rewards-more, .gameplay-box-copycode, .gameplay-box-top, .invitation-record-batch .title").hide(),$("#shopify-section-template--19132149956849__30698451-389d-43ce-b548-827c55ad66c4").hide(),$(".tabs-title, .tabs").show(),$("#referfriend-invitation-content .is-normal-render").show(),$("#referfriend-invitation-content .invitation-record-batch").show(),$(".refer-friend-steps").hide(),$(".referfriend-faq").hide(),$(".u-referral-storise").hide()};function emailErrorShowOrHide(val){val?($("#referfriend-invitation-content .email-default").show(),$("#referfriend-invitation-content .email-error").hide()):($("#referfriend-invitation-content .email-default").hide(),$("#referfriend-invitation-content .email-error").show())}$(".withdrawal-email").on("input",function(){const emailVal=$(".withdrawal-email").val().trim(),isValidEmail=emailVal!==""&&validateEmail(emailVal);toggleValidationState(".withdrawal-email",isValidEmail),emailErrorShowOrHide(isValidEmail),pblicStaFliter("\u8001\u5E26\u65B0\uFF0C\u63D0\u73B0\u9875\uFF0C\u8F93\u5165\u90AE\u7BB1","input-email","")}),$(".withdrawal-code").on("input",function(){pblicStaFliter("\u8001\u5E26\u65B0\uFF0C\u63D0\u73B0\u9875\uFF0C\u8F93\u5165\u9A8C\u8BC1\u7801","input-code","")}),$("#referfriend-invitation-content .submit-btn").click(()=>{const emailVal=$(".withdrawal-email").val().trim(),isValidEmail=emailVal!==""&&validateEmail(emailVal);toggleValidationState(".withdrawal-email",isValidEmail),isValidEmail&&validCodeFun()}),$("#referfriend-invitation-content .success-btn").click(()=>{closeDeliBox(),showDatilPage(),renderWithdrawRecords()}),$("#referfriend-invitation-content .price-area,.rewards-more").click(event=>{event.target.classList.contains("withdraw-btn")||showDatilPage(),isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u6211\u7684\u5956\u52B1\uFF0C\u70B9\u51FB\u66F4\u591A","more-btn","")});async function renderOrderData(){const inviteList=await sendUlikeApi("/promotion/queryBenefitInfoList",{activityId,activityType:"INVITE_NEW_ACTIVITY",includeCount:!0,pageIndex:1,pageSize:20,since:"INVITE_HOME"}),$list=$("#invitation-record-list").empty();if(!inviteList.data||inviteList.data.length==0){$list.append(`
                <p class="no-record">
                    There is no record for the moment.
                </p>`),$(".tabs").is(":visible")||$("#referfriend-invitation-content .invitation-record-batch").hide();return}inviteList.data.forEach((order,index)=>{const extraClass=index>0?"is-normal-render":"";$list.append(`
                <div class="render-area ${extraClass}">
                    <div class="description-text">
                        <div class="description-title">
                            <p class="order-num">Orders</p><p>Settlement time</p><p>Purchase Time</p><p>Subscribers</p><p>Order Status</p>
                        </div>
                        <div class="description-data">
                            <p class="order-num">${order.extend.orderInfo.orderName}</p><p>${timeToYear(order.endTime)}</p><p>${timeToYear(order.extend.orderInfo.createTime)}</p>
                            <p>${order.userEmail}</p><p class="order-status-red">${invivtStatusText(order.benefitStatus)}</p>
                        </div>
                    </div>
                    <div class="description-product">
                        <div class="description-title"><img src="${order.extend.orderInfo.orderItem[0].variant.image.url}" alt="Ulike Air 10" width="100%" height="100%"></div>
                        <div class="description-data">
                            <p class="order-num">${order.extend.orderInfo.orderItem[0].title}</p>
                            <div class="price-detail">
                                <div class="order-amount"><span>Order Amount<a class="show-symbol">:</a></span><br class="hidden-br"/><span class="order-status-red">${order.extend.orderInfo.totalPrice}</span></div>
                                <div class="order-commission"><span>Commission<a class="show-symbol">:</a></span><br class="hidden-br"/><span class="order-status-red">${order.extend.commissionAmount}</span></div>
                            </div>
                        </div>
                    </div>
                </div>`),$("#referfriend-invitation-content .is-normal-render").hide()})}const invivtStatusText=benefitStatus=>{switch(benefitStatus){case"SUCCESS":return"Commission settled";case"PENDING":return"Payment successful, commission to be settled";default:return"Order refund, invalid"}},WithdStatusText=benefitStatus=>{switch(benefitStatus){case"SUCCESS":return"Withdrawal successful";case"PENDING":return"Withdrawing";default:return"Withdrawal failed"}},timeToYear=val=>{const timestamp=val,date=new Date(timestamp*1e3),year=date.getFullYear(),month=String(date.getMonth()+1).padStart(2,"0"),day=String(date.getDate()).padStart(2,"0"),hours=String(date.getHours()).padStart(2,"0"),minutes=String(date.getMinutes()).padStart(2,"0"),seconds=String(date.getSeconds()).padStart(2,"0");return`${year}-${month}-${day} ${hours}:${minutes}`};async function renderWithdrawRecords(){const withdrawalRecord=await sendUlikeApi("/user/queryFundFlowList",{scene:"INVITE_FUND_FLOW",pageIndex:1,pageSize:20,includeCount:!1,flowTypeList:["WITHDRAW_SUBMITTED","INVITE_REWARD_RECEIVED"]}),$list=$(".amount-record-list").empty();if($(".amount-record-list").show(),!withdrawalRecord.data||withdrawalRecord.data.length==0){$list.append(`
                <p class="no-record">
                    There is no record for the moment.
                </p>`);return}let withdrawType="",withdrawAmount="",withdrawStatus="",extraClass="";$("#referfriend-invitation-content .table-header").show(),withdrawalRecord.data.forEach(item=>{item.flowType=="WITHDRAW_SUBMITTED"&&(withdrawType="Withdraw, account:"+item.extend.userFundWithdrawDto.withdrawAccountId,withdrawStatus=WithdStatusText(item.extend.userFundWithdrawDto.withdrawStatus),item.extend.userFundWithdrawDto.withdrawStatus!="FAILED"?withdrawAmount=item.amount:(withdrawAmount="",extraClass="withdraw-error")),item.flowType=="INVITE_REWARD_RECEIVED"&&(withdrawType="Commission settlement",withdrawAmount="+"+Math.abs(item.amount),withdrawStatus=""),$list.append(`
                <div class="table-content">
                    <div class="base-information"><p>${withdrawType}</p><p>${timeToYear(item.createTime)}</p></div>
                    <div class="base-price"><p>${withdrawAmount}</p><p id="${extraClass}">${withdrawStatus}</p></div>
                </div>`)})}const activityId="131861891793289217";initializeUserAccount().then(({user,fund,activity})=>{if(console.log("\u521D\u59CB\u5316\u5B8C\u6210\uFF0C\u7528\u6237:",user,"\u8D44\u91D1\u8D26\u6237:",fund,"\u6D3B\u52A8:",activity),activity){let inviteCouponCodeList=activity.data.extend.inviteCouponCodeList||[];$(".gameplay-box-item").each((index,item)=>{const productId=$(item).attr("data-productId"),productUrl=$(item).attr("data-url"),productVariant=$(item).attr("data-variant"),productData=inviteCouponCodeList.find(fitem=>fitem.productIdList.includes(productId));productData&&($(item).find(".code-disscount span").text("$"+productData.discountAmount),$(item).find(".code-top").text(productData.couponCode),$(item).find(".invitation-product__copy-btn").attr("data-url",productVariant?`${productUrl}?add-discount=${productData.couponCode}&variant=${productVariant}`:`${productUrl}?add-discount=${productData.couponCode}`),$(item).find(".code-disscount").show())})}fund&&($("#referfriend-invitation-content .modal-price").text("$"+fund.data.availableAmount),$("#referfriend-invitation-content .pending .ped-price-total").text("$"+fund.data.freezeAmount),$("#referfriend-invitation-content .withdraw .ped-price-total").text("$"+fund.data.availableAmount))}).catch(err=>{console.error(err)});async function initializeUserAccount(){try{const user=await sendUlikeApi("/user/queryLoginUserAccount",{});if(user?.data?.userId)getLoginStatus=!0,pblicStaFliter("\u8001\u5E26\u65B0\uFF0C\u6D4F\u89C8\u9875\u9762\uFF08\u5DF2\u767B\u5F55\uFF09",getLoginStatus,"");else return $(".invitation-record-batch").hide(),pblicStaFliter("\u8001\u5E26\u65B0\uFF0C\u6D4F\u89C8\u9875\u9762\uFF08\u672A\u767B\u5F55\uFF09",getLoginStatus,""),{user};const fund=await sendUlikeApi("/user/me/queryOrCreateFundAccount",{accountType:"CASH"}),activity=await sendUlikeApi("/promotion/queryOrAddActivityItem",{activityType:"INVITE_NEW_ACTIVITY",activityId});return renderOrderData(),{user,fund,activity}}catch(err){throw console.error(err),err}}const tabs=document.querySelectorAll(".tab"),tabContents=document.querySelectorAll(".tab-content");tabs[0].classList.add("active"),tabContents[0].classList.add("active"),tabs.forEach(tab=>{tab.addEventListener("click",()=>{tabs.forEach(tab2=>tab2.classList.remove("active")),tabContents.forEach(content=>content.classList.remove("active")),tab.classList.add("active");const tabId=tab.getAttribute("data-tab");document.getElementById("tab-content-"+tabId).classList.add("active"),tabId=="1"&&(isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u4F63\u91D1\u9875\uFF0C\u70B9\u51FB\u9080\u8BF7\u8BB0\u5F55","click-record",""),renderOrderData()),tabId=="2"&&(isLoginPriont("\u8001\u5E26\u65B0\uFF0C\u4F63\u91D1\u9875\uFF0C\u70B9\u51FB\u4F63\u91D1\u660E\u7EC6","click-pricedetail",""),$("#referfriend-invitation-content .table-header").hide(),$(".amount-record-list").hide(),renderWithdrawRecords())})}),$("#loginForm .login-email").blur(function(){const email=$(this).val().trim();email===""&&(commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A"),$(".form-group").eq(0).addClass("error"),$("#emailError").text("Email cannot be empty")),!validateEmail(email)&&email!==""&&(commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u90AE\u7BB1\u9519\u8BEF"),$(".form-group").eq(0).addClass("error"),$("#emailError").text("Invalid email address"))}),$("#loginForm .login-email").one("input",function(){commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u8F93\u5165\u90AE\u7BB1")}),$("#loginForm .login-password").one("input",function(){commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u8F93\u5165\u5BC6\u7801")}),$("#loginForm .login-password").blur(function(){const password=$(this).val().trim();isValidPassword(password)||(password===""?(commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A"),$("#passwordError").text("Please enter the correct password")):(commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u5BC6\u7801\u683C\u5F0F\u9519\u8BEF"),$("#passwordError").text("Your password requires 6-16 digits, and must contain letters and numbers")),$(".form-group").eq(1).addClass("error"))}),$("#loginForm .login-email,#loginForm .login-password").on("input",function(){$(this).closest(".form-group").removeClass("error").find(".error").text("")});let loading=!1;$("#loginForm").on("submit",function(e){e.preventDefault();const email=$(".login-email").val().trim(),password=$(".login-password").val().trim();getValidResult(email,password)&&login(email,password)}),$(".login-modal-close").click(function(){loginModalClose()});function loginModalClose(){$(".login-modal").fadeOut(200),$("body").removeClass("no-scroll"),$(".form-group").each(function(index,item){$(item).removeClass("error").find(".error").text(""),$(item).find("input").val("")})}function isValidPassword(password){return/^(?=.*[A-Za-z])(?=.*\d).{6,16}$/.test(password)}function getValidResult(email,password){let valid=!0;return email===""&&($(".form-group").eq(0).addClass("error"),$("#emailError").text("Email cannot be empty"),valid=!1),!validateEmail(email)&&email!==""&&($(".form-group").eq(0).addClass("error"),$("#emailError").text("Invalid email address"),valid=!1),isValidPassword(password)||($(".form-group").eq(1).addClass("error"),$("#passwordError").text("Your password requires 6-16 digits, and must contain letters and numbers"),valid=!1),valid}function login(email,password){if(loading)return;const params={siteCode:"US",userEmail:email,passWord:password,since:"LOTTERY_LOGIN",returnUrl:location.href};commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u767B\u5F55\uFF0C\u70B9\u51FB\u63D0\u4EA4"),showLoading(),sendUlikeApi("/user/login",params).then(res=>{const{code,data}=res;code===0?location.href=data.loginUrl:($(".form-group").eq(1).addClass("error"),$("#passwordError").text("Please enter the correct password"),hideLoading())}).catch(err=>{console.log("\u767B\u5F55\u5931\u8D25",err),hideLoading()})}function showLoading(){loading=!0,$(".form-submit").find("span").hide(),$(".form-submit").find(".loading-icon").show(),$(".form-submit").addClass("disabled")}function hideLoading(){loading=!1,$(".form-submit").find("span").show(),$(".form-submit").find(".loading-icon").hide(),$(".form-submit").removeClass("disabled")}window.closeAll=()=>$("#referfriend-invitation-content").hide()})})();
//# sourceMappingURL=/cdn/shop/t/56/assets/refer-friend-main.js.map?v=127026548156046148771751507476
