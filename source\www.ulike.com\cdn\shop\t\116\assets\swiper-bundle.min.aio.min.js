/*
Generated time: January 19, 2023 03:24
This file was created by the app developer. Feel free to contact the original developer with any questions. It was minified (compressed) by AVADA. AVADA do NOT own this script.
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Swiper=t()}(this,function(){"use strict";function s(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function i(t,a){void 0===t&&(t={}),void 0===a&&(a={}),Object.keys(a).forEach(e=>{void 0===t[e]?t[e]=a[e]:s(a[e])&&s(t[e])&&0<Object.keys(a[e]).length&&i(t[e],a[e])})}const t={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function T(){var e="undefined"!=typeof document?document:{};return i(e,t),e}const G={document:t,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function O(){var e="undefined"!=typeof window?window:{};return i(e,G),e}class l extends Array{constructor(e){if("number"==typeof e)super(e);else{super(...e||[]);{e=this;const t=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=>t,set(e){t.__proto__=e}})}}}}function r(e){const t=[];return(e=void 0===e?[]:e).forEach(e=>{Array.isArray(e)?t.push(...r(e)):t.push(e)}),t}function n(e,t){return Array.prototype.filter.call(e,t)}function L(e,s){const t=O(),i=T();let a=[];if(!s&&e instanceof l)return e;if(!e)return new l(a);if("string"==typeof e){const t=e.trim();if(0<=t.indexOf("<")&&0<=t.indexOf(">")){let e="div";0===t.indexOf("<li")&&(e="ul"),0===t.indexOf("<tr")&&(e="tbody"),0!==t.indexOf("<td")&&0!==t.indexOf("<th")||(e="tr"),0===t.indexOf("<tbody")&&(e="table"),0===t.indexOf("<option")&&(e="select");const s=i.createElement(e);s.innerHTML=t;for(let e=0;e<s.childNodes.length;e+=1)a.push(s.childNodes[e])}else a=function(e){if("string"!=typeof e)return[e];var t=[],a=(s||i).querySelectorAll(e);for(let e=0;e<a.length;e+=1)t.push(a[e]);return t}(e.trim())}else if(e.nodeType||e===t||e===i)a.push(e);else if(Array.isArray(e)){if(e instanceof l)return e;a=e}return new l(function(t){var a=[];for(let e=0;e<t.length;e+=1)-1===a.indexOf(t[e])&&a.push(t[e]);return a}(a))}L.fn=l.prototype;const a={addClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];const s=r(t.map(e=>e.split(" ")));return this.forEach(e=>{e.classList.add(...s)}),this},removeClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];const s=r(t.map(e=>e.split(" ")));return this.forEach(e=>{e.classList.remove(...s)}),this},hasClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];const s=r(t.map(e=>e.split(" ")));return 0<n(this,t=>0<s.filter(e=>t.classList.contains(e)).length).length},toggleClass:function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];const s=r(t.map(e=>e.split(" ")));this.forEach(t=>{s.forEach(e=>{t.classList.toggle(e)})})},attr:function(t,a){if(1===arguments.length&&"string"==typeof t)return this[0]?this[0].getAttribute(t):void 0;for(let e=0;e<this.length;e+=1)if(2===arguments.length)this[e].setAttribute(t,a);else for(const a in t)this[e][a]=t[a],this[e].setAttribute(a,t[a]);return this},removeAttr:function(t){for(let e=0;e<this.length;e+=1)this[e].removeAttribute(t);return this},transform:function(t){for(let e=0;e<this.length;e+=1)this[e].style.transform=t;return this},transition:function(t){for(let e=0;e<this.length;e+=1)this[e].style.transitionDuration="string"!=typeof t?t+"ms":t;return this},on:function(){for(var t=arguments.length,a=new Array(t),e=0;e<t;e++)a[e]=arguments[e];let[s,i,r,l]=a;function n(t){var e=t.target;if(e){var a=t.target.dom7EventData||[];if(a.indexOf(t)<0&&a.unshift(t),L(e).is(i))r.apply(e,a);else{const t=L(e).parents();for(let e=0;e<t.length;e+=1)L(t[e]).is(i)&&r.apply(t[e],a)}}}function o(e){var t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),r.apply(this,t)}"function"==typeof a[1]&&([s,r,l]=a,i=void 0),l=l||!1;var d=s.split(" ");let p;for(let e=0;e<this.length;e+=1){const a=this[e];if(i)for(p=0;p<d.length;p+=1){const t=d[p];a.dom7LiveListeners||(a.dom7LiveListeners={}),a.dom7LiveListeners[t]||(a.dom7LiveListeners[t]=[]),a.dom7LiveListeners[t].push({listener:r,proxyListener:n}),a.addEventListener(t,n,l)}else for(p=0;p<d.length;p+=1){const t=d[p];a.dom7Listeners||(a.dom7Listeners={}),a.dom7Listeners[t]||(a.dom7Listeners[t]=[]),a.dom7Listeners[t].push({listener:r,proxyListener:o}),a.addEventListener(t,o,l)}}return this},off:function(){for(var e=arguments.length,a=new Array(e),s=0;s<e;s++)a[s]=arguments[s];let[t,i,r,l]=a;"function"==typeof a[1]&&([t,r,l]=a,i=void 0),l=l||!1;var n=t.split(" ");for(let e=0;e<n.length;e+=1){const a=n[e];for(let e=0;e<this.length;e+=1){const s=this[e];let t;if(!i&&s.dom7Listeners?t=s.dom7Listeners[a]:i&&s.dom7LiveListeners&&(t=s.dom7LiveListeners[a]),t&&t.length)for(let e=t.length-1;0<=e;--e){const i=t[e];(r&&i.listener===r||r&&i.listener&&i.listener.dom7proxy&&i.listener.dom7proxy===r||!r)&&(s.removeEventListener(a,i.proxyListener,l),t.splice(e,1))}}}return this},trigger:function(){for(var t=O(),a=arguments.length,s=new Array(a),i=0;i<a;i++)s[i]=arguments[i];const r=s[0].split(" "),l=s[1];for(let e=0;e<r.length;e+=1){const i=r[e];for(let e=0;e<this.length;e+=1){const r=this[e];if(t.CustomEvent){const a=new t.CustomEvent(i,{detail:l,bubbles:!0,cancelable:!0});r.dom7EventData=s.filter((e,t)=>0<t),r.dispatchEvent(a),r.dom7EventData=[],delete r.dom7EventData}}}return this},transitionEnd:function(a){const s=this;return a&&s.on("transitionend",function e(t){t.target===this&&(a.call(this,t),s.off("transitionend",e))}),this},outerWidth:function(e){if(0<this.length){if(e){const e=this.styles();return this[0].offsetWidth+parseFloat(e.getPropertyValue("margin-right"))+parseFloat(e.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(0<this.length){if(e){const e=this.styles();return this[0].offsetHeight+parseFloat(e.getPropertyValue("margin-top"))+parseFloat(e.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){var e=O();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){var e,t,a,s,i,r;return 0<this.length?(r=O(),s=T(),t=(e=this[0]).getBoundingClientRect(),s=s.body,a=e.clientTop||s.clientTop||0,s=e.clientLeft||s.clientLeft||0,i=e===r?r.scrollY:e.scrollTop,r=e===r?r.scrollX:e.scrollLeft,{top:t.top+i-a,left:t.left+r-s}):null},css:function(e,t){var a=O();let s;if(1===arguments.length){if("string"!=typeof e){for(s=0;s<this.length;s+=1)for(const t in e)this[s].style[t]=e[t];return this}if(this[0])return a.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e)for(s=0;s<this.length;s+=1)this[s].style[e]=t;return this},each:function(a){return a&&this.forEach((e,t)=>{a.apply(e,[e,t])}),this},html:function(t){if(void 0===t)return this[0]?this[0].innerHTML:null;for(let e=0;e<this.length;e+=1)this[e].innerHTML=t;return this},text:function(t){if(void 0===t)return this[0]?this[0].textContent.trim():null;for(let e=0;e<this.length;e+=1)this[e].textContent=t;return this},is:function(e){var t=O(),a=T(),s=this[0];let i,r;if(s&&void 0!==e)if("string"==typeof e){if(s.matches)return s.matches(e);if(s.webkitMatchesSelector)return s.webkitMatchesSelector(e);if(s.msMatchesSelector)return s.msMatchesSelector(e);for(i=L(e),r=0;r<i.length;r+=1)if(i[r]===s)return!0}else{if(e===a)return s===a;if(e===t)return s===t;if(e.nodeType||e instanceof l)for(i=e.nodeType?[e]:e,r=0;r<i.length;r+=1)if(i[r]===s)return!0}return!1},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){var t;return void 0===e?this:L((t=this.length)-1<e?[]:e<0?(t=t+e)<0?[]:[this[t]]:[this[e]])},append:function(){var a,s=T();for(let e=0;e<arguments.length;e+=1){a=e<0||arguments.length<=e?void 0:arguments[e];for(let t=0;t<this.length;t+=1)if("string"==typeof a){const T=s.createElement("div");for(T.innerHTML=a;T.firstChild;)this[t].appendChild(T.firstChild)}else if(a instanceof l)for(let e=0;e<a.length;e+=1)this[t].appendChild(a[e]);else this[t].appendChild(a)}return this},prepend:function(e){var t=T();let a,s;for(a=0;a<this.length;a+=1)if("string"==typeof e){const T=t.createElement("div");for(T.innerHTML=e,s=T.childNodes.length-1;0<=s;--s)this[a].insertBefore(T.childNodes[s],this[a].childNodes[0])}else if(e instanceof l)for(s=0;s<e.length;s+=1)this[a].insertBefore(e[s],this[a].childNodes[0]);else this[a].insertBefore(e,this[a].childNodes[0]);return this},next:function(e){return 0<this.length?e?this[0].nextElementSibling&&L(this[0].nextElementSibling).is(e)?L([this[0].nextElementSibling]):L([]):this[0].nextElementSibling?L([this[0].nextElementSibling]):L([]):L([])},nextAll:function(e){var t=[];let a=this[0];if(!a)return L([]);for(;a.nextElementSibling;){var s=a.nextElementSibling;e&&!L(s).is(e)||t.push(s),a=s}return L(t)},prev:function(e){var t;return 0<this.length?(t=this[0],e?t.previousElementSibling&&L(t.previousElementSibling).is(e)?L([t.previousElementSibling]):L([]):t.previousElementSibling?L([t.previousElementSibling]):L([])):L([])},prevAll:function(e){var t=[];let a=this[0];if(!a)return L([]);for(;a.previousElementSibling;){var s=a.previousElementSibling;e&&!L(s).is(e)||t.push(s),a=s}return L(t)},parent:function(t){var a=[];for(let e=0;e<this.length;e+=1)null===this[e].parentNode||t&&!L(this[e].parentNode).is(t)||a.push(this[e].parentNode);return L(a)},parents:function(a){var s=[];for(let t=0;t<this.length;t+=1){let e=this[t].parentNode;for(;e;)a&&!L(e).is(a)||s.push(e),e=e.parentNode}return L(s)},closest:function(e){let t=this;return void 0===e?L([]):t=t.is(e)?t:t.parents(e).eq(0)},find:function(t){var a=[];for(let e=0;e<this.length;e+=1){var s=this[e].querySelectorAll(t);for(let e=0;e<s.length;e+=1)a.push(s[e])}return L(a)},children:function(t){var a=[];for(let e=0;e<this.length;e+=1){var s=this[e].children;for(let e=0;e<s.length;e+=1)t&&!L(s[e]).is(t)||a.push(s[e])}return L(a)},filter:function(e){return L(n(this,e))},remove:function(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};function S(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function g(){return Date.now()}function I(e,t){void 0===t&&(t="x");var a=O();let s,i,r;e=function(e){var t=O();let a;return a=(a=!(a=t.getComputedStyle?t.getComputedStyle(e,null):a)&&e.currentStyle?e.currentStyle:a)||e.style}(e);return a.WebKitCSSMatrix?(6<(i=e.transform||e.webkitTransform).split(",").length&&(i=i.split(", ").map(e=>e.replace(",",".")).join(", ")),r=new a.WebKitCSSMatrix("none"===i?"":i)):(r=e.MozTransform||e.OTransform||e.MsTransform||e.msTransform||e.transform||e.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),"x"===t&&(i=a.WebKitCSSMatrix?r.m41:16===s.length?parseFloat(s[12]):parseFloat(s[4])),(i="y"===t?a.WebKitCSSMatrix?r.m42:16===s.length?parseFloat(s[13]):parseFloat(s[5]):i)||0}function d(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function m(e){const a=Object(arguments.length<=0?void 0:e),t=["__proto__","constructor","prototype"];for(let e=1;e<arguments.length;e+=1){var s=e<0||arguments.length<=e?void 0:arguments[e];if(null!=s&&(n=s,!("undefined"!=typeof window&&void 0!==window.HTMLElement?n instanceof HTMLElement:n&&(1===n.nodeType||11===n.nodeType)))){var i=Object.keys(Object(s)).filter(e=>t.indexOf(e)<0);for(let e=0,t=i.length;e<t;e+=1){var r=i[e],l=Object.getOwnPropertyDescriptor(s,r);void 0!==l&&l.enumerable&&(d(a[r])&&d(s[r])?s[r].__swiper__?a[r]=s[r]:m(a[r],s[r]):d(a[r])||!d(s[r])||(a[r]={},s[r].__swiper__)?a[r]=s[r]:m(a[r],s[r]))}}}var n;return a}function M(e,t,a){e.style.setProperty(t,a)}function w(e){let{swiper:a,targetPosition:s,side:i}=e;const r=O(),l=-a.translate;let n,o=null;const d=a.params.speed,p=(a.wrapperEl.style.scrollSnapType="none",r.cancelAnimationFrame(a.cssModeFrameID),s>l?"next":"prev"),c=(e,t)=>"next"===p&&t<=e||"prev"===p&&e<=t,u=()=>{n=(new Date).getTime(),null===o&&(o=n);var e=Math.max(Math.min((n-o)/d,1),0),e=.5-Math.cos(e*Math.PI)/2;let t=l+e*(s-l);c(t,s)&&(t=s),a.wrapperEl.scrollTo({[i]:t}),c(t,s)?(a.wrapperEl.style.overflow="hidden",a.wrapperEl.style.scrollSnapType="",setTimeout(()=>{a.wrapperEl.style.overflow="",a.wrapperEl.scrollTo({[i]:t})}),r.cancelAnimationFrame(a.cssModeFrameID)):a.cssModeFrameID=r.requestAnimationFrame(u)};u()}let e,p,o;function u(){return e=e||function(){const a=O(),e=T();return{smoothScroll:e.documentElement&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in a||a.DocumentTouch&&e instanceof a.DocumentTouch),passiveListener:function(){let e=!1;try{var t=Object.defineProperty({},"passive",{get(){e=!0}});a.addEventListener("testPassiveListener",null,t)}catch(e){}return e}(),gestures:"ongesturestart"in a}}()}function N(){return o=o||function(){const e=O();return{isSafari:0<=(t=e.navigator.userAgent.toLowerCase()).indexOf("safari")&&t.indexOf("chrome")<0&&t.indexOf("android")<0,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)};var t}()}function c(e){var{swiper:e,runCallbacks:t,direction:a,step:s}=e,{activeIndex:i,previousIndex:r}=e;let l=a;if(l=l||(r<i?"next":i<r?"prev":"reset"),e.emit("transition"+s),t&&i!==r){if("reset"===l)return e.emit("slideResetTransition"+s);e.emit("slideChangeTransition"+s),"next"===l?e.emit("slideNextTransition"+s):e.emit("slidePrevTransition"+s)}}function h(){var e,t,a=this,{params:s,el:i}=a;i&&0===i.offsetWidth||(s.breakpoints&&a.setBreakpoint(),{allowSlideNext:i,allowSlidePrev:e,snapGrid:t}=a,a.allowSlideNext=!0,a.allowSlidePrev=!0,a.updateSize(),a.updateSlides(),a.updateSlidesClasses(),("auto"===s.slidesPerView||1<s.slidesPerView)&&a.isEnd&&!a.isBeginning&&!a.params.centeredSlides?a.slideTo(a.slides.length-1,0,!1,!0):a.slideTo(a.activeIndex,0,!1,!0),a.autoplay&&a.autoplay.running&&a.autoplay.paused&&a.autoplay.run(),a.allowSlidePrev=e,a.allowSlideNext=i,a.params.watchOverflow&&t!==a.snapGrid&&a.checkOverflow())}Object.keys(a).forEach(e=>{Object.defineProperty(L.fn,e,{value:a[e],writable:!0})});let f=!1;function B(){}const v=(e,t)=>{var a=T(),{params:s,touchEvents:i,el:r,wrapperEl:l,device:n,support:o}=e,d=!!s.nested,p="on"===t?"addEventListener":"removeEventListener";if(o.touch){const t=!("touchstart"!==i.start||!o.passiveListener||!s.passiveListeners)&&{passive:!0,capture:!1};r[p](i.start,e.onTouchStart,t),r[p](i.move,e.onTouchMove,o.passiveListener?{passive:!1,capture:d}:d),r[p](i.end,e.onTouchEnd,t),i.cancel&&r[p](i.cancel,e.onTouchEnd,t)}else r[p](i.start,e.onTouchStart,!1),a[p](i.move,e.onTouchMove,d),a[p](i.end,e.onTouchEnd,!1);(s.preventClicks||s.preventClicksPropagation)&&r[p]("click",e.onClick,!0),s.cssMode&&l[p]("scroll",e.onScroll),s.updateOnWindowResize?e[t](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",h,!0):e[t]("observerUpdate",h,!0)},b=(e,t)=>e.grid&&t.grid&&1<t.grid.rows;var x={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};const y={eventsEmitter:{on(e,t,a){const s=this;if(s.eventsListeners&&!s.destroyed&&"function"==typeof t){const i=a?"unshift":"push";e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][i](t)})}return s},once(s,i,e){const r=this;return!r.eventsListeners||r.destroyed||"function"!=typeof i?r:(l.__emitterProxy=i,r.on(s,l,e));function l(){r.off(s,l),l.__emitterProxy&&delete l.__emitterProxy;for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];i.apply(r,t)}},onAny(e,t){var a=this;return a.eventsListeners&&!a.destroyed&&"function"==typeof e&&(t=t?"unshift":"push",a.eventsAnyListeners.indexOf(e)<0)&&a.eventsAnyListeners[t](e),a},offAny(e){var t=this;return t.eventsListeners&&!t.destroyed&&t.eventsAnyListeners&&0<=(e=t.eventsAnyListeners.indexOf(e))&&t.eventsAnyListeners.splice(e,1),t},off(e,s){const i=this;return!i.eventsListeners||i.destroyed||i.eventsListeners&&e.split(" ").forEach(a=>{void 0===s?i.eventsListeners[a]=[]:i.eventsListeners[a]&&i.eventsListeners[a].forEach((e,t)=>{(e===s||e.__emitterProxy&&e.__emitterProxy===s)&&i.eventsListeners[a].splice(t,1)})}),i},emit(){const i=this;if(i.eventsListeners&&!i.destroyed&&i.eventsListeners){let e,a,s;for(var t=arguments.length,r=new Array(t),l=0;l<t;l++)r[l]=arguments[l];s="string"==typeof r[0]||Array.isArray(r[0])?(e=r[0],a=r.slice(1,r.length),i):(e=r[0].events,a=r[0].data,r[0].context||i),a.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(t=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(e=>{e.apply(s,[t,...a])}),i.eventsListeners&&i.eventsListeners[t]&&i.eventsListeners[t].forEach(e=>{e.apply(s,a)})})}return i}},update:{updateSize:function(){var e=this;let t,a;var s=e.$el;t=void 0!==e.params.width&&null!==e.params.width?e.params.width:s[0].clientWidth,a=void 0!==e.params.height&&null!==e.params.height?e.params.height:s[0].clientHeight,0===t&&e.isHorizontal()||0===a&&e.isVertical()||(t=t-parseInt(s.css("padding-left")||0,10)-parseInt(s.css("padding-right")||0,10),a=a-parseInt(s.css("padding-top")||0,10)-parseInt(s.css("padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(a)&&(a=0),Object.assign(e,{width:t,height:a,size:e.isHorizontal()?t:a}))},updateSlides:function(){const a=this;function s(e){return a.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}function i(e,t){return parseFloat(e.getPropertyValue(s(t))||0)}const r=a.params,{$wrapperEl:l,size:n,rtlTranslate:o,wrongRTL:d}=a,p=a.virtual&&r.virtual.enabled,e=(p?a.virtual:a).slides.length,c=l.children("."+a.params.slideClass),u=(p?a.virtual.slides:c).length;let h=[];const m=[],f=[];let v=r.slidesOffsetBefore,g=("function"==typeof v&&(v=r.slidesOffsetBefore.call(a)),r.slidesOffsetAfter);"function"==typeof g&&(g=r.slidesOffsetAfter.call(a));var w=a.snapGrid.length,b=a.slidesGrid.length;let x=r.spaceBetween,y=-v,E=0,C=0;if(void 0!==n){"string"==typeof x&&0<=x.indexOf("%")&&(x=parseFloat(x.replace("%",""))/100*n),a.virtualSize=-x,o?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),r.centeredSlides&&r.cssMode&&(M(a.wrapperEl,"--swiper-centered-offset-before",""),M(a.wrapperEl,"--swiper-centered-offset-after",""));var T=r.grid&&1<r.grid.rows&&a.grid;let t;T&&a.grid.initSlides(u);var S="auto"===r.slidesPerView&&r.breakpoints&&0<Object.keys(r.breakpoints).filter(e=>void 0!==r.breakpoints[e].slidesPerView).length;for(let e=0;e<u;e+=1){t=0;const o=c.eq(e);if(T&&a.grid.updateSlide(e,o,u,s),"none"!==o.css("display")){if("auto"===r.slidesPerView){S&&(c[e].style[s("width")]="");const n=getComputedStyle(o[0]),d=o[0].style.transform,p=o[0].style.webkitTransform;if(d&&(o[0].style.transform="none"),p&&(o[0].style.webkitTransform="none"),r.roundLengths)t=a.isHorizontal()?o.outerWidth(!0):o.outerHeight(!0);else{const a=i(n,"width"),s=i(n,"padding-left"),r=i(n,"padding-right"),l=i(n,"margin-left"),d=i(n,"margin-right"),p=n.getPropertyValue("box-sizing");if(p&&"border-box"===p)t=a+l+d;else{const{clientWidth:i,offsetWidth:n}=o[0];t=a+s+r+l+d+(n-i)}}d&&(o[0].style.transform=d),p&&(o[0].style.webkitTransform=p),r.roundLengths&&(t=Math.floor(t))}else t=(n-(r.slidesPerView-1)*x)/r.slidesPerView,r.roundLengths&&(t=Math.floor(t)),c[e]&&(c[e].style[s("width")]=t+"px");c[e]&&(c[e].swiperSlideSize=t),f.push(t),r.centeredSlides?(y=y+t/2+E/2+x,0===E&&0!==e&&(y=y-n/2-x),0===e&&(y=y-n/2-x),Math.abs(y)<.001&&(y=0),r.roundLengths&&(y=Math.floor(y)),C%r.slidesPerGroup==0&&h.push(y),m.push(y)):(r.roundLengths&&(y=Math.floor(y)),(C-Math.min(a.params.slidesPerGroupSkip,C))%a.params.slidesPerGroup==0&&h.push(y),m.push(y),y=y+t+x),a.virtualSize+=t+x,E=t,C+=1}}if(a.virtualSize=Math.max(a.virtualSize,n)+g,o&&d&&("slide"===r.effect||"coverflow"===r.effect)&&l.css({width:a.virtualSize+r.spaceBetween+"px"}),r.setWrapperSize&&l.css({[s("width")]:a.virtualSize+r.spaceBetween+"px"}),T&&a.grid.updateWrapperSize(t,h,s),!r.centeredSlides){const s=[];for(let t=0;t<h.length;t+=1){let e=h[t];r.roundLengths&&(e=Math.floor(e)),h[t]<=a.virtualSize-n&&s.push(e)}h=s,1<Math.floor(a.virtualSize-n)-Math.floor(h[h.length-1])&&h.push(a.virtualSize-n)}if(0===h.length&&(h=[0]),0!==r.spaceBetween){const i=a.isHorizontal()&&o?"marginLeft":s("marginRight");c.filter((e,t)=>!r.cssMode||t!==c.length-1).css({[i]:x+"px"})}if(r.centeredSlides&&r.centeredSlidesBounds){let t=0;f.forEach(e=>{t+=e+(r.spaceBetween||0)});const s=(t-=r.spaceBetween)-n;h=h.map(e=>e<0?-v:e>s?s+g:e)}if(r.centerInsufficientSlides){let t=0;if(f.forEach(e=>{t+=e+(r.spaceBetween||0)}),(t-=r.spaceBetween)<n){const s=(n-t)/2;h.forEach((e,t)=>{h[t]=e-s}),m.forEach((e,t)=>{m[t]=e+s})}}if(Object.assign(a,{slides:c,snapGrid:h,slidesGrid:m,slidesSizesGrid:f}),r.centeredSlides&&r.cssMode&&!r.centeredSlidesBounds){M(a.wrapperEl,"--swiper-centered-offset-before",-h[0]+"px"),M(a.wrapperEl,"--swiper-centered-offset-after",a.size/2-f[f.length-1]/2+"px");const s=-a.snapGrid[0],i=-a.slidesGrid[0];a.snapGrid=a.snapGrid.map(e=>e+s),a.slidesGrid=a.slidesGrid.map(e=>e+i)}if(u!==e&&a.emit("slidesLengthChange"),h.length!==w&&(a.params.watchOverflow&&a.checkOverflow(),a.emit("snapGridLengthChange")),m.length!==b&&a.emit("slidesGridLengthChange"),r.watchSlidesProgress&&a.updateSlidesOffset(),!(p||r.cssMode||"slide"!==r.effect&&"fade"!==r.effect)){const s=r.containerModifierClass+"backface-hidden",i=a.$el.hasClass(s);u<=r.maxBackfaceHiddenSlides?i||a.$el.addClass(s):i&&a.$el.removeClass(s)}}},updateAutoHeight:function(e){const a=this,t=[],s=a.virtual&&a.params.virtual.enabled;let i,r=0;"number"==typeof e?a.setTransition(e):!0===e&&a.setTransition(a.params.speed);var l=t=>(s?a.slides.filter(e=>parseInt(e.getAttribute("data-swiper-slide-index"),10)===t):a.slides.eq(t))[0];if("auto"!==a.params.slidesPerView&&1<a.params.slidesPerView)if(a.params.centeredSlides)(a.visibleSlides||L([])).each(e=>{t.push(e)});else for(i=0;i<Math.ceil(a.params.slidesPerView);i+=1){const e=a.activeIndex+i;if(e>a.slides.length&&!s)break;t.push(l(e))}else t.push(l(a.activeIndex));for(i=0;i<t.length;i+=1)if(void 0!==t[i]){const e=t[i].offsetHeight;r=e>r?e:r}!r&&0!==r||a.$wrapperEl.css("height",r+"px")},updateSlidesOffset:function(){var t=this.slides;for(let e=0;e<t.length;e+=1)t[e].swiperSlideOffset=this.isHorizontal()?t[e].offsetLeft:t[e].offsetTop},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var s=this,i=s.params,{slides:r,rtlTranslate:l,snapGrid:n}=s;if(0!==r.length){void 0===r[0].swiperSlideOffset&&s.updateSlidesOffset();let a=l?e:-e;r.removeClass(i.slideVisibleClass),s.visibleSlidesIndexes=[],s.visibleSlides=[];for(let t=0;t<r.length;t+=1){var o=r[t];let e=o.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(e-=r[0].swiperSlideOffset);const L=(a+(i.centeredSlides?s.minTranslate():0)-e)/(o.swiperSlideSize+i.spaceBetween),d=(a-n[0]+(i.centeredSlides?s.minTranslate():0)-e)/(o.swiperSlideSize+i.spaceBetween),p=-(a-e),c=p+s.slidesSizesGrid[t];(0<=p&&p<s.size-1||1<c&&c<=s.size||p<=0&&c>=s.size)&&(s.visibleSlides.push(o),s.visibleSlidesIndexes.push(t),r.eq(t).addClass(i.slideVisibleClass)),o.progress=l?-L:L,o.originalProgress=l?-d:d}s.visibleSlides=L(s.visibleSlides)}},updateProgress:function(e){var t=this;if(void 0===e){const a=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*a||0}const a=t.params,s=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:r,isEnd:l}=t;var n=r,o=l;l=0==s?(i=0,r=!0):(i=(e-t.minTranslate())/s,r=i<=0,1<=i),Object.assign(t,{progress:i,isBeginning:r,isEnd:l}),(a.watchSlidesProgress||a.centeredSlides&&a.autoHeight)&&t.updateSlidesProgress(e),r&&!n&&t.emit("reachBeginning toEdge"),l&&!o&&t.emit("reachEnd toEdge"),(n&&!r||o&&!l)&&t.emit("fromEdge"),t.emit("progress",i)},updateSlidesClasses:function(){var{slides:e,params:t,$wrapperEl:a,activeIndex:s,realIndex:i}=this,r=this.virtual&&t.virtual.enabled;e.removeClass(`${t.slideActiveClass} ${t.slideNextClass} ${t.slidePrevClass} ${t.slideDuplicateActiveClass} ${t.slideDuplicateNextClass} `+t.slideDuplicatePrevClass),(r=r?this.$wrapperEl.find(`.${t.slideClass}[data-swiper-slide-index="${s}"]`):e.eq(s)).addClass(t.slideActiveClass),t.loop&&(r.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${i}"]`):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${i}"]`)).addClass(t.slideDuplicateActiveClass);let l=r.nextAll("."+t.slideClass).eq(0).addClass(t.slideNextClass),n=(t.loop&&0===l.length&&(l=e.eq(0)).addClass(t.slideNextClass),r.prevAll("."+t.slideClass).eq(0).addClass(t.slidePrevClass));t.loop&&0===n.length&&(n=e.eq(-1)).addClass(t.slidePrevClass),t.loop&&((l.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${l.attr("data-swiper-slide-index")}"]`)).addClass(t.slideDuplicateNextClass),(n.hasClass(t.slideDuplicateClass)?a.children(`.${t.slideClass}:not(.${t.slideDuplicateClass})[data-swiper-slide-index="${n.attr("data-swiper-slide-index")}"]`):a.children(`.${t.slideClass}.${t.slideDuplicateClass}[data-swiper-slide-index="${n.attr("data-swiper-slide-index")}"]`)).addClass(t.slideDuplicatePrevClass)),this.emitSlidesClasses()},updateActiveIndex:function(e){var t=this,a=t.rtlTranslate?t.translate:-t.translate,{slidesGrid:s,snapGrid:i,params:r,activeIndex:l,realIndex:n,snapIndex:o}=t;let d,p=e;if(void 0===p){for(let e=0;e<s.length;e+=1)void 0!==s[e+1]?a>=s[e]&&a<s[e+1]-(s[e+1]-s[e])/2?p=e:a>=s[e]&&a<s[e+1]&&(p=e+1):a>=s[e]&&(p=e);r.normalizeSlideIndex&&(p<0||void 0===p)&&(p=0)}if(0<=i.indexOf(a))d=i.indexOf(a);else{const e=Math.min(r.slidesPerGroupSkip,p);d=e+Math.floor((p-e)/r.slidesPerGroup)}d>=i.length&&(d=i.length-1),p===l?d!==o&&(t.snapIndex=d,t.emit("snapIndexChange")):(e=parseInt(t.slides.eq(p).attr("data-swiper-slide-index")||p,10),Object.assign(t,{snapIndex:d,realIndex:e,previousIndex:l,activeIndex:p}),t.emit("activeIndexChange"),t.emit("snapIndexChange"),n!==e&&t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&t.emit("slideChange"))},updateClickedSlide:function(e){var t=this,a=t.params,s=L(e).closest("."+a.slideClass)[0];let i,r=!1;if(s)for(let e=0;e<t.slides.length;e+=1)if(t.slides[e]===s){r=!0,i=e;break}s&&r?(t.clickedSlide=s,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(L(s).attr("data-swiper-slide-index"),10):t.clickedIndex=i,a.slideToClickedSlide&&void 0!==t.clickedIndex&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()):(t.clickedSlide=void 0,t.clickedIndex=void 0)}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var{params:t,rtlTranslate:a,translate:s,$wrapperEl:i}=this;if(t.virtualTranslate)return a?-s:s;if(t.cssMode)return s;let r=I(i[0],e);return(r=a?-r:r)||0},setTranslate:function(e,t){var a=this,{rtlTranslate:s,params:i,$wrapperEl:r,wrapperEl:l,progress:n}=a;let o=0,d=0;a.isHorizontal()?o=s?-e:e:d=e,i.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),i.cssMode?l[a.isHorizontal()?"scrollLeft":"scrollTop"]=a.isHorizontal()?-o:-d:i.virtualTranslate||r.transform(`translate3d(${o}px, ${d}px, 0px)`),a.previousTranslate=a.translate,a.translate=a.isHorizontal()?o:d;s=a.maxTranslate()-a.minTranslate();(0==s?0:(e-a.minTranslate())/s)!==n&&a.updateProgress(e),a.emit("setTranslate",a.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,a,s,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),void 0===s&&(s=!0);const r=this,{params:l,wrapperEl:n}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;var o=r.minTranslate(),d=r.maxTranslate(),o=s&&o<e?o:s&&e<d?d:e;if(r.updateProgress(o),l.cssMode){const e=r.isHorizontal();if(0===t)n[e?"scrollLeft":"scrollTop"]=-o;else{if(!r.support.smoothScroll)return w({swiper:r,targetPosition:-o,side:e?"left":"top"}),!0;n.scrollTo({[e?"left":"top"]:-o,behavior:"smooth"})}}else 0===t?(r.setTransition(0),r.setTranslate(o),a&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(o),a&&(r.emit("beforeTransitionStart",t,i),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,a)&&r.emit("transitionEnd")}),r.$wrapperEl[0].addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd)));return!0}},transition:{setTransition:function(e,t){this.params.cssMode||this.$wrapperEl.transition(e),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var a=this["params"];a.cssMode||(a.autoHeight&&this.updateAutoHeight(),c({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);var a=this["params"];this.animating=!1,a.cssMode||(this.setTransition(0),c({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,a,s,i){if(void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),"number"!=typeof(e=void 0===e?0:e)&&"string"!=typeof e)throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"==typeof e){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}const r=this;let l=e;l<0&&(l=0);var{params:e,snapGrid:n,slidesGrid:o,previousIndex:d,activeIndex:p,rtlTranslate:c,wrapperEl:u,enabled:h}=r;if(r.animating&&e.preventInteractionOnTransition||!h&&!s&&!i)return!1;h=Math.min(r.params.slidesPerGroupSkip,l);let m=h+Math.floor((l-h)/r.params.slidesPerGroup);m>=n.length&&(m=n.length-1),(p||e.initialSlide||0)===(d||0)&&a&&r.emit("beforeSlideChangeStart");var f=-n[m];if(r.updateProgress(f),e.normalizeSlideIndex)for(let e=0;e<o.length;e+=1){const t=-Math.floor(100*f),a=Math.floor(100*o[e]),s=Math.floor(100*o[e+1]);void 0!==o[e+1]?t>=a&&t<s-(s-a)/2?l=e:t>=a&&t<s&&(l=e+1):t>=a&&(l=e)}if(r.initialized&&l!==p){if(!r.allowSlideNext&&f<r.translate&&f<r.minTranslate())return!1;if(!r.allowSlidePrev&&f>r.translate&&f>r.maxTranslate()&&(p||0)!==l)return!1}let v;if(v=l>p?"next":l<p?"prev":"reset",c&&-f===r.translate||!c&&f===r.translate)return r.updateActiveIndex(l),e.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==e.effect&&r.setTranslate(f),"reset"!=v&&(r.transitionStart(a,v),r.transitionEnd(a,v)),!1;if(e.cssMode){const e=r.isHorizontal(),a=c?f:-f;if(0===t){const t=r.virtual&&r.params.virtual.enabled;t&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),u[e?"scrollLeft":"scrollTop"]=a,t&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._swiperImmediateVirtual=!1})}else{if(!r.support.smoothScroll)return w({swiper:r,targetPosition:a,side:e?"left":"top"}),!0;u.scrollTo({[e?"left":"top"]:a,behavior:"smooth"})}}else r.setTransition(t),r.setTranslate(f),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,s),r.transitionStart(a,v),0===t?r.transitionEnd(a,v):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(a,v))}),r.$wrapperEl[0].addEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd));return!0},slideToLoop:function(e,t,a,s){if(void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),"string"==typeof(e=void 0===e?0:e)){const t=parseInt(e,10);if(!isFinite(t))throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=t}let i=e;return this.params.loop&&(i+=this.loopedSlides),this.slideTo(i,t,a,s)},slideNext:function(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var s=this,{animating:i,enabled:r,params:l}=s;if(!r)return s;let n=l.slidesPerGroup;"auto"===l.slidesPerView&&1===l.slidesPerGroup&&l.slidesPerGroupAuto&&(n=Math.max(s.slidesPerViewDynamic("current",!0),1));r=s.activeIndex<l.slidesPerGroupSkip?1:n;if(l.loop){if(i&&l.loopPreventsSlide)return!1;s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft}return l.rewind&&s.isEnd?s.slideTo(0,e,t,a):s.slideTo(s.activeIndex+r,e,t,a)},slidePrev:function(e,t,a){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);const s=this,{params:i,animating:r,snapGrid:l,slidesGrid:n,rtlTranslate:o,enabled:d}=s;if(!d)return s;if(i.loop){if(r&&i.loopPreventsSlide)return!1;s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}const c=p(o?s.translate:-s.translate),u=l.map(e=>p(e));let h=l[u.indexOf(c)-1];if(void 0===h&&i.cssMode){let a;l.forEach((e,t)=>{c>=e&&(a=t)}),void 0!==a&&(h=l[0<a?a-1:a])}let m=0;if(void 0!==h&&((m=n.indexOf(h))<0&&(m=s.activeIndex-1),"auto"===i.slidesPerView)&&1===i.slidesPerGroup&&i.slidesPerGroupAuto&&(m=m-s.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0)),i.rewind&&s.isBeginning){const i=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(i,e,t,a)}return s.slideTo(m,e,t,a)},slideReset:function(e,t,a){return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t=void 0===t?!0:t,a)},slideToClosest:function(e,t,a,s){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===s&&(s=.5);var i=this;let r=i.activeIndex;var l=Math.min(i.params.slidesPerGroupSkip,r),l=l+Math.floor((r-l)/i.params.slidesPerGroup),n=i.rtlTranslate?i.translate:-i.translate;if(n>=i.snapGrid[l]){const e=i.snapGrid[l];n-e>(i.snapGrid[l+1]-e)*s&&(r+=i.params.slidesPerGroup)}else{const e=i.snapGrid[l-1];n-e<=(i.snapGrid[l]-e)*s&&(r-=i.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,i.slidesGrid.length-1),i.slideTo(r,e,t,a)},slideToClickedSlide:function(){const e=this,{params:t,$wrapperEl:a}=e,s="auto"===t.slidesPerView?e.slidesPerViewDynamic():t.slidesPerView;let i,r=e.clickedIndex;t.loop?e.animating||(i=parseInt(L(e.clickedSlide).attr("data-swiper-slide-index"),10),t.centeredSlides?r<e.loopedSlides-s/2||r>e.slides.length-e.loopedSlides+s/2?(e.loopFix(),r=a.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),S(()=>{e.slideTo(r)})):e.slideTo(r):r>e.slides.length-s?(e.loopFix(),r=a.children(`.${t.slideClass}[data-swiper-slide-index="${i}"]:not(.${t.slideDuplicateClass})`).eq(0).index(),S(()=>{e.slideTo(r)})):e.slideTo(r)):e.slideTo(r)}},loop:{loopCreate:function(){const s=this,t=T(),{params:a,$wrapperEl:e}=s,i=0<e.children().length?L(e.children()[0].parentNode):e;i.children(`.${a.slideClass}.`+a.slideDuplicateClass).remove();let r=i.children("."+a.slideClass);if(a.loopFillGroupWithBlank){const s=a.slidesPerGroup-r.length%a.slidesPerGroup;if(s!==a.slidesPerGroup){for(let e=0;e<s;e+=1){const s=L(t.createElement("div")).addClass(a.slideClass+" "+a.slideBlankClass);i.append(s)}r=i.children("."+a.slideClass)}}"auto"!==a.slidesPerView||a.loopedSlides||(a.loopedSlides=r.length),s.loopedSlides=Math.ceil(parseFloat(a.loopedSlides||a.slidesPerView,10)),s.loopedSlides+=a.loopAdditionalSlides,s.loopedSlides>r.length&&(s.loopedSlides=r.length);const l=[],n=[];r.each((e,t)=>{var a=L(e);t<s.loopedSlides&&n.push(e),t<r.length&&t>=r.length-s.loopedSlides&&l.push(e),a.attr("data-swiper-slide-index",t)});for(let e=0;e<n.length;e+=1)i.append(L(n[e].cloneNode(!0)).addClass(a.slideDuplicateClass));for(let e=l.length-1;0<=e;--e)i.prepend(L(l[e].cloneNode(!0)).addClass(a.slideDuplicateClass))},loopFix:function(){var e=this,{activeIndex:t,slides:a,loopedSlides:s,allowSlidePrev:i,allowSlideNext:r,snapGrid:l,rtlTranslate:n}=(e.emit("beforeLoopFix"),e);let o;e.allowSlidePrev=!0,e.allowSlideNext=!0;l=-l[t]-e.getTranslate();t<s?(o=a.length-3*s+t,o+=s,e.slideTo(o,0,!1,!0)&&0!=l&&e.setTranslate((n?-e.translate:e.translate)-l)):t>=a.length-s&&(o=-a.length+t+s,o+=s,e.slideTo(o,0,!1,!0))&&0!=l&&e.setTranslate((n?-e.translate:e.translate)-l),e.allowSlidePrev=i,e.allowSlideNext=r,e.emit("loopFix")},loopDestroy:function(){var{$wrapperEl:e,params:t,slides:a}=this;e.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.`+t.slideBlankClass).remove(),a.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){var t=this;t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode||((t="container"===t.params.touchEventsTarget?t.el:t.wrapperEl).style.cursor="move",t.style.cursor=e?"grabbing":"grab")},unsetGrabCursor:function(){var e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:{attachEvents:function(){var e=this,t=T(),{params:a,support:s}=e;e.onTouchStart=function(e){var s=this,i=T(),r=O(),l=s.touchEventsData,{params:n,touches:o,enabled:d}=s;if(d&&(!s.animating||!n.preventInteractionOnTransition)){!s.animating&&n.cssMode&&n.loop&&s.loopFix();let t=e,a=L((t=t.originalEvent?t.originalEvent:t).target);if(("wrapper"!==n.touchEventsTarget||a.closest(s.wrapperEl).length)&&(l.isTouchEvent="touchstart"===t.type,l.isTouchEvent||!("which"in t)||3!==t.which)&&!(!l.isTouchEvent&&"button"in t&&0<t.button||l.isTouched&&l.isMoved)){n.noSwipingClass&&""!==n.noSwipingClass&&t.target&&t.target.shadowRoot&&e.path&&e.path[0]&&(a=L(e.path[0]));var d=n.noSwipingSelector||"."+n.noSwipingClass,p=!(!t.target||!t.target.shadowRoot);if(n.noSwiping&&(p?function(s,e){return function e(t){var a;return t&&t!==T()&&t!==O()&&((a=(t=t.assignedSlot?t.assignedSlot:t).closest(s))||t.getRootNode)?a||e(t.getRootNode().host):null}(e=void 0===e?this:e)}(d,a[0]):a.closest(d)[0]))s.allowClick=!0;else if(!n.swipeHandler||a.closest(n.swipeHandler)[0]){o.currentX=("touchstart"===t.type?t.targetTouches[0]:t).pageX,o.currentY=("touchstart"===t.type?t.targetTouches[0]:t).pageY;var p=o.currentX,d=o.currentY,c=n.edgeSwipeDetection||n.iOSEdgeSwipeDetection,u=n.edgeSwipeThreshold||n.iOSEdgeSwipeThreshold;if(c&&(p<=u||p>=r.innerWidth-u)){if("prevent"!==c)return;e.preventDefault()}if(Object.assign(l,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=p,o.startY=d,l.touchStartTime=g(),s.allowClick=!0,s.updateSize(),s.swipeDirection=void 0,0<n.threshold&&(l.allowThresholdMove=!1),"touchstart"!==t.type){let e=!0;a.is(l.focusableElements)&&(e=!1,"SELECT"===a[0].nodeName)&&(l.isTouched=!1),i.activeElement&&L(i.activeElement).is(l.focusableElements)&&i.activeElement!==a[0]&&i.activeElement.blur();const T=e&&s.allowTouchMove&&n.touchStartPreventDefault;!n.touchStartForcePreventDefault&&!T||a[0].isContentEditable||t.preventDefault()}s.params.freeMode&&s.params.freeMode.enabled&&s.freeMode&&s.animating&&!n.cssMode&&s.freeMode.onTouchStart(),s.emit("touchStart",t)}}}}.bind(e),e.onTouchMove=function(e){var i=T(),r=this,l=r.touchEventsData,{params:n,touches:o,rtlTranslate:d,enabled:t}=r;if(t){let s=e;if(s.originalEvent&&(s=s.originalEvent),l.isTouched){if(!l.isTouchEvent||"touchmove"===s.type){t="touchmove"===s.type&&s.targetTouches&&(s.targetTouches[0]||s.changedTouches[0]),e=("touchmove"===s.type?t:s).pageX,t=("touchmove"===s.type?t:s).pageY;if(s.preventedByNestedSwiper)o.startX=e,o.startY=t;else if(r.allowTouchMove){if(l.isTouchEvent&&n.touchReleaseOnEdges&&!n.loop)if(r.isVertical()){if(t<o.startY&&r.translate<=r.maxTranslate()||t>o.startY&&r.translate>=r.minTranslate())return l.isTouched=!1,void(l.isMoved=!1)}else if(e<o.startX&&r.translate<=r.maxTranslate()||e>o.startX&&r.translate>=r.minTranslate())return;if(l.isTouchEvent&&i.activeElement&&s.target===i.activeElement&&L(s.target).is(l.focusableElements))l.isMoved=!0,r.allowClick=!1;else if(l.allowTouchCallbacks&&r.emit("touchMove",s),!(s.targetTouches&&1<s.targetTouches.length)){o.currentX=e,o.currentY=t;var a,i=o.currentX-o.startX,p=o.currentY-o.startY;if(!(r.params.threshold&&Math.sqrt(i**2+p**2)<r.params.threshold))if(void 0===l.isScrolling&&(r.isHorizontal()&&o.currentY===o.startY||r.isVertical()&&o.currentX===o.startX?l.isScrolling=!1:25<=i*i+p*p&&(a=180*Math.atan2(Math.abs(p),Math.abs(i))/Math.PI,l.isScrolling=r.isHorizontal()?a>n.touchAngle:90-a>n.touchAngle)),l.isScrolling&&r.emit("touchMoveOpposite",s),void 0!==l.startMoving||o.currentX===o.startX&&o.currentY===o.startY||(l.startMoving=!0),l.isScrolling)l.isTouched=!1;else if(l.startMoving){r.allowClick=!1,!n.cssMode&&s.cancelable&&s.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&s.stopPropagation(),l.isMoved||(n.loop&&!n.cssMode&&r.loopFix(),l.startTranslate=r.getTranslate(),r.setTransition(0),r.animating&&r.$wrapperEl.trigger("webkitTransitionEnd transitionend"),l.allowMomentumBounce=!1,!n.grabCursor||!0!==r.allowSlideNext&&!0!==r.allowSlidePrev||r.setGrabCursor(!0),r.emit("sliderFirstMove",s)),r.emit("sliderMove",s),l.isMoved=!0;let e=r.isHorizontal()?i:p,t=(o.diff=e,e*=n.touchRatio,d&&(e=-e),r.swipeDirection=0<e?"prev":"next",l.currentTranslate=e+l.startTranslate,!0),a=n.resistanceRatio;if(n.touchReleaseOnEdges&&(a=0),0<e&&l.currentTranslate>r.minTranslate()?(t=!1,n.resistance&&(l.currentTranslate=r.minTranslate()-1+(-r.minTranslate()+l.startTranslate+e)**a)):e<0&&l.currentTranslate<r.maxTranslate()&&(t=!1,n.resistance)&&(l.currentTranslate=r.maxTranslate()+1-(r.maxTranslate()-l.startTranslate-e)**a),t&&(s.preventedByNestedSwiper=!0),!r.allowSlideNext&&"next"===r.swipeDirection&&l.currentTranslate<l.startTranslate&&(l.currentTranslate=l.startTranslate),!r.allowSlidePrev&&"prev"===r.swipeDirection&&l.currentTranslate>l.startTranslate&&(l.currentTranslate=l.startTranslate),r.allowSlidePrev||r.allowSlideNext||(l.currentTranslate=l.startTranslate),0<n.threshold){if(!(Math.abs(e)>n.threshold||l.allowThresholdMove))return void(l.currentTranslate=l.startTranslate);if(!l.allowThresholdMove)return l.allowThresholdMove=!0,o.startX=o.currentX,o.startY=o.currentY,l.currentTranslate=l.startTranslate,void(o.diff=r.isHorizontal()?o.currentX-o.startX:o.currentY-o.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&r.freeMode||n.watchSlidesProgress)&&(r.updateActiveIndex(),r.updateSlidesClasses()),r.params.freeMode&&n.freeMode.enabled&&r.freeMode&&r.freeMode.onTouchMove(),r.updateProgress(l.currentTranslate),r.setTranslate(l.currentTranslate))}}}else L(s.target).is(l.focusableElements)||(r.allowClick=!1),l.isTouched&&(Object.assign(o,{startX:e,startY:t,currentX:e,currentY:t}),l.touchStartTime=g())}}else l.startMoving&&l.isScrolling&&r.emit("touchMoveOpposite",s)}}.bind(e),e.onTouchEnd=function(r){const l=this,e=l.touchEventsData,{params:n,touches:t,rtlTranslate:a,slidesGrid:o,enabled:s}=l;if(s){let i=r;if(i.originalEvent&&(i=i.originalEvent),e.allowTouchCallbacks&&l.emit("touchEnd",i),e.allowTouchCallbacks=!1,e.isTouched){n.grabCursor&&e.isMoved&&e.isTouched&&(!0===l.allowSlideNext||!0===l.allowSlidePrev)&&l.setGrabCursor(!1);var d,p=g(),c=p-e.touchStartTime;if(l.allowClick){const r=i.path||i.composedPath&&i.composedPath();l.updateClickedSlide(r&&r[0]||i.target),l.emit("tap click",i),c<300&&p-e.lastClickTime<300&&l.emit("doubleTap doubleClick",i)}if(e.lastClickTime=g(),S(()=>{l.destroyed||(l.allowClick=!0)}),e.isTouched&&e.isMoved&&l.swipeDirection&&0!==t.diff&&e.currentTranslate!==e.startTranslate){if(e.isTouched=!1,e.isMoved=!1,e.startMoving=!1,d=n.followFinger?a?l.translate:-l.translate:-e.currentTranslate,!n.cssMode)if(l.params.freeMode&&n.freeMode.enabled)l.freeMode.onTouchEnd({currentPos:d});else{let t=0,a=l.slidesSizesGrid[0];for(let e=0;e<o.length;e+=e<n.slidesPerGroupSkip?1:n.slidesPerGroup){const l=e<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==o[e+l]?d>=o[e]&&d<o[e+l]&&(t=e,a=o[e+l]-o[e]):d>=o[e]&&(t=e,a=o[o.length-1]-o[o.length-2])}let e=null,s=null;n.rewind&&(l.isBeginning?s=l.params.virtual&&l.params.virtual.enabled&&l.virtual?l.virtual.slides.length-1:l.slides.length-1:l.isEnd&&(e=0));r=(d-o[t])/a,p=t<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;c>n.longSwipesMs?n.longSwipes?("next"===l.swipeDirection&&(r>=n.longSwipesRatio?l.slideTo(n.rewind&&l.isEnd?e:t+p):l.slideTo(t)),"prev"===l.swipeDirection&&(r>1-n.longSwipesRatio?l.slideTo(t+p):null!==s&&r<0&&Math.abs(r)>n.longSwipesRatio?l.slideTo(s):l.slideTo(t))):l.slideTo(l.activeIndex):n.shortSwipes?!l.navigation||i.target!==l.navigation.nextEl&&i.target!==l.navigation.prevEl?("next"===l.swipeDirection&&l.slideTo(null!==e?e:t+p),"prev"===l.swipeDirection&&l.slideTo(null!==s?s:t)):i.target===l.navigation.nextEl?l.slideTo(t+p):l.slideTo(t):l.slideTo(l.activeIndex)}}else e.isTouched=!1,e.isMoved=!1,e.startMoving=!1}else e.isMoved&&n.grabCursor&&l.setGrabCursor(!1),e.isMoved=!1,e.startMoving=!1}}.bind(e),a.cssMode&&(e.onScroll=function(){var e=this,{wrapperEl:t,rtlTranslate:a,enabled:s}=e;s&&(e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses(),(0==(s=e.maxTranslate()-e.minTranslate())?0:(e.translate-e.minTranslate())/s)!==e.progress&&e.updateProgress(a?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1))}.bind(e)),e.onClick=function(e){var t=this;t.enabled&&!t.allowClick&&(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation)&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())}.bind(e),s.touch&&!f&&(t.addEventListener("touchstart",B),f=!0),v(e,"on")},detachEvents:function(){v(this,"off")}},breakpoints:{setBreakpoint:function(){const s=this,{activeIndex:e,initialized:t,loopedSlides:a=0,params:i,$el:r}=s,l=i.breakpoints;if(l&&0!==Object.keys(l).length){var n=s.getBreakpoint(l,s.params.breakpointsBase,s.el);if(n&&s.currentBreakpoint!==n){const p=(n in l?l[n]:void 0)||s.originalParams,c=b(s,i),u=b(s,p),h=i.enabled;c&&!u?(r.removeClass(`${i.containerModifierClass}grid ${i.containerModifierClass}grid-column`),s.emitContainerClasses()):!c&&u&&(r.addClass(i.containerModifierClass+"grid"),(p.grid.fill&&"column"===p.grid.fill||!p.grid.fill&&"column"===i.grid.fill)&&r.addClass(i.containerModifierClass+"grid-column"),s.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(e=>{var t=i[e]&&i[e].enabled,a=p[e]&&p[e].enabled;t&&!a&&s[e].disable(),!t&&a&&s[e].enable()});var o=p.direction&&p.direction!==i.direction,d=i.loop&&(p.slidesPerView!==i.slidesPerView||o),o=(o&&t&&s.changeDirection(),m(s.params,p),s.params.enabled);Object.assign(s,{allowTouchMove:s.params.allowTouchMove,allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev}),h&&!o?s.disable():!h&&o&&s.enable(),s.currentBreakpoint=n,s.emit("_beforeBreakpoint",p),d&&t&&(s.loopDestroy(),s.loopCreate(),s.updateSlides(),s.slideTo(e-a+s.loopedSlides,0,!1)),s.emit("breakpoint",p)}}},getBreakpoint:function(e,a,s){if(void 0===a&&(a="window"),e&&("container"!==a||s)){let t=!1;const i=O(),r="window"===a?i.innerHeight:s.clientHeight,l=Object.keys(e).map(e=>{var t;return"string"==typeof e&&0===e.indexOf("@")?(t=parseFloat(e.substr(1)),{value:r*t,point:e}):{value:e,point:e}});l.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<l.length;e+=1){const{point:O,value:r}=l[e];"window"===a?i.matchMedia(`(min-width: ${r}px)`).matches&&(t=O):r<=s.clientWidth&&(t=O)}return t||"max"}}},checkOverflow:{checkOverflow:function(){const e=this,{isLocked:t,params:a}=e,s=a["slidesOffsetBefore"];if(s){const t=e.slides.length-1,a=e.slidesGrid[t]+e.slidesSizesGrid[t]+2*s;e.isLocked=e.size>a}else e.isLocked=1===e.snapGrid.length;!0===a.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===a.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){var{classNames:e,params:t,rtl:a,$el:s,device:i,support:r}=this,r=function(e,a){const s=[];return e.forEach(t=>{"object"==typeof t?Object.keys(t).forEach(e=>{t[e]&&s.push(a+e)}):"string"==typeof t&&s.push(a+t)}),s}(["initialized",t.direction,{"pointer-events":!r.touch},{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:a},{grid:t.grid&&1<t.grid.rows},{"grid-column":t.grid&&1<t.grid.rows&&"column"===t.grid.fill},{android:i.android},{ios:i.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...r),s.addClass([...e].join(" ")),this.emitContainerClasses()},removeClasses:function(){var{$el:e,classNames:t}=this;e.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function(e,t,a,s,i,r){var l=O();function n(){r&&r()}!(L(e).parent("picture")[0]||e.complete&&i)&&t?((e=new l.Image).onload=n,e.onerror=n,s&&(e.sizes=s),a&&(e.srcset=a),t&&(e.src=t)):n()},preloadImages:function(){const t=this;function a(){null!=t&&t&&!t.destroyed&&(void 0!==t.imagesLoaded&&(t.imagesLoaded+=1),t.imagesLoaded===t.imagesToLoad.length)&&(t.params.updateOnImagesReady&&t.update(),t.emit("imagesReady"))}t.imagesToLoad=t.$el.find("img");for(let e=0;e<t.imagesToLoad.length;e+=1){var s=t.imagesToLoad[e];t.loadImage(s,s.currentSrc||s.getAttribute("src"),s.srcset||s.getAttribute("srcset"),s.sizes||s.getAttribute("sizes"),!0,a)}}}},E={};class C{constructor(){let t,a;for(var c,e=arguments.length,s=new Array(e),i=0;i<e;i++)s[i]=arguments[i];if(1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?a=s[0]:[t,a]=s,a=m({},a=a||{}),t&&!a.el&&(a.el=t),a.el&&1<L(a.el).length){const t=[];return L(a.el).each(e=>{e=m({},a,{el:e});t.push(new C(e))}),t}const r=this,l=(r.__swiper__=!0,r.support=u(),r.device=(void 0===(c={userAgent:a.userAgent})&&(c={}),p=p||function(){var e=(void 0===c?{}:c)["userAgent"],t=u(),a=O(),s=a.navigator.platform,e=e||a.navigator.userAgent,i={ios:!1,android:!1},r=a.screen.width,a=a.screen.height,l=e.match(/(Android);?[\s\/]+([\d.]+)?/);let n=e.match(/(iPad).*OS\s([\d_]+)/);var o=e.match(/(iPod)(.*OS\s([\d_]+))?/),d=!n&&e.match(/(iPhone\sOS|iOS)\s([\d_]+)/),p="Win32"===s,s="MacIntel"===s;return!n&&s&&t.touch&&0<=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(r+"x"+a)&&(n=(n=e.match(/(Version)\/([\d.]+)/))||[0,1,"13_0_0"]),l&&!p&&(i.os="android",i.android=!0),(n||d||o)&&(i.os="ios",i.ios=!0),i}()),r.browser=N(),r.eventsListeners={},r.eventsAnyListeners=[],r.modules=[...r.__modules__],a.modules&&Array.isArray(a.modules)&&r.modules.push(...a.modules),{});r.modules.forEach(e=>{var s,i;e({swiper:r,extendParams:(s=a,i=l,function(e){void 0===e&&(e={});var t=Object.keys(e)[0],a=e[t];"object"==typeof a&&null!==a&&(0<=["navigation","pagination","scrollbar"].indexOf(t)&&!0===s[t]&&(s[t]={auto:!0}),t in s)&&"enabled"in a&&(!0===s[t]&&(s[t]={enabled:!0}),"object"!=typeof s[t]||"enabled"in s[t]||(s[t].enabled=!0),s[t]||(s[t]={enabled:!1})),m(i,e)}),on:r.on.bind(r),once:r.once.bind(r),off:r.off.bind(r),emit:r.emit.bind(r)})});var n,o=m({},x,l);return r.params=m({},o,E,a),r.originalParams=m({},r.params),r.passedParams=m({},a),r.params&&r.params.on&&Object.keys(r.params.on).forEach(e=>{r.on(e,r.params.on[e])}),r.params&&r.params.onAny&&r.onAny(r.params.onAny),r.$=L,Object.assign(r,{enabled:r.params.enabled,el:t,classNames:[],slides:L(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===r.params.direction,isVertical:()=>"vertical"===r.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:r.params.allowSlideNext,allowSlidePrev:r.params.allowSlidePrev,touchEvents:(o=["touchstart","touchmove","touchend","touchcancel"],n=["pointerdown","pointermove","pointerup"],r.touchEventsTouch={start:o[0],move:o[1],end:o[2],cancel:o[3]},r.touchEventsDesktop={start:n[0],move:n[1],end:n[2]},r.support.touch||!r.params.simulateTouch?r.touchEventsTouch:r.touchEventsDesktop),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:r.params.focusableElements,lastClickTime:g(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:r.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),r.emit("_swiper"),r.params.init&&r.init(),r}enable(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){var a=this,s=(e=Math.min(Math.max(e,0),1),a.minTranslate()),e=(a.maxTranslate()-s)*e+s;a.translateTo(e,void 0===t?0:t),a.updateActiveIndex(),a.updateSlidesClasses()}emitContainerClasses(){const t=this;var e;t.params._emitClasses&&t.el&&(e=t.el.className.split(" ").filter(e=>0===e.indexOf("swiper")||0===e.indexOf(t.params.containerModifierClass)),t.emit("_containerClasses",e.join(" ")))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){const a=this;if(a.params._emitClasses&&a.el){const s=[];a.slides.each(e=>{var t=a.getSlideClasses(e);s.push({slideEl:e,classNames:t}),a.emit("_slideClass",e,t)}),a.emit("_slideClasses",s)}}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);var{params:a,slides:s,slidesGrid:i,slidesSizesGrid:r,size:l,activeIndex:n}=this;let o=1;if(a.centeredSlides){let t,a=s[n].swiperSlideSize;for(let e=n+1;e<s.length;e+=1)s[e]&&!t&&(a+=s[e].swiperSlideSize,o+=1,a>l)&&(t=!0);for(let e=n-1;0<=e;--e)s[e]&&!t&&(a+=s[e].swiperSlideSize,o+=1,a>l)&&(t=!0)}else if("current"===e)for(let e=n+1;e<s.length;e+=1)(t?i[e]+r[e]-i[n]<l:i[e]-i[n]<l)&&(o+=1);else for(let e=n-1;0<=e;--e)i[n]-i[e]<l&&(o+=1);return o}update(){const t=this;var e,a;function s(){var e=t.rtlTranslate?-1*t.translate:t.translate,e=Math.min(Math.max(e,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}t&&!t.destroyed&&({snapGrid:e,params:a}=t,a.breakpoints&&t.setBreakpoint(),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.params.freeMode&&t.params.freeMode.enabled?(s(),t.params.autoHeight&&t.updateAutoHeight()):(("auto"===t.params.slidesPerView||1<t.params.slidesPerView)&&t.isEnd&&!t.params.centeredSlides?t.slideTo(t.slides.length-1,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0))||s(),a.watchOverflow&&e!==t.snapGrid&&t.checkOverflow(),t.emit("update"))}changeDirection(t,e){void 0===e&&(e=!0);var a=this,s=a.params.direction;return(t=t||("horizontal"===s?"vertical":"horizontal"))===s||"horizontal"!==t&&"vertical"!==t||(a.$el.removeClass(""+a.params.containerModifierClass+s).addClass(""+a.params.containerModifierClass+t),a.emitContainerClasses(),a.params.direction=t,a.slides.each(e=>{"vertical"===t?e.style.width="":e.style.height=""}),a.emit("changeDirection"),e&&a.update()),a}changeLanguageDirection(e){var t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.$el.addClass(t.params.containerModifierClass+"rtl"),t.el.dir="rtl"):(t.$el.removeClass(t.params.containerModifierClass+"rtl"),t.el.dir="ltr"),t.update())}mount(e){const a=this;if(!a.mounted){const i=L(e||a.params.el);if(!(e=i[0]))return!1;e.swiper=a;const r=()=>"."+(a.params.wrapperClass||"").trim().split(" ").join(".");let t=e&&e.shadowRoot&&e.shadowRoot.querySelector?((s=L(e.shadowRoot.querySelector(r()))).children=e=>i.children(e),s):(i.children?i:L(i)).children(r());var s;if(0===t.length&&a.params.createElements){const e=T().createElement("div");t=L(e),e.className=a.params.wrapperClass,i.append(e),i.children("."+a.params.slideClass).each(e=>{t.append(e)})}Object.assign(a,{$el:i,el:e,$wrapperEl:t,wrapperEl:t[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction"),rtlTranslate:"horizontal"===a.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction")),wrongRTL:"-webkit-box"===t.css("display")})}return!0}init(e){var t=this;return t.initialized||!1!==t.mount(e)&&(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);const a=this,{params:s,$el:i,$wrapperEl:r,slides:l}=a;if(void 0!==a.params&&!a.destroyed){if(a.emit("beforeDestroy"),a.initialized=!1,a.detachEvents(),s.loop&&a.loopDestroy(),t&&(a.removeClasses(),i.removeAttr("style"),r.removeAttr("style"),l)&&l.length&&l.removeClass([s.slideVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index"),a.emit("destroy"),Object.keys(a.eventsListeners).forEach(e=>{a.off(e)}),!1!==e){a.$el[0].swiper=null;{const n=a;Object.keys(n).forEach(e=>{try{n[e]=null}catch(e){}try{delete n[e]}catch(e){}})}}a.destroyed=!0}return null}static extendDefaults(e){m(E,e)}static get extendedDefaults(){return E}static get defaults(){return x}static installModule(e){C.prototype.__modules__||(C.prototype.__modules__=[]);var t=C.prototype.__modules__;"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>C.installModule(e)):C.installModule(e),C}}function $(a,s,i,r){const l=T();return a.params.createElements&&Object.keys(r).forEach(t=>{if(!i[t]&&!0===i.auto){let e=a.$el.children("."+r[t])[0];e||((e=l.createElement("div")).className=r[t],a.$el.append(e)),i[t]=e,s[t]=e}}),i}function P(e){return"."+(e=void 0===e?"":e).trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}function k(e){const{effect:a,swiper:s,on:t,setTranslate:i,setTransition:r,overwriteParams:l,perspective:n,recreateShadows:o,getEffectParams:d}=e;let p;t("beforeInit",()=>{var e;s.params.effect===a&&(s.classNames.push(""+s.params.containerModifierClass+a),n&&n()&&s.classNames.push(s.params.containerModifierClass+"3d"),e=l?l():{},Object.assign(s.params,e),Object.assign(s.originalParams,e))}),t("setTranslate",()=>{s.params.effect===a&&i()}),t("setTransition",(e,t)=>{s.params.effect===a&&r(t)}),t("transitionEnd",()=>{s.params.effect===a&&o&&d&&d().slideShadows&&(s.slides.each(e=>{s.$(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").remove()}),o())}),t("virtualUpdate",()=>{s.params.effect===a&&(s.slides.length||(p=!0),requestAnimationFrame(()=>{p&&s.slides&&s.slides.length&&(i(),p=!1)}))})}function z(e,t){return e.transformEl?t.find(e.transformEl).css({"backface-visibility":"hidden","-webkit-backface-visibility":"hidden"}):t}function A(e){let{swiper:a,duration:t,transformEl:s,allSlides:i}=e;const{slides:r,activeIndex:l,$wrapperEl:n}=a;if(a.params.virtualTranslate&&0!==t){let e=!1;(i?s?r.find(s):r:s?r.eq(l).find(s):r.eq(l)).transitionEnd(()=>{if(!e&&a&&!a.destroyed){e=!0,a.animating=!1;var t=["webkitTransitionEnd","transitionend"];for(let e=0;e<t.length;e+=1)n.trigger(t[e])}})}}function D(e,t,a){var s="swiper-slide-shadow"+(a?"-"+a:""),e=e.transformEl?t.find(e.transformEl):t;let i=e.children("."+s);return i.length||(i=L(`<div class="swiper-slide-shadow${a?"-"+a:""}"></div>`),e.append(i)),i}return Object.keys(y).forEach(t=>{Object.keys(y[t]).forEach(e=>{C.prototype[e]=y[t][e]})}),C.use([function(e){let{swiper:r,on:t,emit:a}=e;const s=O();let i=null,l=null;const n=()=>{r&&!r.destroyed&&r.initialized&&(a("beforeResize"),a("resize"))},o=()=>{r&&!r.destroyed&&r.initialized&&a("orientationchange")};t("init",()=>{r.params.resizeObserver&&void 0!==s.ResizeObserver?r&&!r.destroyed&&r.initialized&&(i=new ResizeObserver(a=>{l=s.requestAnimationFrame(()=>{var{width:e,height:t}=r;let s=e,i=t;a.forEach(e=>{var{contentBoxSize:e,contentRect:t,target:a}=e;a&&a!==r.el||(s=t?t.width:(e[0]||e).inlineSize,i=t?t.height:(e[0]||e).blockSize)}),s===e&&i===t||n()})})).observe(r.el):(s.addEventListener("resize",n),s.addEventListener("orientationchange",o))}),t("destroy",()=>{l&&s.cancelAnimationFrame(l),i&&i.unobserve&&r.el&&(i.unobserve(r.el),i=null),s.removeEventListener("resize",n),s.removeEventListener("orientationchange",o)})},function(e){let{swiper:a,extendParams:t,on:s,emit:i}=e;function r(e,t){void 0===t&&(t={});var a=new(n.MutationObserver||n.WebkitMutationObserver)(e=>{var t;1===e.length?i("observerUpdate",e[0]):(t=function(){i("observerUpdate",e[0])},n.requestAnimationFrame?n.requestAnimationFrame(t):n.setTimeout(t,0))});a.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),l.push(a)}const l=[],n=O();t({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",()=>{if(a.params.observer){if(a.params.observeParents){var t=a.$el.parents();for(let e=0;e<t.length;e+=1)r(t[e])}r(a.$el[0],{childList:a.params.observeSlideChildren}),r(a.$wrapperEl[0],{attributes:!1})}}),s("destroy",()=>{l.forEach(e=>{e.disconnect()}),l.splice(0,l.length)})}]),C.use([function(e){let t,{swiper:y,extendParams:a,on:s,emit:E}=e;function C(e,t){var a=y.params.virtual;return a.cache&&y.virtual.cache[t]?y.virtual.cache[t]:((e=a.renderSlide?L(a.renderSlide.call(y,e,t)):L(`<div class="${y.params.slideClass}" data-swiper-slide-index="${t}">${e}</div>`)).attr("data-swiper-slide-index")||e.attr("data-swiper-slide-index",t),a.cache&&(y.virtual.cache[t]=e),e)}function l(t){const{slidesPerView:e,slidesPerGroup:a,centeredSlides:s}=y.params,{addSlidesBefore:i,addSlidesAfter:r}=y.params.virtual,{from:l,to:n,slides:o,slidesGrid:d,offset:p}=y.virtual;y.params.cssMode||y.updateActiveIndex();var c=y.activeIndex||0;let u,h,m;u=y.rtlTranslate?"right":y.isHorizontal()?"left":"top",m=s?(h=Math.floor(e/2)+a+r,Math.floor(e/2)+a+i):(h=e+(a-1)+r,a+i);const f=Math.max((c||0)-m,0),v=Math.min((c||0)+h,o.length-1),g=(y.slidesGrid[f]||0)-(y.slidesGrid[0]||0);function w(){y.updateSlides(),y.updateProgress(),y.updateSlidesClasses(),y.lazy&&y.params.lazy.enabled&&y.lazy.load(),E("virtualUpdate")}if(Object.assign(y.virtual,{from:f,to:v,offset:g,slidesGrid:y.slidesGrid}),l!==f||n!==v||t)if(y.params.virtual.renderExternal)y.params.virtual.renderExternal.call(y,{offset:g,from:f,to:v,slides:function(){var t=[];for(let e=f;e<=v;e+=1)t.push(o[e]);return t}()}),y.params.virtual.renderExternalUpdate?w():E("virtualUpdate");else{var b=[],x=[];if(t)y.$wrapperEl.find("."+y.params.slideClass).remove();else for(let e=l;e<=n;e+=1)(e<f||e>v)&&y.$wrapperEl.find(`.${y.params.slideClass}[data-swiper-slide-index="${e}"]`).remove();for(let e=0;e<o.length;e+=1)e>=f&&e<=v&&(void 0===n||t?x.push(e):(e>n&&x.push(e),e<l&&b.push(e)));x.forEach(e=>{y.$wrapperEl.append(C(o[e],e))}),b.sort((e,t)=>t-e).forEach(e=>{y.$wrapperEl.prepend(C(o[e],e))}),y.$wrapperEl.children(".swiper-slide").css(u,g+"px"),w()}else y.slidesGrid!==d&&g!==p&&y.slides.css(u,g+"px"),y.updateProgress(),E("virtualUpdate")}a({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),y.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]},s("beforeInit",()=>{y.params.virtual.enabled&&(y.virtual.slides=y.params.virtual.slides,y.classNames.push(y.params.containerModifierClass+"virtual"),y.params.watchSlidesProgress=!0,y.originalParams.watchSlidesProgress=!0,y.params.initialSlide||l())}),s("setTranslate",()=>{y.params.virtual.enabled&&(y.params.cssMode&&!y._immediateVirtual?(clearTimeout(t),t=setTimeout(()=>{l()},100)):l())}),s("init update resize",()=>{y.params.virtual.enabled&&y.params.cssMode&&M(y.wrapperEl,"--swiper-virtual-size",y.virtualSize+"px")}),Object.assign(y.virtual,{appendSlide:function(t){if("object"==typeof t&&"length"in t)for(let e=0;e<t.length;e+=1)t[e]&&y.virtual.slides.push(t[e]);else y.virtual.slides.push(t);l(!0)},prependSlide:function(s){const i=y.activeIndex;let e=i+1,r=1;if(Array.isArray(s)){for(let e=0;e<s.length;e+=1)s[e]&&y.virtual.slides.unshift(s[e]);e=i+s.length,r=s.length}else y.virtual.slides.unshift(s);if(y.params.virtual.cache){const s=y.virtual.cache,i={};Object.keys(s).forEach(e=>{var t=s[e],a=t.attr("data-swiper-slide-index");a&&t.attr("data-swiper-slide-index",parseInt(a,10)+r),i[parseInt(e,10)+r]=t}),y.virtual.cache=i}l(!0),y.slideTo(e,0)},removeSlide:function(a){if(null!=a){let t=y.activeIndex;if(Array.isArray(a))for(let e=a.length-1;0<=e;--e)y.virtual.slides.splice(a[e],1),y.params.virtual.cache&&delete y.virtual.cache[a[e]],a[e]<t&&--t,t=Math.max(t,0);else y.virtual.slides.splice(a,1),y.params.virtual.cache&&delete y.virtual.cache[a],a<t&&--t,t=Math.max(t,0);l(!0),y.slideTo(t,0)}},removeAllSlides:function(){y.virtual.slides=[],y.params.virtual.cache&&(y.virtual.cache={}),l(!0),y.slideTo(0,0)},update:l})},function(e){let{swiper:c,extendParams:t,on:a,emit:u}=e;const h=T(),m=O();function s(t){if(c.enabled){const a=c["rtlTranslate"];let e=t;const s=(e=e.originalEvent?e.originalEvent:e).keyCode||e.charCode,i=c.params.keyboard.pageUpDown,r=i&&33===s,l=i&&34===s,n=37===s,o=39===s,d=38===s,p=40===s;if(!c.allowSlideNext&&(c.isHorizontal()&&o||c.isVertical()&&p||l))return!1;if(!c.allowSlidePrev&&(c.isHorizontal()&&n||c.isVertical()&&d||r))return!1;if(!(e.shiftKey||e.altKey||e.ctrlKey||e.metaKey||h.activeElement&&h.activeElement.nodeName&&("input"===h.activeElement.nodeName.toLowerCase()||"textarea"===h.activeElement.nodeName.toLowerCase()))){if(c.params.keyboard.onlyInViewport&&(r||l||n||o||d||p)){let t=!1;if(0<c.$el.parents("."+c.params.slideClass).length&&0===c.$el.parents("."+c.params.slideActiveClass).length)return;const e=c.$el,s=e[0].clientWidth,i=e[0].clientHeight,u=m.innerWidth,h=m.innerHeight,r=c.$el.offset(),l=(a&&(r.left-=c.$el[0].scrollLeft),[[r.left,r.top],[r.left+s,r.top],[r.left,r.top+i],[r.left+s,r.top+i]]);for(let e=0;e<l.length;e+=1){const a=l[e];0<=a[0]&&a[0]<=u&&0<=a[1]&&a[1]<=h&&(0===a[0]&&0===a[1]||(t=!0))}if(!t)return}c.isHorizontal()?((r||l||n||o)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),((l||o)&&!a||(r||n)&&a)&&c.slideNext(),((r||n)&&!a||(l||o)&&a)&&c.slidePrev()):((r||l||d||p)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),(l||p)&&c.slideNext(),(r||d)&&c.slidePrev()),u("keyPress",s)}}}function i(){c.keyboard.enabled||(L(h).on("keydown",s),c.keyboard.enabled=!0)}function r(){c.keyboard.enabled&&(L(h).off("keydown",s),c.keyboard.enabled=!1)}c.keyboard={enabled:!1},t({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),a("init",()=>{c.params.keyboard.enabled&&i()}),a("destroy",()=>{c.keyboard.enabled&&r()}),Object.assign(c.keyboard,{enable:i,disable:r})},function(e){let{swiper:d,extendParams:t,on:a,emit:p}=e;const s=O();let c;t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null}}),d.mousewheel={enabled:!1};let u,i=g();const h=[];function r(){d.enabled&&(d.mouseEntered=!0)}function l(){d.enabled&&(d.mouseEntered=!1)}function m(e){d.params.mousewheel.thresholdDelta&&e.delta<d.params.mousewheel.thresholdDelta||d.params.mousewheel.thresholdTime&&g()-i<d.params.mousewheel.thresholdTime||6<=e.delta&&g()-i<60||(e.direction<0?d.isEnd&&!d.params.loop||d.animating||(d.slideNext(),p("scroll",e.raw)):d.isBeginning&&!d.params.loop||d.animating||(d.slidePrev(),p("scroll",e.raw)),i=(new s.Date).getTime())}function n(s){let i=s,r=!0;if(d.enabled){var l=d.params.mousewheel;d.params.cssMode&&i.preventDefault();let e=d.$el;if("container"!==d.params.mousewheel.eventsTarget&&(e=L(d.params.mousewheel.eventsTarget)),!d.mouseEntered&&!e[0].contains(i.target)&&!l.releaseOnEdges)return!0;i.originalEvent&&(i=i.originalEvent);let t=0;var n=d.rtlTranslate?-1:1,o=function(e){let t=0,a=0,s=0,i=0;return"detail"in e&&(a=e.detail),"wheelDelta"in e&&(a=-e.wheelDelta/120),"wheelDeltaY"in e&&(a=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=a,a=0),s=10*t,i=10*a,"deltaY"in e&&(i=e.deltaY),"deltaX"in e&&(s=e.deltaX),e.shiftKey&&!s&&(s=i,i=0),(s||i)&&e.deltaMode&&(1===e.deltaMode?(s*=40,i*=40):(s*=800,i*=800)),s&&!t&&(t=s<1?-1:1),i&&!a&&(a=i<1?-1:1),{spinX:t,spinY:a,pixelX:s,pixelY:i}}(i);if(l.forceToAxis)if(d.isHorizontal()){if(!(Math.abs(o.pixelX)>Math.abs(o.pixelY)))return!0;t=-o.pixelX*n}else{if(!(Math.abs(o.pixelY)>Math.abs(o.pixelX)))return!0;t=-o.pixelY}else t=Math.abs(o.pixelX)>Math.abs(o.pixelY)?-o.pixelX*n:-o.pixelY;if(0===t)return!0;l.invert&&(t=-t);let a=d.getTranslate()+t*l.sensitivity;if((a=a>=d.minTranslate()?d.minTranslate():a)<=d.maxTranslate()&&(a=d.maxTranslate()),(r=!!d.params.loop||!(a===d.minTranslate()||a===d.maxTranslate()))&&d.params.nested&&i.stopPropagation(),d.params.freeMode&&d.params.freeMode.enabled){const s={time:g(),delta:Math.abs(t),direction:Math.sign(t)},r=u&&s.time<u.time+500&&s.delta<=u.delta&&s.direction===u.direction;if(!r){u=void 0,d.params.loop&&d.loopFix();let e=d.getTranslate()+t*l.sensitivity;const L=d.isBeginning,g=d.isEnd;if((e=e>=d.minTranslate()?d.minTranslate():e)<=d.maxTranslate()&&(e=d.maxTranslate()),d.setTransition(0),d.setTranslate(e),d.updateProgress(),d.updateActiveIndex(),d.updateSlidesClasses(),(!L&&d.isBeginning||!g&&d.isEnd)&&d.updateSlidesClasses(),d.params.freeMode.sticky){clearTimeout(c),c=void 0,15<=h.length&&h.shift();const i=h.length?h[h.length-1]:void 0,r=h[0];if(h.push(s),i&&(s.delta>i.delta||s.direction!==i.direction))h.splice(0);else if(15<=h.length&&s.time-r.time<500&&1<=r.delta-s.delta&&s.delta<=6){const i=0<t?.8:.2;u=s,h.splice(0),c=S(()=>{d.slideToClosest(d.params.speed,!0,void 0,i)},0)}c=c||S(()=>{u=s,h.splice(0),d.slideToClosest(d.params.speed,!0,void 0,.5)},500)}if(r||p("scroll",i),d.params.autoplay&&d.params.autoplayDisableOnInteraction&&d.autoplay.stop(),e===d.minTranslate()||e===d.maxTranslate())return!0}}else{const i={time:g(),delta:Math.abs(t),direction:Math.sign(t),raw:s},r=(2<=h.length&&h.shift(),h.length?h[h.length-1]:void 0);if(h.push(i),(!r||i.direction!==r.direction||i.delta>r.delta||i.time>r.time+150)&&m(i),function(e){var t=d.params.mousewheel;if(e.direction<0){if(d.isEnd&&!d.params.loop&&t.releaseOnEdges)return 1}else if(d.isBeginning&&!d.params.loop&&t.releaseOnEdges)return 1}(i))return!0}return i.preventDefault?i.preventDefault():i.returnValue=!1,!1}}function o(e){let t=d.$el;(t="container"!==d.params.mousewheel.eventsTarget?L(d.params.mousewheel.eventsTarget):t)[e]("mouseenter",r),t[e]("mouseleave",l),t[e]("wheel",n)}function f(){return d.params.cssMode?(d.wrapperEl.removeEventListener("wheel",n),!0):!d.mousewheel.enabled&&(o("on"),d.mousewheel.enabled=!0)}function v(){return d.params.cssMode?(d.wrapperEl.addEventListener(event,n),!0):!!d.mousewheel.enabled&&(o("off"),!(d.mousewheel.enabled=!1))}a("init",()=>{!d.params.mousewheel.enabled&&d.params.cssMode&&v(),d.params.mousewheel.enabled&&f()}),a("destroy",()=>{d.params.cssMode&&f(),d.mousewheel.enabled&&v()}),Object.assign(d.mousewheel,{enable:f,disable:v})},function(e){let{swiper:i,extendParams:t,on:a,emit:r}=e;function s(e){let t;return t=e&&(t=L(e),i.params.uniqueNavElements)&&"string"==typeof e&&1<t.length&&1===i.$el.find(e).length?i.$el.find(e):t}function l(e,t){var a=i.params.navigation;e&&0<e.length&&(e[t?"addClass":"removeClass"](a.disabledClass),e[0]&&"BUTTON"===e[0].tagName&&(e[0].disabled=t),i.params.watchOverflow)&&i.enabled&&e[i.isLocked?"addClass":"removeClass"](a.lockClass)}function n(){var e,t;i.params.loop||({$nextEl:e,$prevEl:t}=i.navigation,l(t,i.isBeginning&&!i.params.rewind),l(e,i.isEnd&&!i.params.rewind))}function o(e){e.preventDefault(),i.isBeginning&&!i.params.loop&&!i.params.rewind||(i.slidePrev(),r("navigationPrev"))}function d(e){e.preventDefault(),i.isEnd&&!i.params.loop&&!i.params.rewind||(i.slideNext(),r("navigationNext"))}function p(){var e,t,a=i.params.navigation;i.params.navigation=$(i,i.originalParams.navigation,i.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),(a.nextEl||a.prevEl)&&(e=s(a.nextEl),t=s(a.prevEl),e&&0<e.length&&e.on("click",d),t&&0<t.length&&t.on("click",o),Object.assign(i.navigation,{$nextEl:e,nextEl:e&&e[0],$prevEl:t,prevEl:t&&t[0]}),i.enabled||(e&&e.addClass(a.lockClass),t&&t.addClass(a.lockClass)))}function c(){var{$nextEl:e,$prevEl:t}=i.navigation;e&&e.length&&(e.off("click",d),e.removeClass(i.params.navigation.disabledClass)),t&&t.length&&(t.off("click",o),t.removeClass(i.params.navigation.disabledClass))}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),i.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},a("init",()=>{(!1===i.params.navigation.enabled?u:(p(),n))()}),a("toEdge fromEdge lock unlock",()=>{n()}),a("destroy",()=>{c()}),a("enable disable",()=>{var{$nextEl:e,$prevEl:t}=i.navigation;e&&e[i.enabled?"removeClass":"addClass"](i.params.navigation.lockClass),t&&t[i.enabled?"removeClass":"addClass"](i.params.navigation.lockClass)}),a("click",(e,t)=>{var{$nextEl:a,$prevEl:s}=i.navigation,t=t.target;if(i.params.navigation.hideOnClick&&!L(t).is(s)&&!L(t).is(a)&&(!(i.pagination&&i.params.pagination&&i.params.pagination.clickable)||i.pagination.el!==t&&!i.pagination.el.contains(t))){let e;a?e=a.hasClass(i.params.navigation.hiddenClass):s&&(e=s.hasClass(i.params.navigation.hiddenClass)),r(!0===e?"navigationShow":"navigationHide"),a&&a.toggleClass(i.params.navigation.hiddenClass),s&&s.toggleClass(i.params.navigation.hiddenClass)}});const u=()=>{i.$el.addClass(i.params.navigation.navigationDisabledClass),c()};Object.assign(i.navigation,{enable:()=>{i.$el.removeClass(i.params.navigation.navigationDisabledClass),p(),n()},disable:u,update:n,init:p,destroy:c})},function(e){let{swiper:o,extendParams:t,on:a,emit:d}=e;e="swiper-pagination";let p,c=(t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:e+"-bullet",bulletActiveClass:e+"-bullet-active",modifierClass:e+"-",currentClass:e+"-current",totalClass:e+"-total",hiddenClass:e+"-hidden",progressbarFillClass:e+"-progressbar-fill",progressbarOppositeClass:e+"-progressbar-opposite",clickableClass:e+"-clickable",lockClass:e+"-lock",horizontalClass:e+"-horizontal",verticalClass:e+"-vertical",paginationDisabledClass:e+"-disabled"}}),o.pagination={el:null,$el:null,bullets:[]},0);function u(){return!o.params.pagination.el||!o.pagination.el||!o.pagination.$el||0===o.pagination.$el.length}function h(e,t){var a=o.params.pagination["bulletActiveClass"];e[t]().addClass(a+"-"+t)[t]().addClass(a+`-${t}-`+t)}function s(){const t=o.rtl,r=o.params.pagination;if(!u()){const l=(o.virtual&&o.params.virtual.enabled?o.virtual:o).slides.length,n=o.pagination.$el;let i;var a=o.params.loop?Math.ceil((l-2*o.loopedSlides)/o.params.slidesPerGroup):o.snapGrid.length;if(o.params.loop?((i=Math.ceil((o.activeIndex-o.loopedSlides)/o.params.slidesPerGroup))>l-1-2*o.loopedSlides&&(i-=l-2*o.loopedSlides),i>a-1&&(i-=a),i<0&&"bullets"!==o.params.paginationType&&(i=a+i)):i=void 0!==o.snapIndex?o.snapIndex:o.activeIndex||0,"bullets"===r.type&&o.pagination.bullets&&0<o.pagination.bullets.length){const l=o.pagination.bullets;let a,s,e;if(r.dynamicBullets&&(p=l.eq(0)[o.isHorizontal()?"outerWidth":"outerHeight"](!0),n.css(o.isHorizontal()?"width":"height",p*(r.dynamicMainBullets+4)+"px"),1<r.dynamicMainBullets&&void 0!==o.previousIndex&&((c+=i-(o.previousIndex-o.loopedSlides||0))>r.dynamicMainBullets-1?c=r.dynamicMainBullets-1:c<0&&(c=0)),a=Math.max(i-c,0),s=a+(Math.min(l.length,r.dynamicMainBullets)-1),e=(s+a)/2),l.removeClass(["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>""+r.bulletActiveClass+e).join(" ")),1<n.length)l.each(e=>{var e=L(e),t=e.index();t===i&&e.addClass(r.bulletActiveClass),r.dynamicBullets&&(t>=a&&t<=s&&e.addClass(r.bulletActiveClass+"-main"),t===a&&h(e,"prev"),t===s)&&h(e,"next")});else{const t=l.eq(i),n=t.index();if(t.addClass(r.bulletActiveClass),r.dynamicBullets){const t=l.eq(a),p=l.eq(s);for(let e=a;e<=s;e+=1)l.eq(e).addClass(r.bulletActiveClass+"-main");if(o.params.loop)if(n>=l.length){for(let e=r.dynamicMainBullets;0<=e;--e)l.eq(l.length-e).addClass(r.bulletActiveClass+"-main");l.eq(l.length-r.dynamicMainBullets-1).addClass(r.bulletActiveClass+"-prev")}else h(t,"prev"),h(p,"next");else h(t,"prev"),h(p,"next")}}if(r.dynamicBullets){const d=Math.min(l.length,r.dynamicMainBullets+4),n=(p*d-p)/2-e*p,c=t?"right":"left";l.css(o.isHorizontal()?c:"top",n+"px")}}if("fraction"===r.type&&(n.find(P(r.currentClass)).text(r.formatFractionCurrent(i+1)),n.find(P(r.totalClass)).text(r.formatFractionTotal(a))),"progressbar"===r.type){var s=r.progressbarOpposite?o.isHorizontal()?"vertical":"horizontal":o.isHorizontal()?"horizontal":"vertical";const l=(i+1)/a;let e=1,t=1;"horizontal"==s?e=l:t=l,n.find(P(r.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${e}) scaleY(${t})`).transition(o.params.speed)}"custom"===r.type&&r.renderCustom?(n.html(r.renderCustom(o,i+1,a)),d("paginationRender",n[0])):d("paginationUpdate",n[0]),o.params.watchOverflow&&o.enabled&&n[o.isLocked?"addClass":"removeClass"](r.lockClass)}}function i(){var s=o.params.pagination;if(!u()){var e=(o.virtual&&o.params.virtual.enabled?o.virtual:o).slides.length,i=o.pagination.$el;let a="";if("bullets"===s.type){let t=o.params.loop?Math.ceil((e-2*o.loopedSlides)/o.params.slidesPerGroup):o.snapGrid.length;o.params.freeMode&&o.params.freeMode.enabled&&!o.params.loop&&t>e&&(t=e);for(let e=0;e<t;e+=1)s.renderBullet?a+=s.renderBullet.call(o,e,s.bulletClass):a+=`<${s.bulletElement} class="${s.bulletClass}"></${s.bulletElement}>`;i.html(a),o.pagination.bullets=i.find(P(s.bulletClass))}"fraction"===s.type&&(a=s.renderFraction?s.renderFraction.call(o,s.currentClass,s.totalClass):`<span class="${s.currentClass}"></span> / <span class="${s.totalClass}"></span>`,i.html(a)),"progressbar"===s.type&&(a=s.renderProgressbar?s.renderProgressbar.call(o,s.progressbarFillClass):`<span class="${s.progressbarFillClass}"></span>`,i.html(a)),"custom"!==s.type&&d("paginationRender",o.pagination.$el[0])}}function r(){o.params.pagination=$(o,o.originalParams.pagination,o.params.pagination,{el:"swiper-pagination"});var t=o.params.pagination;if(t.el){let e=L(t.el);0!==e.length&&(o.params.uniqueNavElements&&"string"==typeof t.el&&1<e.length&&1<(e=o.$el.find(t.el)).length&&(e=e.filter(e=>L(e).parents(".swiper")[0]===o.el)),"bullets"===t.type&&t.clickable&&e.addClass(t.clickableClass),e.addClass(t.modifierClass+t.type),e.addClass(o.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.addClass(""+t.modifierClass+t.type+"-dynamic"),c=0,t.dynamicMainBullets<1)&&(t.dynamicMainBullets=1),"progressbar"===t.type&&t.progressbarOpposite&&e.addClass(t.progressbarOppositeClass),t.clickable&&e.on("click",P(t.bulletClass),function(e){e.preventDefault();let t=L(this).index()*o.params.slidesPerGroup;o.params.loop&&(t+=o.loopedSlides),o.slideTo(t)}),Object.assign(o.pagination,{$el:e,el:e[0]}),o.enabled||e.addClass(t.lockClass))}}function l(){var e,t=o.params.pagination;u()||((e=o.pagination.$el).removeClass(t.hiddenClass),e.removeClass(t.modifierClass+t.type),e.removeClass(o.isHorizontal()?t.horizontalClass:t.verticalClass),o.pagination.bullets&&o.pagination.bullets.removeClass&&o.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&e.off("click",P(t.bulletClass)))}a("init",()=>{(!1===o.params.pagination.enabled?n:(r(),i(),s))()}),a("activeIndexChange",()=>{!o.params.loop&&void 0!==o.snapIndex||s()}),a("snapIndexChange",()=>{o.params.loop||s()}),a("slidesLengthChange",()=>{o.params.loop&&(i(),s())}),a("snapGridLengthChange",()=>{o.params.loop||(i(),s())}),a("destroy",()=>{l()}),a("enable disable",()=>{var e=o.pagination["$el"];e&&e[o.enabled?"removeClass":"addClass"](o.params.pagination.lockClass)}),a("lock unlock",()=>{s()}),a("click",(e,t)=>{var t=t.target,a=o.pagination["$el"];if(o.params.pagination.el&&o.params.pagination.hideOnClick&&a&&0<a.length&&!L(t).hasClass(o.params.pagination.bulletClass)&&(!o.navigation||!(o.navigation.nextEl&&t===o.navigation.nextEl||o.navigation.prevEl&&t===o.navigation.prevEl))){const e=a.hasClass(o.params.pagination.hiddenClass);d(!0===e?"paginationShow":"paginationHide"),a.toggleClass(o.params.pagination.hiddenClass)}});const n=()=>{o.$el.addClass(o.params.pagination.paginationDisabledClass),o.pagination.$el&&o.pagination.$el.addClass(o.params.pagination.paginationDisabledClass),l()};Object.assign(o.pagination,{enable:()=>{o.$el.removeClass(o.params.pagination.paginationDisabledClass),o.pagination.$el&&o.pagination.$el.removeClass(o.params.pagination.paginationDisabledClass),r(),i(),s()},disable:n,render:i,update:s,init:r,destroy:l})},function(e){let{swiper:o,extendParams:t,on:a,emit:r}=e;const n=T();let l,d,p,s,c=!1,u=null,h=null;function i(){if(o.params.scrollbar.el&&o.scrollbar.el){const{scrollbar:a,rtlTranslate:s,progress:i}=o,{$dragEl:r,$el:l}=a,n=o.params.scrollbar;let e=d,t=(p-d)*i;s?0<(t=-t)?(e=d-t,t=0):-t+d>p&&(e=p+t):t<0?(e=d+t,t=0):t+d>p&&(e=p-t),o.isHorizontal()?(r.transform(`translate3d(${t}px, 0, 0)`),r[0].style.width=e+"px"):(r.transform(`translate3d(0px, ${t}px, 0)`),r[0].style.height=e+"px"),n.hide&&(clearTimeout(u),l[0].style.opacity=1,u=setTimeout(()=>{l[0].style.opacity=0,l.transition(400)},1e3))}}function m(){var e,t,a;o.params.scrollbar.el&&o.scrollbar.el&&(e=o["scrollbar"],{$dragEl:t,$el:a}=e,t[0].style.width="",t[0].style.height="",p=o.isHorizontal()?a[0].offsetWidth:a[0].offsetHeight,s=o.size/(o.virtualSize+o.params.slidesOffsetBefore-(o.params.centeredSlides?o.snapGrid[0]:0)),d="auto"===o.params.scrollbar.dragSize?p*s:parseInt(o.params.scrollbar.dragSize,10),o.isHorizontal()?t[0].style.width=d+"px":t[0].style.height=d+"px",a[0].style.display=1<=s?"none":"",o.params.scrollbar.hide&&(a[0].style.opacity=0),o.params.watchOverflow)&&o.enabled&&e.$el[o.isLocked?"addClass":"removeClass"](o.params.scrollbar.lockClass)}function f(e){return o.isHorizontal()?("touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0]:e).clientX:("touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0]:e).clientY}function v(e){var{scrollbar:t,rtlTranslate:a}=o,t=t["$el"];let s;s=(f(e)-t.offset()[o.isHorizontal()?"left":"top"]-(null!==l?l:d/2))/(p-d),s=Math.max(Math.min(s,1),0),a&&(s=1-s);e=o.minTranslate()+(o.maxTranslate()-o.minTranslate())*s;o.updateProgress(e),o.setTranslate(e),o.updateActiveIndex(),o.updateSlidesClasses()}function g(e){var t=o.params.scrollbar,{scrollbar:a,$wrapperEl:s}=o,{$el:a,$dragEl:i}=a;c=!0,l=e.target===i[0]||e.target===i?f(e)-e.target.getBoundingClientRect()[o.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),s.transition(100),i.transition(100),v(e),clearTimeout(h),a.transition(0),t.hide&&a.css("opacity",1),o.params.cssMode&&o.$wrapperEl.css("scroll-snap-type","none"),r("scrollbarDragStart",e)}function w(e){var{scrollbar:t,$wrapperEl:a}=o,{$el:t,$dragEl:s}=t;c&&(e.preventDefault?e.preventDefault():e.returnValue=!1,v(e),a.transition(0),t.transition(0),s.transition(0),r("scrollbarDragMove",e))}function b(e){const t=o.params.scrollbar,{scrollbar:a,$wrapperEl:s}=o,i=a["$el"];c&&(c=!1,o.params.cssMode&&(o.$wrapperEl.css("scroll-snap-type",""),s.transition("")),t.hide&&(clearTimeout(h),h=S(()=>{i.css("opacity",0),i.transition(400)},1e3)),r("scrollbarDragEnd",e),t.snapOnRelease)&&o.slideToClosest()}function x(e){var t,{scrollbar:a,touchEventsTouch:s,touchEventsDesktop:i,params:r,support:l}=o,a=a.$el;a&&(a=a[0],t=!(!l.passiveListener||!r.passiveListeners)&&{passive:!1,capture:!1},r=!(!l.passiveListener||!r.passiveListeners)&&{passive:!0,capture:!1},a)&&(e="on"===e?"addEventListener":"removeEventListener",l.touch?(a[e](s.start,g,t),a[e](s.move,w,t),a[e](s.end,b,r)):(a[e](i.start,g,t),n[e](i.move,w,t),n[e](i.end,b,r)))}function y(){var{scrollbar:a,$el:s}=o,i=(o.params.scrollbar=$(o,o.originalParams.scrollbar,o.params.scrollbar,{el:"swiper-scrollbar"}),o.params.scrollbar);if(i.el){let e=L(i.el),t=((e=o.params.uniqueNavElements&&"string"==typeof i.el&&1<e.length&&1===s.find(i.el).length?s.find(i.el):e).addClass(o.isHorizontal()?i.horizontalClass:i.verticalClass),e.find("."+o.params.scrollbar.dragClass));0===t.length&&(t=L(`<div class="${o.params.scrollbar.dragClass}"></div>`),e.append(t)),Object.assign(a,{$el:e,el:e[0],$dragEl:t,dragEl:t[0]}),i.draggable&&o.params.scrollbar.el&&o.scrollbar.el&&x("on"),e&&e[o.enabled?"removeClass":"addClass"](o.params.scrollbar.lockClass)}}function E(){var e=o.params.scrollbar,t=o.scrollbar.$el;t&&t.removeClass(o.isHorizontal()?e.horizontalClass:e.verticalClass),o.params.scrollbar.el&&o.scrollbar.el&&x("off")}t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),o.scrollbar={el:null,dragEl:null,$el:null,$dragEl:null},a("init",()=>{(!1===o.params.scrollbar.enabled?C:(y(),m(),i))()}),a("update resize observerUpdate lock unlock",()=>{m()}),a("setTranslate",()=>{i()}),a("setTransition",(e,t)=>{t=t,o.params.scrollbar.el&&o.scrollbar.el&&o.scrollbar.$dragEl.transition(t)}),a("enable disable",()=>{var e=o.scrollbar["$el"];e&&e[o.enabled?"removeClass":"addClass"](o.params.scrollbar.lockClass)}),a("destroy",()=>{E()});const C=()=>{o.$el.addClass(o.params.scrollbar.scrollbarDisabledClass),o.scrollbar.$el&&o.scrollbar.$el.addClass(o.params.scrollbar.scrollbarDisabledClass),E()};Object.assign(o.scrollbar,{enable:()=>{o.$el.removeClass(o.params.scrollbar.scrollbarDisabledClass),o.scrollbar.$el&&o.scrollbar.$el.removeClass(o.params.scrollbar.scrollbarDisabledClass),y(),m(),i()},disable:C,updateSize:m,setTranslate:i,init:y,destroy:E})},function(e){let{swiper:o,extendParams:t,on:a}=e;t({parallax:{enabled:!1}});const r=(e,t)=>{var a=o["rtl"],s=L(e),e=a?-1:1,a=s.attr("data-swiper-parallax")||"0";let i=s.attr("data-swiper-parallax-x"),r=s.attr("data-swiper-parallax-y");var l=s.attr("data-swiper-parallax-scale"),n=s.attr("data-swiper-parallax-opacity");if(i||r?(i=i||"0",r=r||"0"):o.isHorizontal()?(i=a,r="0"):(r=a,i="0"),i=0<=i.indexOf("%")?parseInt(i,10)*t*e+"%":i*t*e+"px",r=0<=r.indexOf("%")?parseInt(r,10)*t+"%":r*t+"px",null!=n){const e=n-(n-1)*(1-Math.abs(t));s[0].style.opacity=e}if(null==l)s.transform(`translate3d(${i}, ${r}, 0px)`);else{const e=l-(l-1)*(1-Math.abs(t));s.transform(`translate3d(${i}, ${r}, 0px) scale(${e})`)}},s=()=>{const{$el:e,slides:t,progress:s,snapGrid:i}=o;e.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(e=>{r(e,s)}),t.each((e,t)=>{let a=e.progress;1<o.params.slidesPerGroup&&"auto"!==o.params.slidesPerView&&(a+=Math.ceil(t/2)-s*(i.length-1)),a=Math.min(Math.max(a,-1),1),L(e).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(e=>{r(e,a)})})};a("beforeInit",()=>{o.params.parallax.enabled&&(o.params.watchSlidesProgress=!0,o.originalParams.watchSlidesProgress=!0)}),a("init",()=>{o.params.parallax.enabled&&s()}),a("setTranslate",()=>{o.params.parallax.enabled&&s()}),a("setTransition",(e,t)=>{var a;o.params.parallax.enabled&&(void 0===(a=t)&&(a=o.params.speed),o.$el.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(e=>{e=L(e);let t=parseInt(e.attr("data-swiper-parallax-duration"),10)||a;0===a&&(t=0),e.transition(t)}))})},function(e){let{swiper:y,extendParams:t,on:a,emit:s}=e;const E=O();t({zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),y.zoom={enabled:!1};let i,r,l,C=1,n=!1;const T={$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},S={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},o={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0};let d=1;function p(e){var t,a,s;return e.targetTouches.length<2?1:(t=e.targetTouches[0].pageX,a=e.targetTouches[0].pageY,s=e.targetTouches[1].pageX,e=e.targetTouches[1].pageY,Math.sqrt((s-t)**2+(e-a)**2))}function c(e){var t=y.support,a=y.params.zoom;if(r=!1,l=!1,!t.gestures){if("touchstart"!==e.type||"touchstart"===e.type&&e.targetTouches.length<2)return;r=!0,T.scaleStart=p(e)}T.$slideEl&&T.$slideEl.length||(T.$slideEl=L(e.target).closest("."+y.params.slideClass),0===T.$slideEl.length&&(T.$slideEl=y.slides.eq(y.activeIndex)),T.$imageEl=T.$slideEl.find("."+a.containerClass).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),T.$imageWrapEl=T.$imageEl.parent("."+a.containerClass),T.maxRatio=T.$imageWrapEl.attr("data-swiper-zoom")||a.maxRatio,0!==T.$imageWrapEl.length)?(T.$imageEl&&T.$imageEl.transition(0),n=!0):T.$imageEl=void 0}function u(e){var t=y.support,a=y.params.zoom,s=y.zoom;if(!t.gestures){if("touchmove"!==e.type||"touchmove"===e.type&&e.targetTouches.length<2)return;l=!0,T.scaleMove=p(e)}T.$imageEl&&0!==T.$imageEl.length?(t.gestures?s.scale=e.scale*C:s.scale=T.scaleMove/T.scaleStart*C,s.scale>T.maxRatio&&(s.scale=T.maxRatio-1+(s.scale-T.maxRatio+1)**.5),s.scale<a.minRatio&&(s.scale=a.minRatio+1-(a.minRatio-s.scale+1)**.5),T.$imageEl.transform(`translate3d(0,0,0) scale(${s.scale})`)):"gesturechange"===e.type&&c(e)}function h(e){var t=y.device,a=y.support,s=y.params.zoom,i=y.zoom;if(!a.gestures){if(!r||!l)return;if("touchend"!==e.type||"touchend"===e.type&&e.changedTouches.length<2&&!t.android)return;r=!1,l=!1}T.$imageEl&&0!==T.$imageEl.length&&(i.scale=Math.max(Math.min(i.scale,T.maxRatio),s.minRatio),T.$imageEl.transition(y.params.speed).transform(`translate3d(0,0,0) scale(${i.scale})`),C=i.scale,n=!1,1===i.scale)&&(T.$slideEl=void 0)}function m(e){var t=y.zoom;if(T.$imageEl&&0!==T.$imageEl.length&&(y.allowClick=!1,S.isTouched)&&T.$slideEl){S.isMoved||(S.width=T.$imageEl[0].offsetWidth,S.height=T.$imageEl[0].offsetHeight,S.startX=I(T.$imageWrapEl[0],"x")||0,S.startY=I(T.$imageWrapEl[0],"y")||0,T.slideWidth=T.$slideEl[0].offsetWidth,T.slideHeight=T.$slideEl[0].offsetHeight,T.$imageWrapEl.transition(0));var a=S.width*t.scale,t=S.height*t.scale;if(!(a<T.slideWidth&&t<T.slideHeight)){if(S.minX=Math.min(T.slideWidth/2-a/2,0),S.maxX=-S.minX,S.minY=Math.min(T.slideHeight/2-t/2,0),S.maxY=-S.minY,S.touchesCurrent.x=("touchmove"===e.type?e.targetTouches[0]:e).pageX,S.touchesCurrent.y=("touchmove"===e.type?e.targetTouches[0]:e).pageY,!S.isMoved&&!n){if(y.isHorizontal()&&(Math.floor(S.minX)===Math.floor(S.startX)&&S.touchesCurrent.x<S.touchesStart.x||Math.floor(S.maxX)===Math.floor(S.startX)&&S.touchesCurrent.x>S.touchesStart.x))return void(S.isTouched=!1);if(!y.isHorizontal()&&(Math.floor(S.minY)===Math.floor(S.startY)&&S.touchesCurrent.y<S.touchesStart.y||Math.floor(S.maxY)===Math.floor(S.startY)&&S.touchesCurrent.y>S.touchesStart.y))return void(S.isTouched=!1)}e.cancelable&&e.preventDefault(),e.stopPropagation(),S.isMoved=!0,S.currentX=S.touchesCurrent.x-S.touchesStart.x+S.startX,S.currentY=S.touchesCurrent.y-S.touchesStart.y+S.startY,S.currentX<S.minX&&(S.currentX=S.minX+1-(S.minX-S.currentX+1)**.8),S.currentX>S.maxX&&(S.currentX=S.maxX-1+(S.currentX-S.maxX+1)**.8),S.currentY<S.minY&&(S.currentY=S.minY+1-(S.minY-S.currentY+1)**.8),S.currentY>S.maxY&&(S.currentY=S.maxY-1+(S.currentY-S.maxY+1)**.8),o.prevPositionX||(o.prevPositionX=S.touchesCurrent.x),o.prevPositionY||(o.prevPositionY=S.touchesCurrent.y),o.prevTime||(o.prevTime=Date.now()),o.x=(S.touchesCurrent.x-o.prevPositionX)/(Date.now()-o.prevTime)/2,o.y=(S.touchesCurrent.y-o.prevPositionY)/(Date.now()-o.prevTime)/2,Math.abs(S.touchesCurrent.x-o.prevPositionX)<2&&(o.x=0),Math.abs(S.touchesCurrent.y-o.prevPositionY)<2&&(o.y=0),o.prevPositionX=S.touchesCurrent.x,o.prevPositionY=S.touchesCurrent.y,o.prevTime=Date.now(),T.$imageWrapEl.transform(`translate3d(${S.currentX}px, ${S.currentY}px,0)`)}}}function f(){var e=y.zoom;T.$slideEl&&y.previousIndex!==y.activeIndex&&(T.$imageEl&&T.$imageEl.transform("translate3d(0,0,0) scale(1)"),T.$imageWrapEl&&T.$imageWrapEl.transform("translate3d(0,0,0)"),e.scale=1,C=1,T.$slideEl=void 0,T.$imageEl=void 0,T.$imageWrapEl=void 0)}function v(w){var b=y.zoom,x=y.params.zoom;if(T.$slideEl||(w&&w.target&&(T.$slideEl=L(w.target).closest("."+y.params.slideClass)),T.$slideEl||(y.params.virtual&&y.params.virtual.enabled&&y.virtual?T.$slideEl=y.$wrapperEl.children("."+y.params.slideActiveClass):T.$slideEl=y.slides.eq(y.activeIndex)),T.$imageEl=T.$slideEl.find("."+x.containerClass).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),T.$imageWrapEl=T.$imageEl.parent("."+x.containerClass)),T.$imageEl&&0!==T.$imageEl.length&&T.$imageWrapEl&&0!==T.$imageWrapEl.length){let e,t,a,s,i,r,l,n,o,d,p,c,u,h,m,f,v,g;y.params.cssMode&&(y.wrapperEl.style.overflow="hidden",y.wrapperEl.style.touchAction="none"),T.$slideEl.addClass(""+x.zoomedSlideClass),t=void 0===S.touchesStart.x&&w?(e=("touchend"===w.type?w.changedTouches[0]:w).pageX,("touchend"===w.type?w.changedTouches[0]:w).pageY):(e=S.touchesStart.x,S.touchesStart.y),b.scale=T.$imageWrapEl.attr("data-swiper-zoom")||x.maxRatio,C=T.$imageWrapEl.attr("data-swiper-zoom")||x.maxRatio,w?(v=T.$slideEl[0].offsetWidth,g=T.$slideEl[0].offsetHeight,a=T.$slideEl.offset().left+E.scrollX,s=T.$slideEl.offset().top+E.scrollY,i=a+v/2-e,r=s+g/2-t,o=T.$imageEl[0].offsetWidth,d=T.$imageEl[0].offsetHeight,p=o*b.scale,c=d*b.scale,m=-(u=Math.min(v/2-p/2,0)),f=-(h=Math.min(g/2-c/2,0)),l=i*b.scale,n=r*b.scale,(l=l<u?u:l)>m&&(l=m),(n=n<h?h:n)>f&&(n=f)):(l=0,n=0),T.$imageWrapEl.transition(300).transform(`translate3d(${l}px, ${n}px,0)`),T.$imageEl.transition(300).transform(`translate3d(0,0,0) scale(${b.scale})`)}}function g(){var e=y.zoom,t=y.params.zoom;T.$slideEl||(y.params.virtual&&y.params.virtual.enabled&&y.virtual?T.$slideEl=y.$wrapperEl.children("."+y.params.slideActiveClass):T.$slideEl=y.slides.eq(y.activeIndex),T.$imageEl=T.$slideEl.find("."+t.containerClass).eq(0).find("picture, img, svg, canvas, .swiper-zoom-target").eq(0),T.$imageWrapEl=T.$imageEl.parent("."+t.containerClass)),T.$imageEl&&0!==T.$imageEl.length&&T.$imageWrapEl&&0!==T.$imageWrapEl.length&&(y.params.cssMode&&(y.wrapperEl.style.overflow="",y.wrapperEl.style.touchAction=""),e.scale=1,C=1,T.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),T.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),T.$slideEl.removeClass(""+t.zoomedSlideClass),T.$slideEl=void 0)}function w(e){var t=y.zoom;t.scale&&1!==t.scale?g():v(e)}function b(){var e=y.support;return{passiveListener:!("touchstart"!==y.touchEvents.start||!e.passiveListener||!y.params.passiveListeners)&&{passive:!0,capture:!1},activeListenerWithCapture:!e.passiveListener||{passive:!1,capture:!0}}}function x(){return"."+y.params.slideClass}function M(e){var t=b()["passiveListener"],a=x();y.$wrapperEl[e]("gesturestart",a,c,t),y.$wrapperEl[e]("gesturechange",a,u,t),y.$wrapperEl[e]("gestureend",a,h,t)}function $(){i||(i=!0,M("on"))}function P(){i&&(i=!1,M("off"))}function k(){var e,t,a,s=y.zoom;s.enabled||(s.enabled=!0,s=y.support,{passiveListener:e,activeListenerWithCapture:t}=b(),a=x(),s.gestures?(y.$wrapperEl.on(y.touchEvents.start,$,e),y.$wrapperEl.on(y.touchEvents.end,P,e)):"touchstart"===y.touchEvents.start&&(y.$wrapperEl.on(y.touchEvents.start,a,c,e),y.$wrapperEl.on(y.touchEvents.move,a,u,t),y.$wrapperEl.on(y.touchEvents.end,a,h,e),y.touchEvents.cancel)&&y.$wrapperEl.on(y.touchEvents.cancel,a,h,e),y.$wrapperEl.on(y.touchEvents.move,"."+y.params.zoom.containerClass,m,t))}function z(){var e,t,a,s=y.zoom;s.enabled&&(e=y.support,{passiveListener:s,activeListenerWithCapture:t}=(s.enabled=!1,b()),a=x(),e.gestures?(y.$wrapperEl.off(y.touchEvents.start,$,s),y.$wrapperEl.off(y.touchEvents.end,P,s)):"touchstart"===y.touchEvents.start&&(y.$wrapperEl.off(y.touchEvents.start,a,c,s),y.$wrapperEl.off(y.touchEvents.move,a,u,t),y.$wrapperEl.off(y.touchEvents.end,a,h,s),y.touchEvents.cancel)&&y.$wrapperEl.off(y.touchEvents.cancel,a,h,s),y.$wrapperEl.off(y.touchEvents.move,"."+y.params.zoom.containerClass,m,t))}Object.defineProperty(y.zoom,"scale",{get:()=>d,set(e){var t,a;d!==e&&(t=T.$imageEl?T.$imageEl[0]:void 0,a=T.$slideEl?T.$slideEl[0]:void 0,s("zoomChange",e,t,a)),d=e}}),a("init",()=>{y.params.zoom.enabled&&k()}),a("destroy",()=>{z()}),a("touchStart",(e,t)=>{var a;y.zoom.enabled&&(t=t,a=y.device,T.$imageEl)&&0!==T.$imageEl.length&&!S.isTouched&&(a.android&&t.cancelable&&t.preventDefault(),S.isTouched=!0,S.touchesStart.x=("touchstart"===t.type?t.targetTouches[0]:t).pageX,S.touchesStart.y=("touchstart"===t.type?t.targetTouches[0]:t).pageY)}),a("touchEnd",(e,t)=>{if(y.zoom.enabled){var a=y.zoom;if(T.$imageEl&&0!==T.$imageEl.length){if(!S.isTouched||!S.isMoved)return void(S.isTouched=!1,S.isMoved=!1);S.isTouched=!1,S.isMoved=!1;let e=300,t=300;var s=o.x*e,s=S.currentX+s,i=o.y*t,i=S.currentY+i,r=(0!==o.x&&(e=Math.abs((s-S.currentX)/o.x)),0!==o.y&&(t=Math.abs((i-S.currentY)/o.y)),Math.max(e,t)),s=(S.currentX=s,S.currentY=i,S.width*a.scale),i=S.height*a.scale;S.minX=Math.min(T.slideWidth/2-s/2,0),S.maxX=-S.minX,S.minY=Math.min(T.slideHeight/2-i/2,0),S.maxY=-S.minY,S.currentX=Math.max(Math.min(S.currentX,S.maxX),S.minX),S.currentY=Math.max(Math.min(S.currentY,S.maxY),S.minY),T.$imageWrapEl.transition(r).transform(`translate3d(${S.currentX}px, ${S.currentY}px,0)`)}}}),a("doubleTap",(e,t)=>{!y.animating&&y.params.zoom.enabled&&y.zoom.enabled&&y.params.zoom.toggle&&w(t)}),a("transitionEnd",()=>{y.zoom.enabled&&y.params.zoom.enabled&&f()}),a("slideChange",()=>{y.zoom.enabled&&y.params.zoom.enabled&&y.params.cssMode&&f()}),Object.assign(y.zoom,{enable:k,disable:z,in:v,out:g,toggle:w})},function(e){let{swiper:p,extendParams:t,on:a,emit:c}=e,n=(t({lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:"",elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}}),!(p.lazy={})),d=!1;function u(e,n){void 0===n&&(n=!0);const o=p.params.lazy;if(void 0!==e&&0!==p.slides.length){const d=p.virtual&&p.params.virtual.enabled?p.$wrapperEl.children(`.${p.params.slideClass}[data-swiper-slide-index="${e}"]`):p.slides.eq(e),t=d.find(`.${o.elementClass}:not(.${o.loadedClass}):not(.${o.loadingClass})`);!d.hasClass(o.elementClass)||d.hasClass(o.loadedClass)||d.hasClass(o.loadingClass)||t.push(d[0]),0!==t.length&&t.each(e=>{const t=L(e),a=(t.addClass(o.loadingClass),t.attr("data-background")),s=t.attr("data-src"),i=t.attr("data-srcset"),r=t.attr("data-sizes"),l=t.parent("picture");p.loadImage(t[0],s||a,i,r,!1,()=>{var e;null==p||!p||p&&!p.params||p.destroyed||(a?(t.css("background-image",`url("${a}")`),t.removeAttr("data-background")):(i&&(t.attr("srcset",i),t.removeAttr("data-srcset")),r&&(t.attr("sizes",r),t.removeAttr("data-sizes")),l.length&&l.children("source").each(e=>{e=L(e);e.attr("data-srcset")&&(e.attr("srcset",e.attr("data-srcset")),e.removeAttr("data-srcset"))}),s&&(t.attr("src",s),t.removeAttr("data-src"))),t.addClass(o.loadedClass).removeClass(o.loadingClass),d.find("."+o.preloaderClass).remove(),p.params.loop&&n&&(e=d.attr("data-swiper-slide-index"),d.hasClass(p.params.slideDuplicateClass)?u(p.$wrapperEl.children(`[data-swiper-slide-index="${e}"]:not(.${p.params.slideDuplicateClass})`).index(),!1):u(p.$wrapperEl.children(`.${p.params.slideDuplicateClass}[data-swiper-slide-index="${e}"]`).index(),!1)),c("lazyImageReady",d[0],t[0]),p.params.autoHeight&&p.updateAutoHeight())}),c("lazyImageLoad",d[0],t[0])})}}function o(){const{$wrapperEl:t,params:a,slides:s,activeIndex:i}=p,r=p.virtual&&a.virtual.enabled,e=a.lazy;let l=a.slidesPerView;function n(e){if(r){if(t.children(`.${a.slideClass}[data-swiper-slide-index="${e}"]`).length)return 1}else if(s[e])return 1}function o(e){return r?L(e).attr("data-swiper-slide-index"):L(e).index()}if("auto"===l&&(l=0),d=d||!0,p.params.watchSlidesProgress)t.children("."+a.slideVisibleClass).each(e=>{u(r?L(e).attr("data-swiper-slide-index"):L(e).index())});else if(1<l)for(let e=i;e<i+l;e+=1)n(e)&&u(e);else u(i);if(e.loadPrevNext)if(1<l||e.loadPrevNextAmount&&1<e.loadPrevNextAmount){const t=e.loadPrevNextAmount,p=Math.ceil(l),a=Math.min(i+p+Math.max(t,p),s.length),r=Math.max(i-Math.max(p,t),0);for(let e=i+p;e<a;e+=1)n(e)&&u(e);for(let e=r;e<i;e+=1)n(e)&&u(e)}else{const p=t.children("."+a.slideNextClass),s=(0<p.length&&u(o(p)),t.children("."+a.slidePrevClass));0<s.length&&u(o(s))}}function h(){var e=O();if(p&&!p.destroyed){var a=p.params.lazy.scrollingElement?L(p.params.lazy.scrollingElement):L(e),s=a[0]===e,i=s?e.innerWidth:a[0].offsetWidth,r=s?e.innerHeight:a[0].offsetHeight,s=p.$el.offset(),e=p["rtlTranslate"];let t=!1;e&&(s.left-=p.$el[0].scrollLeft);var l=[[s.left,s.top],[s.left+p.width,s.top],[s.left,s.top+p.height],[s.left+p.width,s.top+p.height]];for(let e=0;e<l.length;e+=1){const p=l[e];0<=p[0]&&p[0]<=i&&0<=p[1]&&p[1]<=r&&(0===p[0]&&0===p[1]||(t=!0))}e=!("touchstart"!==p.touchEvents.start||!p.support.passiveListener||!p.params.passiveListeners)&&{passive:!0,capture:!1};t?(o(),a.off("scroll",h,e)):n||(n=!0,a.on("scroll",h,e))}}a("beforeInit",()=>{p.params.lazy.enabled&&p.params.preloadImages&&(p.params.preloadImages=!1)}),a("init",()=>{p.params.lazy.enabled&&(p.params.lazy.checkInView?h:o)()}),a("scroll",()=>{p.params.freeMode&&p.params.freeMode.enabled&&!p.params.freeMode.sticky&&o()}),a("scrollbarDragMove resize _freeModeNoMomentumRelease",()=>{p.params.lazy.enabled&&(p.params.lazy.checkInView?h:o)()}),a("transitionStart",()=>{p.params.lazy.enabled&&(p.params.lazy.loadOnTransitionStart||!p.params.lazy.loadOnTransitionStart&&!d)&&(p.params.lazy.checkInView?h:o)()}),a("transitionEnd",()=>{p.params.lazy.enabled&&!p.params.lazy.loadOnTransitionStart&&(p.params.lazy.checkInView?h:o)()}),a("slideChange",()=>{var{lazy:e,cssMode:t,watchSlidesProgress:a,touchReleaseOnEdges:s,resistanceRatio:i}=p.params;e.enabled&&(t||a&&(s||0===i))&&o()}),a("destroy",()=>{p.$el&&p.$el.find("."+p.params.lazy.loadingClass).removeClass(p.params.lazy.loadingClass)}),Object.assign(p.lazy,{load:o,loadInSlide:u})},function(e){let{swiper:n,extendParams:t,on:a}=e;function o(e,t){const a=function(){let a,s,i;return(e,t)=>{for(s=-1,a=e.length;1<a-s;)e[i=a+s>>1]<=t?s=i:a=i;return a}}();let s,i;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(i=a(this.x,e),s=i-1,(e-this.x[s])*(this.y[i]-this.y[s])/(this.x[i]-this.x[s])+this.y[s]):0},this}function s(){n.controller.control&&n.controller.spline&&(n.controller.spline=void 0,delete n.controller.spline)}t({controller:{control:void 0,inverse:!1,by:"slide"}}),n.controller={control:void 0},a("beforeInit",()=>{n.controller.control=n.params.controller.control}),a("update",()=>{s()}),a("resize",()=>{s()}),a("observerUpdate",()=>{s()}),a("setTranslate",(e,t,a)=>{n.controller.control&&n.controller.setTranslate(t,a)}),a("setTransition",(e,t,a)=>{n.controller.control&&n.controller.setTransition(t,a)}),Object.assign(n.controller,{setTranslate:function(e,t){var a=n.controller.control;let s,i;var r=n.constructor;function l(e){var t,a=n.rtlTranslate?-n.translate:n.translate;"slide"===n.params.controller.by&&(t=e,n.controller.spline||(n.controller.spline=n.params.loop?new o(n.slidesGrid,t.slidesGrid):new o(n.snapGrid,t.snapGrid)),i=-n.controller.spline.interpolate(-a)),i&&"container"!==n.params.controller.by||(s=(e.maxTranslate()-e.minTranslate())/(n.maxTranslate()-n.minTranslate()),i=(a-n.minTranslate())*s+e.minTranslate()),n.params.controller.inverse&&(i=e.maxTranslate()-i),e.updateProgress(i),e.setTranslate(i,n),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(a))for(let e=0;e<a.length;e+=1)a[e]!==t&&a[e]instanceof r&&l(a[e]);else a instanceof r&&t!==a&&l(a)},setTransition:function(t,e){const a=n.constructor,s=n.controller.control;let i;function r(e){e.setTransition(t,n),0!==t&&(e.transitionStart(),e.params.autoHeight&&S(()=>{e.updateAutoHeight()}),e.$wrapperEl.transitionEnd(()=>{s&&(e.params.loop&&"slide"===n.params.controller.by&&e.loopFix(),e.transitionEnd())}))}if(Array.isArray(s))for(i=0;i<s.length;i+=1)s[i]!==e&&s[i]instanceof a&&r(s[i]);else s instanceof a&&e!==s&&r(s)}})},function(e){let{swiper:l,extendParams:t,on:a}=e,n=(t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}}),null);function s(e){var t=n;0!==t.length&&(t.html(""),t.html(e))}function i(e){e.attr("tabIndex","0")}function r(e){e.attr("tabIndex","-1")}function o(e,t){e.attr("role",t)}function d(e,t){e.attr("aria-roledescription",t)}function p(e,t){e.attr("aria-label",t)}function c(e){e.attr("aria-disabled",!0)}function u(e){e.attr("aria-disabled",!1)}function h(e){var t;13!==e.keyCode&&32!==e.keyCode||(t=l.params.a11y,e=L(e.target),l.navigation&&l.navigation.$nextEl&&e.is(l.navigation.$nextEl)&&(l.isEnd&&!l.params.loop||l.slideNext(),l.isEnd?s(t.lastSlideMessage):s(t.nextSlideMessage)),l.navigation&&l.navigation.$prevEl&&e.is(l.navigation.$prevEl)&&(l.isBeginning&&!l.params.loop||l.slidePrev(),l.isBeginning?s(t.firstSlideMessage):s(t.prevSlideMessage)),l.pagination&&e.is(P(l.params.pagination.bulletClass))&&e[0].click())}function m(){return l.pagination&&l.pagination.bullets&&l.pagination.bullets.length}function f(){return m()&&l.params.pagination.clickable}const v=(e,t,a)=>{i(e),"BUTTON"!==e[0].tagName&&(o(e,"button"),e.on("keydown",h)),p(e,a),e.attr("aria-controls",t)},g=e=>{var t,a,e=e.target.closest("."+l.params.slideClass);e&&l.slides.includes(e)&&(t=l.slides.indexOf(e)===l.activeIndex,a=l.params.watchSlidesProgress&&l.visibleSlides&&l.visibleSlides.includes(e),t||a||l.slideTo(l.slides.indexOf(e),0))},w=()=>{const a=l.params.a11y,s=(a.itemRoleDescriptionMessage&&d(L(l.slides),a.itemRoleDescriptionMessage),a.slideRole&&o(L(l.slides),a.slideRole),(l.params.loop?l.slides.filter(e=>!e.classList.contains(l.params.slideDuplicateClass)):l.slides).length);a.slideLabelMessage&&l.slides.each((e,t)=>{e=L(e),t=l.params.loop?parseInt(e.attr("data-swiper-slide-index"),10):t;p(e,a.slideLabelMessage.replace(/\{\{index\}\}/,t+1).replace(/\{\{slidesLength\}\}/,s))})};a("beforeInit",()=>{n=L(`<span class="${l.params.a11y.notificationClass}" aria-live="assertive" aria-atomic="true"></span>`)}),a("afterInit",()=>{if(l.params.a11y.enabled){var a=l.params.a11y,s=(l.$el.append(n),l.$el),s=(a.containerRoleDescriptionMessage&&d(s,a.containerRoleDescriptionMessage),a.containerMessage&&p(s,a.containerMessage),l.$wrapperEl),i=a.id||s.attr("id")||"swiper-wrapper-"+"x".repeat(i=void 0===(i=16)?16:i).replace(/x/g,()=>Math.round(16*Math.random()).toString(16)),r=l.params.autoplay&&l.params.autoplay.enabled?"off":"polite";let e,t;s.attr("id",i),s.attr("aria-live",r),w(),l.navigation&&l.navigation.$nextEl&&(e=l.navigation.$nextEl),l.navigation&&l.navigation.$prevEl&&(t=l.navigation.$prevEl),e&&e.length&&v(e,i,a.nextSlideMessage),t&&t.length&&v(t,i,a.prevSlideMessage),f()&&l.pagination.$el.on("keydown",P(l.params.pagination.bulletClass),h),l.$el.on("focus",g,!0)}}),a("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{l.params.a11y.enabled&&w()}),a("fromEdge toEdge afterInit lock unlock",()=>{var e,t;l.params.a11y.enabled&&!l.params.loop&&!l.params.rewind&&l.navigation&&({$nextEl:e,$prevEl:t}=l.navigation,t&&0<t.length&&(l.isBeginning?(c(t),r):(u(t),i))(t),e&&0<e.length)&&(l.isEnd?(c(e),r):(u(e),i))(e)}),a("paginationUpdate",()=>{if(l.params.a11y.enabled){const t=l.params.a11y;m()&&l.pagination.bullets.each(e=>{e=L(e);l.params.pagination.clickable&&(i(e),l.params.pagination.renderBullet||(o(e,"button"),p(e,t.paginationBulletMessage.replace(/\{\{index\}\}/,e.index()+1)))),e.is("."+l.params.pagination.bulletActiveClass)?e.attr("aria-current","true"):e.removeAttr("aria-current")})}}),a("destroy",()=>{if(l.params.a11y.enabled){let e,t;n&&0<n.length&&n.remove(),l.navigation&&l.navigation.$nextEl&&(e=l.navigation.$nextEl),l.navigation&&l.navigation.$prevEl&&(t=l.navigation.$prevEl),e&&e.off("keydown",h),t&&t.off("keydown",h),f()&&l.pagination.$el.off("keydown",P(l.params.pagination.bulletClass),h),l.$el.off("focus",g,!0)}})},function(e){let{swiper:l,extendParams:t,on:a}=e,r=(t({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}}),!1),s={};const n=e=>e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,""),i=e=>{var t=O(),e=(e?new URL(e):t.location).pathname.slice(1).split("/").filter(e=>""!==e),t=e.length;return{key:e[t-2],value:e[t-1]}},o=(a,e)=>{var s=O();if(r&&l.params.history.enabled){var i=l.params.url?new URL(l.params.url):s.location,e=l.slides.eq(e);let t=n(e.attr("data-history"));if(0<l.params.history.root.length){let e=l.params.history.root;"/"===e[e.length-1]&&(e=e.slice(0,e.length-1)),t=e+`/${a}/`+t}else i.pathname.includes(a)||(t=a+"/"+t);l.params.history.keepQuery&&(t+=i.search);e=s.history.state;e&&e.value===t||(l.params.history.replaceState?s.history.replaceState({value:t},null,t):s.history.pushState({value:t},null,t))}},d=(a,s,i)=>{if(s)for(let e=0,t=l.slides.length;e<t;e+=1){var r=l.slides.eq(e);if(n(r.attr("data-history"))===s&&!r.hasClass(l.params.slideDuplicateClass)){const s=r.index();l.slideTo(s,a,i)}}else l.slideTo(0,a,i)},p=()=>{s=i(l.params.url),d(l.params.speed,s.value,!1)};a("init",()=>{var e;l.params.history.enabled&&(e=O(),l.params.history)&&(e.history&&e.history.pushState?(r=!0,((s=i(l.params.url)).key||s.value)&&(d(0,s.value,l.params.runCallbacksOnInit),l.params.history.replaceState||e.addEventListener("popstate",p))):(l.params.history.enabled=!1,l.params.hashNavigation.enabled=!0))}),a("destroy",()=>{var e;l.params.history.enabled&&(e=O(),l.params.history.replaceState||e.removeEventListener("popstate",p))}),a("transitionEnd _freeModeNoMomentumRelease",()=>{r&&o(l.params.history.key,l.activeIndex)}),a("slideChange",()=>{r&&l.params.cssMode&&o(l.params.history.key,l.activeIndex)})},function(e){let{swiper:i,extendParams:t,emit:a,on:s}=e,r=!1;const l=T(),n=O(),o=(t({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}}),()=>{a("hashChange");var e=l.location.hash.replace("#","");e!==i.slides.eq(i.activeIndex).attr("data-hash")&&void 0!==(e=i.$wrapperEl.children(`.${i.params.slideClass}[data-hash="${e}"]`).index())&&i.slideTo(e)}),d=()=>{var e;r&&i.params.hashNavigation.enabled&&(i.params.hashNavigation.replaceState&&n.history&&n.history.replaceState?n.history.replaceState(null,null,"#"+i.slides.eq(i.activeIndex).attr("data-hash")||""):(e=(e=i.slides.eq(i.activeIndex)).attr("data-hash")||e.attr("data-history"),l.location.hash=e||""),a("hashSet"))};s("init",()=>{if(i.params.hashNavigation.enabled&&!(!i.params.hashNavigation.enabled||i.params.history&&i.params.history.enabled)){r=!0;const s=l.location.hash.replace("#","");if(s)for(let e=0,t=i.slides.length;e<t;e+=1){var a=i.slides.eq(e);if((a.attr("data-hash")||a.attr("data-history"))===s&&!a.hasClass(i.params.slideDuplicateClass)){const s=a.index();i.slideTo(s,0,i.params.runCallbacksOnInit,!0)}}i.params.hashNavigation.watchState&&L(n).on("hashchange",o)}}),s("destroy",()=>{i.params.hashNavigation.enabled&&i.params.hashNavigation.watchState&&L(n).off("hashchange",o)}),s("transitionEnd _freeModeNoMomentumRelease",()=>{r&&d()}),s("slideChange",()=>{r&&i.params.cssMode&&d()})},function(e){let a,{swiper:s,extendParams:t,on:i,emit:r}=e;function l(){var e=s.slides.eq(s.activeIndex);let t=s.params.autoplay.delay;e.attr("data-swiper-autoplay")&&(t=e.attr("data-swiper-autoplay")||s.params.autoplay.delay),clearTimeout(a),a=S(()=>{let e;s.params.autoplay.reverseDirection?s.params.loop?(s.loopFix(),e=s.slidePrev(s.params.speed,!0,!0),r("autoplay")):s.isBeginning?s.params.autoplay.stopOnLastSlide?o():(e=s.slideTo(s.slides.length-1,s.params.speed,!0,!0),r("autoplay")):(e=s.slidePrev(s.params.speed,!0,!0),r("autoplay")):s.params.loop?(s.loopFix(),e=s.slideNext(s.params.speed,!0,!0),r("autoplay")):s.isEnd?s.params.autoplay.stopOnLastSlide?o():(e=s.slideTo(0,s.params.speed,!0,!0),r("autoplay")):(e=s.slideNext(s.params.speed,!0,!0),r("autoplay")),(s.params.cssMode&&s.autoplay.running||!1===e)&&l()},t)}function n(){return void 0===a&&!s.autoplay.running&&(s.autoplay.running=!0,r("autoplayStart"),l(),!0)}function o(){return!!s.autoplay.running&&void 0!==a&&(a&&(clearTimeout(a),a=void 0),s.autoplay.running=!1,r("autoplayStop"),!0)}function d(e){!s.autoplay.running||s.autoplay.paused||(a&&clearTimeout(a),s.autoplay.paused=!0,0!==e&&s.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach(e=>{s.$wrapperEl[0].addEventListener(e,c)}):(s.autoplay.paused=!1,l()))}function p(){var e=T();"hidden"===e.visibilityState&&s.autoplay.running&&d(),"visible"===e.visibilityState&&s.autoplay.paused&&(l(),s.autoplay.paused=!1)}function c(e){s&&!s.destroyed&&s.$wrapperEl&&e.target===s.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach(e=>{s.$wrapperEl[0].removeEventListener(e,c)}),s.autoplay.paused=!1,(s.autoplay.running?l:o)())}function u(){(s.params.autoplay.disableOnInteraction?o:(r("autoplayPause"),d))(),["transitionend","webkitTransitionEnd"].forEach(e=>{s.$wrapperEl[0].removeEventListener(e,c)})}function h(){s.params.autoplay.disableOnInteraction||(s.autoplay.paused=!1,r("autoplayResume"),l())}s.autoplay={running:!1,paused:!1},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}}),i("init",()=>{s.params.autoplay.enabled&&(n(),T().addEventListener("visibilitychange",p),s.params.autoplay.pauseOnMouseEnter)&&(s.$el.on("mouseenter",u),s.$el.on("mouseleave",h))}),i("beforeTransitionStart",(e,t,a)=>{s.autoplay.running&&(a||!s.params.autoplay.disableOnInteraction?s.autoplay.pause(t):o())}),i("sliderFirstMove",()=>{s.autoplay.running&&(s.params.autoplay.disableOnInteraction?o:d)()}),i("touchEnd",()=>{s.params.cssMode&&s.autoplay.paused&&!s.params.autoplay.disableOnInteraction&&l()}),i("destroy",()=>{s.$el.off("mouseenter",u),s.$el.off("mouseleave",h),s.autoplay.running&&o(),T().removeEventListener("visibilitychange",p)}),Object.assign(s.autoplay,{pause:d,run:l,start:n,stop:o})},function(e){let{swiper:o,extendParams:t,on:a}=e,s=(t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}}),!1),i=!1;function r(){var e=o.thumbs.swiper;if(e&&!e.destroyed){const a=e.clickedIndex,s=e.clickedSlide;if(!(s&&L(s).hasClass(o.params.thumbs.slideThumbActiveClass)||null==a)){let t;if(t=e.params.loop?parseInt(L(e.clickedSlide).attr("data-swiper-slide-index"),10):a,o.params.loop){let e=o.activeIndex;o.slides.eq(e).hasClass(o.params.slideDuplicateClass)&&(o.loopFix(),o._clientLeft=o.$wrapperEl[0].clientLeft,e=o.activeIndex);const a=o.slides.eq(e).prevAll(`[data-swiper-slide-index="${t}"]`).eq(0).index(),s=o.slides.eq(e).nextAll(`[data-swiper-slide-index="${t}"]`).eq(0).index();t=void 0===a||void 0!==s&&s-e<e-a?s:a}o.slideTo(t)}}}function l(){var e=o.params["thumbs"];if(s)return!1;s=!0;var t=o.constructor;return e.swiper instanceof t?(o.thumbs.swiper=e.swiper,Object.assign(o.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(o.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1})):d(e.swiper)&&(e=Object.assign({},e.swiper),Object.assign(e,{watchSlidesProgress:!0,slideToClickedSlide:!1}),o.thumbs.swiper=new t(e),i=!0),o.thumbs.swiper.$el.addClass(o.params.thumbs.thumbsContainerClass),o.thumbs.swiper.on("tap",r),!0}function n(s){var i=o.thumbs.swiper;if(i&&!i.destroyed){const n="auto"===i.params.slidesPerView?i.slidesPerViewDynamic():i.params.slidesPerView;let t=1;var a=o.params.thumbs.slideThumbActiveClass;if(1<o.params.slidesPerView&&!o.params.centeredSlides&&(t=o.params.slidesPerView),o.params.thumbs.multipleActiveThumbs||(t=1),t=Math.floor(t),i.slides.removeClass(a),i.params.loop||i.params.virtual&&i.params.virtual.enabled)for(let e=0;e<t;e+=1)i.$wrapperEl.children(`[data-swiper-slide-index="${o.realIndex+e}"]`).addClass(a);else for(let e=0;e<t;e+=1)i.slides.eq(o.realIndex+e).addClass(a);var r=o.params.thumbs.autoScrollOffset,l=r&&!i.params.loop;if(o.realIndex!==i.realIndex||l){let e,t,a=i.activeIndex;if(i.params.loop){i.slides.eq(a).hasClass(i.params.slideDuplicateClass)&&(i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft,a=i.activeIndex);const s=i.slides.eq(a).prevAll(`[data-swiper-slide-index="${o.realIndex}"]`).eq(0).index(),n=i.slides.eq(a).nextAll(`[data-swiper-slide-index="${o.realIndex}"]`).eq(0).index();e=void 0===s?n:void 0===n?s:n-a==a-s?1<i.params.slidesPerGroup?n:a:n-a<a-s?n:s,t=o.activeIndex>o.previousIndex?"next":"prev"}else e=o.realIndex,t=e>o.previousIndex?"next":"prev";l&&(e+="next"===t?r:-1*r),i.visibleSlidesIndexes&&i.visibleSlidesIndexes.indexOf(e)<0&&(i.params.centeredSlides?e=e>a?e-Math.floor(n/2)+1:e+Math.floor(n/2)-1:e>a&&i.params.slidesPerGroup,i.slideTo(e,s?0:void 0))}}}o.thumbs={swiper:null},a("beforeInit",()=>{var e=o.params["thumbs"];e&&e.swiper&&(l(),n(!0))}),a("slideChange update resize observerUpdate",()=>{n()}),a("setTransition",(e,t)=>{var a=o.thumbs.swiper;a&&!a.destroyed&&a.setTransition(t)}),a("beforeDestroy",()=>{var e=o.thumbs.swiper;e&&!e.destroyed&&i&&e.destroy()}),Object.assign(o.thumbs,{init:l,update:n})},function(e){let{swiper:u,extendParams:t,emit:h,once:m}=e;t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(u,{freeMode:{onTouchStart:function(){var e=u.getTranslate();u.setTranslate(e),u.setTransition(0),u.touchEventsData.velocities.length=0,u.freeMode.onTouchEnd({currentPos:u.rtl?u.translate:-u.translate})},onTouchMove:function(){var{touchEventsData:e,touches:t}=u;0===e.velocities.length&&e.velocities.push({position:t[u.isHorizontal()?"startX":"startY"],time:e.touchStartTime}),e.velocities.push({position:t[u.isHorizontal()?"currentX":"currentY"],time:g()})},onTouchEnd:function(r){let l=r["currentPos"];const{params:n,$wrapperEl:o,rtlTranslate:d,snapGrid:p,touchEventsData:c}=u,e=g()-c.touchStartTime;if(l<-u.minTranslate())u.slideTo(u.activeIndex);else if(l>-u.maxTranslate())u.slides.length<p.length?u.slideTo(p.length-1):u.slideTo(u.slides.length-1);else{if(n.freeMode.momentum){if(1<c.velocities.length){const r=c.velocities.pop(),l=c.velocities.pop(),h=r.position-l.position,m=r.time-l.time;u.velocity=h/m,u.velocity/=2,Math.abs(u.velocity)<n.freeMode.minimumVelocity&&(u.velocity=0),(150<m||300<g()-r.time)&&(u.velocity=0)}else u.velocity=0;u.velocity*=n.freeMode.momentumVelocityRatio,c.velocities.length=0;let e=1e3*n.freeMode.momentumRatio;const l=u.velocity*e;let a=u.translate+l;d&&(a=-a);let t,s=!1;r=20*Math.abs(u.velocity)*n.freeMode.momentumBounceRatio;let i;if(a<u.maxTranslate())n.freeMode.momentumBounce?(a+u.maxTranslate()<-r&&(a=u.maxTranslate()-r),t=u.maxTranslate(),s=!0,c.allowMomentumBounce=!0):a=u.maxTranslate(),n.loop&&n.centeredSlides&&(i=!0);else if(a>u.minTranslate())n.freeMode.momentumBounce?(a-u.minTranslate()>r&&(a=u.minTranslate()+r),t=u.minTranslate(),s=!0,c.allowMomentumBounce=!0):a=u.minTranslate(),n.loop&&n.centeredSlides&&(i=!0);else if(n.freeMode.sticky){let t;for(let e=0;e<p.length;e+=1)if(p[e]>-a){t=e;break}a=-(a=Math.abs(p[t]-a)<Math.abs(p[t-1]-a)||"next"===u.swipeDirection?p[t]:p[t-1])}if(i&&m("transitionEnd",()=>{u.loopFix()}),0!==u.velocity){if(e=d?Math.abs((-a-u.translate)/u.velocity):Math.abs((a-u.translate)/u.velocity),n.freeMode.sticky){const l=Math.abs((d?-a:a)-u.translate),h=u.slidesSizesGrid[u.activeIndex];e=l<h?n.speed:l<2*h?1.5*n.speed:2.5*n.speed}}else if(n.freeMode.sticky)return void u.slideToClosest();n.freeMode.momentumBounce&&s?(u.updateProgress(t),u.setTransition(e),u.setTranslate(a),u.transitionStart(!0,u.swipeDirection),u.animating=!0,o.transitionEnd(()=>{u&&!u.destroyed&&c.allowMomentumBounce&&(h("momentumBounce"),u.setTransition(n.speed),setTimeout(()=>{u.setTranslate(t),o.transitionEnd(()=>{u&&!u.destroyed&&u.transitionEnd()})},0))})):u.velocity?(h("_freeModeNoMomentumRelease"),u.updateProgress(a),u.setTransition(e),u.setTranslate(a),u.transitionStart(!0,u.swipeDirection),u.animating||(u.animating=!0,o.transitionEnd(()=>{u&&!u.destroyed&&u.transitionEnd()}))):u.updateProgress(a),u.updateActiveIndex(),u.updateSlidesClasses()}else{if(n.freeMode.sticky)return void u.slideToClosest();n.freeMode&&h("_freeModeNoMomentumRelease")}(!n.freeMode.momentum||e>=n.longSwipesMs)&&(u.updateProgress(),u.updateActiveIndex(),u.updateSlidesClasses())}}}})},function(e){let c,u,h,{swiper:m,extendParams:t}=e;t({grid:{rows:1,fill:"column"}}),m.grid={initSlides:e=>{var t=m.params["slidesPerView"],{rows:a,fill:s}=m.params.grid;u=c/a,h=Math.floor(e/a),c=Math.floor(e/a)===e/a?e:Math.ceil(e/a)*a,"auto"!==t&&"row"===s&&(c=Math.max(c,t*a))},updateSlide:(e,t,a,s)=>{var{slidesPerGroup:i,spaceBetween:r}=m.params,{rows:l,fill:n}=m.params.grid;let o,d,p;if("row"===n&&1<i){const u=Math.floor(e/(i*l)),h=e-l*i*u,m=0===u?i:Math.min(Math.ceil((a-u*l*i)/l),i);p=Math.floor(h/m),o=(d=h-p*m+u*i)+p*c/l,t.css({"-webkit-order":o,order:o})}else"column"===n?(d=Math.floor(e/l),p=e-d*l,(d>h||d===h&&p===l-1)&&(p+=1)>=l&&(p=0,d+=1)):(p=Math.floor(e/u),d=e-p*u);t.css(s("margin-top"),0!==p?r&&r+"px":"")},updateWrapperSize:(a,s,e)=>{var{spaceBetween:t,centeredSlides:i,roundLengths:r}=m.params,l=m.params.grid["rows"];if(m.virtualSize=(a+t)*c,m.virtualSize=Math.ceil(m.virtualSize/l)-t,m.$wrapperEl.css({[e("width")]:m.virtualSize+t+"px"}),i){s.splice(0,s.length);const a=[];for(let t=0;t<s.length;t+=1){let e=s[t];r&&(e=Math.floor(e)),s[t]<m.virtualSize+s[0]&&a.push(e)}s.push(...a)}}}},function(e){e=e.swiper;Object.assign(e,{appendSlide:function(t){var{$wrapperEl:a,params:e}=this;if(e.loop&&this.loopDestroy(),"object"==typeof t&&"length"in t)for(let e=0;e<t.length;e+=1)t[e]&&a.append(t[e]);else a.append(t);e.loop&&this.loopCreate(),e.observer||this.update()}.bind(e),prependSlide:function(t){var e=this,{params:a,$wrapperEl:s,activeIndex:i}=e;a.loop&&e.loopDestroy();let r=i+1;if("object"==typeof t&&"length"in t){for(let e=0;e<t.length;e+=1)t[e]&&s.prepend(t[e]);r=i+t.length}else s.prepend(t);a.loop&&e.loopCreate(),a.observer||e.update(),e.slideTo(r,0,!1)}.bind(e),addSlide:function(t,a){var s=this,{$wrapperEl:i,params:r,activeIndex:l}=s;let n=l;if(r.loop&&(n-=s.loopedSlides,s.loopDestroy(),s.slides=i.children("."+r.slideClass)),l=s.slides.length,t<=0)s.prependSlide(a);else if(l<=t)s.appendSlide(a);else{let e=n>t?n+1:n;var o=[];for(let e=l-1;e>=t;--e){const t=s.slides.eq(e);t.remove(),o.unshift(t)}if("object"==typeof a&&"length"in a){for(let e=0;e<a.length;e+=1)a[e]&&i.append(a[e]);e=n>t?n+a.length:n}else i.append(a);for(let e=0;e<o.length;e+=1)i.append(o[e]);r.loop&&s.loopCreate(),r.observer||s.update(),r.loop?s.slideTo(e+s.loopedSlides,0,!1):s.slideTo(e,0,!1)}}.bind(e),removeSlide:function(t){var a=this,{params:e,$wrapperEl:s,activeIndex:i}=a;let r=i;e.loop&&(r-=a.loopedSlides,a.loopDestroy(),a.slides=s.children("."+e.slideClass));let l,n=r;if("object"==typeof t&&"length"in t)for(let e=0;e<t.length;e+=1)l=t[e],a.slides[l]&&a.slides.eq(l).remove(),l<n&&--n;else l=t,a.slides[l]&&a.slides.eq(l).remove(),l<n&&--n;n=Math.max(n,0),e.loop&&a.loopCreate(),e.observer||a.update(),e.loop?a.slideTo(n+a.loopedSlides,0,!1):a.slideTo(n,0,!1)}.bind(e),removeAllSlides:function(){var t=[];for(let e=0;e<this.slides.length;e+=1)t.push(e);this.removeSlide(t)}.bind(e)})},function(e){let{swiper:l,extendParams:t,on:a}=e;t({fadeEffect:{crossFade:!1,transformEl:null}}),k({effect:"fade",swiper:l,on:a,setTranslate:()=>{const s=l["slides"],i=l.params.fadeEffect;for(let a=0;a<s.length;a+=1){const s=l.slides.eq(a);let e=-s[0].swiperSlideOffset,t=(l.params.virtualTranslate||(e-=l.translate),0);l.isHorizontal()||(t=e,e=0);var r=l.params.fadeEffect.crossFade?Math.max(1-Math.abs(s[0].progress),0):1+Math.min(Math.max(s[0].progress,-1),0);z(i,s).css({opacity:r}).transform(`translate3d(${e}px, ${t}px, 0px)`)}},setTransition:e=>{var t=l.params.fadeEffect["transformEl"];(t?l.slides.find(t):l.slides).transition(e),A({swiper:l,duration:e,transformEl:t,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!l.params.cssMode})})},function(e){let{swiper:f,extendParams:t,on:a}=e;t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});const v=(e,t,a)=>{let s=a?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),i=a?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===s.length&&(s=L(`<div class="swiper-slide-shadow-${a?"left":"top"}"></div>`),e.append(s)),0===i.length&&(i=L(`<div class="swiper-slide-shadow-${a?"right":"bottom"}"></div>`),e.append(i)),s.length&&(s[0].style.opacity=Math.max(-t,0)),i.length&&(i[0].style.opacity=Math.max(t,0))};k({effect:"cube",swiper:f,on:a,setTranslate:()=>{const{$el:e,$wrapperEl:t,slides:n,width:a,height:s,rtlTranslate:o,size:d,browser:i}=f,p=f.params.cubeEffect,c=f.isHorizontal(),u=f.virtual&&f.params.virtual.enabled;let r,h=0;p.shadow&&(c?(0===(r=t.find(".swiper-cube-shadow")).length&&(r=L('<div class="swiper-cube-shadow"></div>'),t.append(r)),r.css({height:a+"px"})):0===(r=e.find(".swiper-cube-shadow")).length&&(r=L('<div class="swiper-cube-shadow"></div>'),e.append(r)));for(let l=0;l<n.length;l+=1){const f=n.eq(l);let e=l,t=90*(e=u?parseInt(f.attr("data-swiper-slide-index"),10):e),a=Math.floor(t/360);o&&(t=-t,a=Math.floor(-t/360));const L=Math.max(Math.min(f[0].progress,1),-1);let s=0,i=0,r=0;e%4==0?(s=4*-a*d,r=0):(e-1)%4==0?(s=0,r=4*-a*d):(e-2)%4==0?(s=d+4*a*d,r=d):(e-3)%4==0&&(s=-d,r=3*d+4*d*a),o&&(s=-s),c||(i=s,s=0);var m=`rotateX(${c?0:-t}deg) rotateY(${c?t:0}deg) translate3d(${s}px, ${i}px, ${r}px)`;L<=1&&-1<L&&(h=90*e+90*L,o)&&(h=90*-e-90*L),f.transform(m),p.slideShadows&&v(f,L,c)}if(t.css({"-webkit-transform-origin":`50% 50% -${d/2}px`,"transform-origin":`50% 50% -${d/2}px`}),p.shadow)if(c)r.transform(`translate3d(0px, ${a/2+p.shadowOffset}px, ${-a/2}px) rotateX(90deg) rotateZ(0deg) scale(${p.shadowScale})`);else{const e=Math.abs(h)-90*Math.floor(Math.abs(h)/90),f=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),t=p.shadowScale,n=p.shadowScale/f,v=p.shadowOffset;r.transform(`scale3d(${t}, 1, ${n}) translate3d(0px, ${s/2+v}px, ${-s/2/n}px) rotateX(-90deg)`)}var l=i.isSafari||i.isWebView?-d/2:0;t.transform(`translate3d(0px,0,${l}px) rotateX(${f.isHorizontal()?0:h}deg) rotateY(${f.isHorizontal()?-h:0}deg)`),t[0].style.setProperty("--swiper-cube-translate-z",l+"px")},setTransition:e=>{var{$el:t,slides:a}=f;a.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),f.params.cubeEffect.shadow&&!f.isHorizontal()&&t.find(".swiper-cube-shadow").transition(e)},recreateShadows:()=>{const a=f.isHorizontal();f.slides.each(e=>{var t=Math.max(Math.min(e.progress,1),-1);v(L(e),t,a)})},getEffectParams:()=>f.params.cubeEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0})})},function(e){let{swiper:c,extendParams:t,on:a}=e;t({flipEffect:{slideShadows:!0,limitRotation:!0,transformEl:null}});const u=(e,t,a)=>{let s=c.isHorizontal()?e.find(".swiper-slide-shadow-left"):e.find(".swiper-slide-shadow-top"),i=c.isHorizontal()?e.find(".swiper-slide-shadow-right"):e.find(".swiper-slide-shadow-bottom");0===s.length&&(s=D(a,e,c.isHorizontal()?"left":"top")),0===i.length&&(i=D(a,e,c.isHorizontal()?"right":"bottom")),s.length&&(s[0].style.opacity=Math.max(-t,0)),i.length&&(i[0].style.opacity=Math.max(t,0))};k({effect:"flip",swiper:c,on:a,setTranslate:()=>{var{slides:l,rtlTranslate:n}=c,o=c.params.flipEffect;for(let r=0;r<l.length;r+=1){var d=l.eq(r);let e=d[0].progress;c.params.flipEffect.limitRotation&&(e=Math.max(Math.min(d[0].progress,1),-1));var p=d[0].swiperSlideOffset;let t=-180*e,a=0,s=c.params.cssMode?-p-c.translate:-p,i=0;c.isHorizontal()?n&&(t=-t):(i=s,s=0,a=-t,t=0),d[0].style.zIndex=-Math.abs(Math.round(e))+l.length,o.slideShadows&&u(d,e,o);p=`translate3d(${s}px, ${i}px, 0px) rotateX(${a}deg) rotateY(${t}deg)`;z(o,d).transform(p)}},setTransition:e=>{var t=c.params.flipEffect["transformEl"];(t?c.slides.find(t):c.slides).transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),A({swiper:c,duration:e,transformEl:t})},recreateShadows:()=>{const s=c.params.flipEffect;c.slides.each(e=>{var t=L(e);let a=t[0].progress;c.params.flipEffect.limitRotation&&(a=Math.max(Math.min(e.progress,1),-1)),u(t,a,s)})},getEffectParams:()=>c.params.flipEffect,perspective:()=>!0,overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!c.params.cssMode})})},function(e){let{swiper:b,extendParams:t,on:a}=e;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0,transformEl:null}}),k({effect:"coverflow",swiper:b,on:a,setTranslate:()=>{const{width:e,height:o,slides:d,slidesSizesGrid:p}=b,c=b.params.coverflowEffect,u=b.isHorizontal(),h=b.translate,m=u?e/2-h:o/2-h,f=u?c.rotate:-c.rotate,v=c.depth;for(let n=0,e=d.length;n<e;n+=1){const b=d.eq(n),o=p[n],h=(m-b[0].swiperSlideOffset-o/2)/o,w="function"==typeof c.modifier?c.modifier(h):h*c.modifier;let e=u?f*w:0,t=u?0:f*w,a=-v*Math.abs(w),s=c.stretch,i=("string"==typeof s&&-1!==s.indexOf("%")&&(s=parseFloat(c.stretch)/100*o),u?0:s*w),r=u?s*w:0,l=1-(1-c.scale)*Math.abs(w);Math.abs(r)<.001&&(r=0),Math.abs(i)<.001&&(i=0),Math.abs(a)<.001&&(a=0),Math.abs(e)<.001&&(e=0),Math.abs(t)<.001&&(t=0),Math.abs(l)<.001&&(l=0);var g=`translate3d(${r}px,${i}px,${a}px)  rotateX(${t}deg) rotateY(${e}deg) scale(${l})`;if(z(c,b).transform(g),b[0].style.zIndex=1-Math.abs(Math.round(w)),c.slideShadows){let e=u?b.find(".swiper-slide-shadow-left"):b.find(".swiper-slide-shadow-top"),t=u?b.find(".swiper-slide-shadow-right"):b.find(".swiper-slide-shadow-bottom");0===e.length&&(e=D(c,b,u?"left":"top")),0===t.length&&(t=D(c,b,u?"right":"bottom")),e.length&&(e[0].style.opacity=0<w?w:0),t.length&&(t[0].style.opacity=0<-w?-w:0)}}},setTransition:e=>{var t=b.params.coverflowEffect["transformEl"];(t?b.slides.find(t):b.slides).transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})},function(e){let{swiper:w,extendParams:t,on:a}=e;t({creativeEffect:{transformEl:null,limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});k({effect:"creative",swiper:w,on:a,setTranslate:()=>{const{slides:i,$wrapperEl:e,slidesSizesGrid:r}=w,l=w.params.creativeEffect,n=l["progressMultiplier"],o=w.params.centeredSlides;if(o){const i=r[0]/2-w.params.slidesOffsetBefore||0;e.transform(`translateX(calc(50% - ${i}px))`)}for(let s=0;s<i.length;s+=1){const r=i.eq(s),h=r[0].progress,m=Math.min(Math.max(r[0].progress,-l.limitProgress),l.limitProgress);let e=m;o||(e=Math.min(Math.max(r[0].originalProgress,-l.limitProgress),l.limitProgress));const f=r[0].swiperSlideOffset,v=[w.params.cssMode?-f-w.translate:-f,0,0],g=[0,0,0];let t=!1,a=(w.isHorizontal()||(v[1]=v[0],v[0]=0),{translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1});m<0?(a=l.next,t=!0):0<m&&(a=l.prev,t=!0),v.forEach((e,t)=>{v[t]=`calc(${e}px + (${e=a.translate[t],"string"==typeof e?e:e+"px"} * ${Math.abs(m*n)}))`}),g.forEach((e,t)=>{g[t]=a.rotate[t]*Math.abs(m*n)}),r[0].style.zIndex=-Math.abs(Math.round(h))+i.length;var d=v.join(", "),p=`rotateX(${g[0]}deg) rotateY(${g[1]}deg) rotateZ(${g[2]}deg)`,c=e<0?`scale(${1+(1-a.scale)*e*n})`:`scale(${1-(1-a.scale)*e*n})`,u=e<0?1+(1-a.opacity)*e*n:1-(1-a.opacity)*e*n,d=`translate3d(${d}) ${p} `+c;if(t&&a.shadow||!t){let e=r.children(".swiper-slide-shadow");if((e=0===e.length&&a.shadow?D(l,r):e).length){const w=l.shadowPerProgress?m*(1/l.limitProgress):m;e[0].style.opacity=Math.min(Math.max(Math.abs(w),0),1)}}p=z(l,r);p.transform(d).css({opacity:u}),a.origin&&p.css("transform-origin",a.origin)}},setTransition:e=>{var t=w.params.creativeEffect["transformEl"];(t?w.slides.find(t):w.slides).transition(e).find(".swiper-slide-shadow").transition(e),A({swiper:w,duration:e,transformEl:t,allSlides:!0})},perspective:()=>w.params.creativeEffect.perspective,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!w.params.cssMode})})},function(e){let{swiper:b,extendParams:t,on:a}=e;t({cardsEffect:{slideShadows:!0,transformEl:null,rotate:!0}}),k({effect:"cards",swiper:b,on:a,setTranslate:()=>{const{slides:n,activeIndex:o}=b,d=b.params.cardsEffect,{startTranslate:p,isTouched:c}=b.touchEventsData,u=b.translate;for(let l=0;l<n.length;l+=1){var h=n.eq(l),m=h[0].progress,f=Math.min(Math.max(m,-4),4);let e=h[0].swiperSlideOffset,t=(b.params.centeredSlides&&!b.params.cssMode&&b.$wrapperEl.transform(`translateX(${b.minTranslate()}px)`),b.params.centeredSlides&&b.params.cssMode&&(e-=n[0].swiperSlideOffset),b.params.cssMode?-e-b.translate:-e),a=0;var v=-100*Math.abs(f);let s=1,i=-2*f,r=8-.75*Math.abs(f);var g=b.virtual&&b.params.virtual.enabled?b.virtual.from+l:l,w=(g===o||g===o-1)&&0<f&&f<1&&(c||b.params.cssMode)&&u<p,g=(g===o||g===o+1)&&f<0&&-1<f&&(c||b.params.cssMode)&&p<u;if(w||g){const n=(1-Math.abs((Math.abs(f)-.5)/.5))**.5;i+=-28*f*n,s+=-.5*n,r+=96*n,a=-25*n*Math.abs(f)+"%"}if(t=f<0?`calc(${t}px + (${r*Math.abs(f)}%))`:0<f?`calc(${t}px + (-${r*Math.abs(f)}%))`:t+"px",!b.isHorizontal()){const n=a;a=t,t=n}w=f<0?""+(1+(1-s)*f):""+(1-(1-s)*f),g=`
        translate3d(${t}, ${a}, ${v}px)
        rotateZ(${d.rotate?i:0}deg)
        scale(${w})
      `;if(d.slideShadows){let e=h.find(".swiper-slide-shadow");(e=0===e.length?D(d,h):e).length&&(e[0].style.opacity=Math.min(Math.max((Math.abs(f)-.5)/.5,0),1))}h[0].style.zIndex=-Math.abs(Math.round(m))+n.length,z(d,h).transform(g)}},setTransition:e=>{var t=b.params.cardsEffect["transformEl"];(t?b.slides.find(t):b.slides).transition(e).find(".swiper-slide-shadow").transition(e),A({swiper:b,duration:e,transformEl:t})},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0,virtualTranslate:!b.params.cssMode})})}]),C});