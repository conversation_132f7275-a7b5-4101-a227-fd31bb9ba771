window.theme=window.theme||{},theme.config={mqlSmall:!1,mediaQuerySmall:"screen and (max-width: 749px)",isTouch:!!("ontouchstart"in window||window.DocumentTouch&&window.document instanceof DocumentTouch||window.navigator.maxTouchPoints||window.navigator.msMaxTouchPoints)},theme.initWhenVisible=function(options){const threshold=options.threshold?options.threshold:0;new IntersectionObserver((entries,observer2)=>{entries.forEach(entry=>{entry.isIntersecting&&typeof options.callback=="function"&&(options.callback(),observer2.unobserve(entry.target))})},{rootMargin:`0px 0px ${threshold}px 0px`}).observe(options.element)};class ImageComparison extends HTMLElement{constructor(){super(),this.active=!1,this.button=this.querySelector("button"),this.horizontal=this.dataset.layout==="horizontal",this.init(),theme.initWhenVisible({element:this.querySelector(".image-comparison__animate"),callback:this.animate.bind(this),threshold:0})}animate(){this.setAttribute("animate",""),this.classList.add("animating"),setTimeout(()=>{this.classList.remove("animating")},1e3)}init(){theme.config.isTouch?(this.button.addEventListener("touchstart",this.startHandler.bind(this)),document.body.addEventListener("touchend",this.endHandler.bind(this)),document.body.addEventListener("touchmove",this.onHandler.bind(this))):(this.button.addEventListener("mousedown",this.startHandler.bind(this)),document.body.addEventListener("mouseup",this.endHandler.bind(this)),document.body.addEventListener("mousemove",this.onHandler.bind(this)))}startHandler(){this.active=!0,this.classList.add("scrolling")}endHandler(){this.active=!1,this.classList.remove("scrolling")}onHandler(e){if(!this.active)return;const event=e.touches&&e.touches[0]||e,x=this.horizontal?event.pageX-this.offsetLeft:event.pageY-this.offsetTop;this.scrollIt(x)}scrollIt(x){const distance=this.horizontal?this.clientWidth:this.clientHeight,max=distance-20,mousePercent=Math.max(20,Math.min(x,max))*100/distance;this.style.setProperty("--percent",mousePercent+"%")}}customElements.define("image-comparison",ImageComparison);const API="https://api.myulike.com";"scrollRestoration"in history&&(history.scrollRestoration="manual");function isMobile(){const userAgent=navigator.userAgent||navigator.vendor||window.opera;return/Android|iPhone|iPad|iPod|BlackBerry/i.test(userAgent)&&window.innerWidth<=768}function commonGtmEvent(category,operating,label){operating=="true"&&isMobile()&&(category=`M-${category}`),dataLayer.push({event:"common",category,operating,label})}function getCookie(name){const cookieName=name+"=",cookies=document.cookie.split(";");for(let i=0;i<cookies.length;i++){let cookie=cookies[i].trim();if(cookie.indexOf(cookieName)===0)return cookie.substring(cookieName.length,cookie.length)}return""}function setCookie(cookieName,cookieValue,daysToExpire,endOfDay=!1){var expires="";if(daysToExpire){var date=new Date;date.setTime(date.getTime()+daysToExpire*24*60*60*1e3),endOfDay&&date.setHours(0,0,0,0),expires="; expires="+date.toUTCString()}document.cookie=cookieName+"="+cookieValue+expires+"; path=/"}function clearCookie(name){document.cookie=name+"=; max-age=0; path=/"}function init(){document.querySelectorAll("video img").forEach(img=>{img.setAttribute("alt","video image"),img.setAttribute("loading","lazy")});let gDiscount=new URLSearchParams(window.location.search).get("discount");gDiscount&&autoDiscountCode(gDiscount)}function initGA(){if($(document).on("click","#shopify-section-announcement-bar .announcement-bar__message a",function(){let _title=$(this).text();_title=="Learn more"?commonGtmEvent("\u9996\u9875-90 learn","click",_title):commonGtmEvent("\u9996\u9875-free shop","click",_title)}),$(document).on("click",".header__logo-link",function(){commonGtmEvent("header__logo-link","click","logo")}),$(document).on("click","#desktop-menu-1 .header-new-operate-btn, #mobile-menu-1 .header-new-operate-btn",function(){commonGtmEvent("\u5BFC\u822A-Sale","true",location.pathname)}),$(document).on("click","#desktop-menu-2 .header-new-product-item, #mobile-menu-2  .header-new-product-item",function(){let index=$("#desktop-menu-2 .header-new-product-item").index(this);switch(console.log(index),isMobile()&&(index=$("#mobile-menu-2 .header-new-product-item").index(this)),index){case 0:commonGtmEvent("\u5BFC\u822A-air10","true",location.pathname);break;case 1:commonGtmEvent("\u5BFC\u822A-air3","true",location.pathname);break;case 2:commonGtmEvent("\u5BFC\u822A-airX","true",location.pathname);break;case 3:commonGtmEvent("\u5BFC\u822A-reglow","true",location.pathname);break}}),$(document).on("click","#desktop-menu-4 .header-new-link-item, #mobile-menu-4 .header-new-link-item",function(e){const linkText=$(this).attr("text").trim();commonGtmEvent("\u5BFC\u822A-explore more-"+linkText,"true",location.pathname)}),$(document).on("click",".header .header__linklist-link, #mobile-menu-drawer .mobile-nav .mobile-nav__link",function(){const index=$(this).parent().index(),mIndex=$("#mobile-menu-drawer .mobile-nav .mobile-nav__link").index(this),linkText=$(this).attr("text");if(!linkText)return!1;(!isMobile()&&index!=0||mIndex!=0)&&commonGtmEvent("\u5BFC\u822A-"+linkText,"true",location.pathname)}),$(document).on("click","#desktop-menu-5 .header-new-link-item,#mobile-menu-5 .header-new-link-item",function(e){const linkText=$(this).attr("text").trim();commonGtmEvent("\u5BFC\u822A-Explore More-"+linkText,"true",location.pathname)}),$(document).on("click",".header .popover-button",function(e){commonGtmEvent("\u5BFC\u822A-english","true",location.pathname)}),$(document).on("click",".header .j-search",function(){commonGtmEvent("\u5BFC\u822A-search","true","search")}),$(document).on("click",".header .j-cart",function(){commonGtmEvent("\u5BFC\u822A-add cart","true","cart")}),$(document).on("click",".header .j-user-login",function(){commonGtmEvent("\u5BFC\u822A-login","true","wishlist")}),$(document).on("click","#AddToCart",function(){window._conv_q=window._conv_q||[],_conv_q.push(["triggerConversion","100476462"]),commonGtmEvent(`${g_product_name} Product-add to cart`,"","Product")}),$(document).on("click",".slideshow__slide-list .button-wrapper .home-btn",function(){var i=1,index=1;$(".slideshow__progress-bar").each(function(){if($(this).attr("aria-current")=="true"){index=i;return}i++});const btnIndex=$(this).hasClass("home-btn")?$(this).index(".home-btn"):-1;console.log("\u6309\u94AE\u7D22\u5F15:",btnIndex,"\u5F53\u524D\u7D22\u5F15:",index),commonGtmEvent(`\u9996\u9875-banner${index}`,"click","")}),$(document).on("click",".product-area-list .product-area-item:eq(0) .product-area-btn-item:eq(0)",function(){commonGtmEvent("\u9996\u9875-air10-learn more","click","")}),$(document).on("click",".product-area-list .product-area-item:eq(0) .product-area-btn-item:eq(1)",function(){commonGtmEvent("\u9996\u9875-air10-shop now","click","")}),$(document).on("click",".product-area-list .product-area-item:eq(1) .product-area-btn-item:eq(0)",function(){commonGtmEvent("\u9996\u9875-air3 learn more","click","")}),$(document).on("click",".product-area-list .product-area-item:eq(1) .product-area-btn-item:eq(1)",function(){commonGtmEvent("\u9996\u9875-air3 shop now","click","")}),$(document).on("click",".media-evaluation-real-box .media-real-bottom-arrow-item",function(){commonGtmEvent("\u9996\u9875-review swith","click","")}),$(document).on("click",".sub-activity-left .sub-activity-btn",function(){commonGtmEvent("\u9996\u9875- marketing banner1","click","")}),$(document).on("click",".sub-activity-right .sub-activity-btn:eq(0)",function(){commonGtmEvent("\u9996\u9875- marketing banner2","click","")}),$(document).on("click",".sub-activity-right .sub-activity-btn:eq(1)",function(){commonGtmEvent("\u9996\u9875- marketing banner3","click","")}),$(document).on("click",".blog-info-cont .blog-info-left",function(){commonGtmEvent("\u9996\u9875-blog1","click","")}),$(document).on("click",".blog-info-list .blog-info-item",function(){var index=$(".blog-info-list .blog-info-item").index($(this))+2;commonGtmEvent(`\u9996\u9875-blog${index}`,"click","")}),$(document).on("click",".footer__top .btn-ico",function(){commonGtmEvent("\u9996\u9875-bottom subscription","click","")}),$(document).on("click",".footer__top .social-media__item",function(){let index=$(this).index(),name="\u9996\u9875-ins";switch(index){case 0:name="\u9996\u9875-fb";break;case 1:name="\u9996\u9875-tw";break;case 2:name="\u9996\u9875-ins";break;case 3:name="\u9996\u9875-youtube";break;case 4:name="\u9996\u9875-tt";break}commonGtmEvent(name,"click","")}),$(document).on("click",".footer__item-list .link--faded",function(){let _title=$(this).text();commonGtmEvent(`\u9996\u9875-bottom ${_title}`,"click","")}),$(document).on("click",".checkout-button",function(){commonGtmEvent("\u7ED3\u8D26-check out","click",location.pathname),window._conv_q=window._conv_q||[],_conv_q.push(["triggerConversion","*********"])}),$(document).on("click",".brand-video .brand-video-btn",function(){let slideIndex=$(this).closest(".brand-video-item").attr("data-swiper-slide-index")+1;commonGtmEvent(`\u9996\u9875-\u89C6\u9891\u70B9\u51FB-${slideIndex}`,"click",location.pathname,slideIndex)}),$(document).on("click",".account-list .header-logout",function(e){e.preventDefault(),localStorage.removeItem("userInfo"),fetch("https://api.ulike.com/user/logout",{headers:{accept:"application/json, text/plain, */*","content-type":"application/json","x-requested-with":"XMLHttpRequest"},referrer:"https://account.ulike.com/",referrerPolicy:"strict-origin-when-cross-origin",method:"GET",mode:"cors",credentials:"include"}).then(response=>{response.ok?window.location.href=$(this).attr("href")||"/":console.error("Logout failed")}).catch(error=>{console.error("Logout error:",error)})}),location.pathname=="/pages/treatment-tracker"){let _temp=isMobile()?"M-":"";$(document).on("click",".treatment-tracker-banner .button-wrapper a",function(){commonGtmEvent(`${_temp}reminder_join`,isMobile(),location.pathname)}),$(document).on("click",".january-two .btn-txt",function(){commonGtmEvent(`${_temp}reminder_sign up`,isMobile(),location.pathname)}),$(document).on("click",".review-product .review-product-btn",function(){window._conv_q=window._conv_q||[],_conv_q.push(["triggerConversion","*********"])}),$(document).on("click",".results-reviews-product-module .results-reviews-product-add-btn",function(){window._conv_q=window._conv_q||[],_conv_q.push(["triggerConversion","*********"])})}location.href=="https://www.ulike.com/"&&($(".refer-friend-container .join-button").on("click",function(){commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u5BFC\u6D41\uFF0C\u9996\u9875\u70B9\u51FB",isMobile(),location.pathname)}),$(".product-area-first .product-area-btn-item").on("click",function(){commonGtmEvent("\u9996\u9875-A10-buy amazon",isMobile(),location.pathname)})),$(document).on("click",".product-form__button .button",function(e){commonGtmEvent("buy on amazon",isMobile(),location.pathname)}),document.body.addEventListener("docapp-discount-code-remove",e=>{console.log("Code is being removed"),setTimeout(function(){document.documentElement.dispatchEvent(new CustomEvent("cart:refresh",{bubbles:!1}))},2400)})}document.addEventListener("DOMContentLoaded",function(){init(),setTimeout(initGA,1e3)});function getUrlParameter(paramName){return new URLSearchParams(window.location.search).get(paramName)}function makeRequest(url,method="GET",data=null){return new Promise((resolve,reject)=>{const options={method,headers:{"Content-Type":"application/json"},mode:"cors",cache:"no-cache"};data&&(options.body=JSON.stringify(data)),fetch(url,options).then(response=>{if(!response.ok)throw new Error(`HTTP\u9519\u8BEF\uFF01\u72B6\u6001\u7801\uFF1A${response.status}`);return response.json()}).then(data2=>resolve(data2)).catch(error=>reject(error))})}function validateEmail(email){var emailRegex=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;return emailRegex.test(email)}function copyTextFallback(text){if(navigator.clipboard)navigator.clipboard.writeText(text);else{const textArea=document.createElement("textarea");textArea.value=text,textArea.style.position="fixed",document.body.appendChild(textArea),textArea.select();try{document.execCommand("copy"),console.log("\u590D\u5236\u6210\u529F:",text)}catch(err){console.error("\u590D\u5236\u5931\u8D25:",err)}document.body.removeChild(textArea)}}function autoDiscountCode(code){let ulikeCode=["VVBPV0VS","SE9MSURBWQ==","VkRBWTI0","MTBZZWFycw==","UHJpbWVEZWFs","TUVNREFZODA=","TU9NUzgw","QUlSMTBFQVJMWQ==","TkVXNjA="];checkObject(window.discountOnCartProApp,function(){let cartCodeArr=window.discountOnCartProApp.codes;if(cartCodeArr.length<1)window.discountOnCartProApp.applyCode(code),window.discountOnCartProApp.updateWidgets();else{var flag=ulikeCode.some(item=>cartCodeArr.includes(atob(item)));console.log(flag),flag&&(window.discountOnCartProApp.removeCode(cartCodeArr[cartCodeArr.length-1]),window.discountOnCartProApp.applyCode(code))}setTimeout(function(){window.discountOnCartProApp.updateWidgets()},3e3)})}function checkObject(obj,callback){window.discountOnCartProApp?callback():setTimeout(()=>checkObject(window.discountOnCartProApp,callback),1e3)}function scrollToElement(targetId){var targetElement=$("#"+targetId);targetElement.length?$("html, body").animate({scrollTop:targetElement.offset().top-100},1e3):$("html, body").animate({scrollTop:0},1e3)}function subscriptionApi(email,tag,type){return sendUlikeApi("/user/userSubscribe",{userEmail:email,userPhone:"",since:type||"USER_SUBSCRIBE",tags:[tag]})}function subscriptionApiNew(email,tag,type){return sendUlikeApi("/user/userSubscribe",{userEmail:email,userPhone:"",scene:type||"USER_SUBSCRIBE",tags:[tag]})}function sendUlikeApi(url,data){data.siteCode="US";var _url="https://api.ulike.com"+url;data.language=window.Shopify.locale,data.requestId=generateGUID();var settings={url:_url,method:"POST",headers:{"Content-Type":"application/json"},data:JSON.stringify(data),xhrFields:{withCredentials:!0}};return new Promise((resolve,reject)=>{$.ajax(settings).done(function(response){resolve(response)}).fail(function(error){console.error("Error during API call:",error),reject(error)})})}function generateGUID(){function s4(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}return s4()+s4()+"-"+s4()+"-"+s4()+"-"+s4()+"-"+s4()+s4()+s4()}function validatePhoneNumber(number){return/^(\+|00)?[\d\s-]{0,4}?[\s-]?(\(?\d+\)?[\s-]?){1,6}\d+$/.test(number)}function loadCSS(href){let link=document.createElement("link");link.rel="stylesheet",link.href=href,link.media="all",link.fetchPriority="low",document.head.appendChild(link)}
//# sourceMappingURL=/cdn/shop/t/56/assets/custom.js.map?v=32900079008877312541752223524
