# 7Magic WordPress Theme

A modern WordPress theme cloned from the Ulike website with 1:1 design accuracy. Features IPL hair removal device showcase with responsive design and Elementor compatibility.

## Author
**jiang**

## Version
1.0.0

## Description
This theme replicates the Ulike website design perfectly while adapting it for WordPress functionality. All product links redirect to Amazon for purchases, making it ideal for affiliate marketing.

## Features

### Design & Layout
- ✅ **1:1 Ulike Website Clone** - Pixel-perfect recreation of the original design
- ✅ **Responsive Design** - Works perfectly on all devices
- ✅ **Modern UI/UX** - Clean, professional interface
- ✅ **Fast Loading** - Optimized for performance

### WordPress Integration
- ✅ **Custom Post Types** - Product management system
- ✅ **Elementor Ready** - Full compatibility with Elementor page builder
- ✅ **Widget Areas** - Customizable sidebar and footer widgets
- ✅ **Menu Support** - Custom navigation menus
- ✅ **SEO Optimized** - Clean, semantic HTML structure

### E-commerce Features
- ✅ **Amazon Integration** - All product buttons redirect to Amazon
- ✅ **Product Showcase** - Beautiful product grid layouts
- ✅ **No WooCommerce Required** - Lightweight alternative
- ✅ **Affiliate Ready** - Perfect for affiliate marketing

### Technical Features
- ✅ **Original Assets** - Uses actual Ulike CSS, JS, and images
- ✅ **GSAP Animations** - Smooth, professional animations
- ✅ **Swiper Carousel** - Touch-friendly product sliders
- ✅ **Lazy Loading** - Improved performance
- ✅ **Cross-browser Compatible** - Works in all modern browsers

## Installation

1. **Upload Theme**
   ```
   wp-content/themes/7magic/
   ```

2. **Activate Theme**
   - Go to WordPress Admin → Appearance → Themes
   - Find "7Magic" and click "Activate"

3. **Configure Settings**
   - Set up your navigation menu
   - Add your logo in Customizer
   - Configure widgets

## Theme Structure

```
7magic/
├── style.css              # Main stylesheet with theme info
├── index.php              # Main template file
├── header.php             # Header template
├── footer.php             # Footer template
├── functions.php          # Theme functions and features
├── screenshot.png         # Theme preview image
├── README.md             # This file
└── assets/
    ├── css/              # Stylesheets from Ulike
    │   ├── theme.min.css
    │   ├── common-ulike.min.css
    │   ├── custom_css.css
    │   └── swiper-bundle.min.css
    ├── js/               # JavaScript files
    │   ├── jquery.min.js
    │   ├── theme.js
    │   ├── gsap.min.js
    │   ├── swiper-bundle.min.js
    │   └── custom.js
    ├── fonts/            # Custom fonts
    │   ├── Saans-Regular.otf
    │   └── Saans-Medium.otf
    └── images/           # Theme images
        ├── logo.svg
        ├── sapphire-air-10.jpg
        ├── sapphire-air-3.jpg
        ├── ulike-x.jpg
        └── led-mask.jpg
```

## Customization

### Adding Products
1. Go to WordPress Admin → Products → Add New
2. Fill in product details:
   - Title
   - Description
   - Featured Image
   - Amazon URL (in custom fields)
   - Price
   - Key Features

### Customizing Colors
Edit `style.css` and look for these CSS variables:
```css
:root {
  --primary-color: #ff6b6b;
  --secondary-color: #ff8e8e;
  --text-color: #333;
  --background-color: #fff;
}
```

### Adding Amazon Links
Use the custom meta box in the product editor to add Amazon URLs. The theme will automatically create "Buy on Amazon" buttons.

### Elementor Integration
The theme is fully compatible with Elementor. You can:
- Create custom layouts
- Use Elementor widgets
- Build landing pages
- Customize headers/footers

## Shortcodes

### Product Grid
Display products in a grid layout:
```
[product_grid limit="4" category="ipl-devices"]
```

Parameters:
- `limit` - Number of products to show (default: 4)
- `category` - Filter by product category

## Hooks & Filters

### Custom Hooks
```php
// Before product grid
do_action('sevenmagic_before_product_grid');

// After product grid  
do_action('sevenmagic_after_product_grid');

// Custom product card content
apply_filters('sevenmagic_product_card_content', $content, $post_id);
```

## Browser Support
- ✅ Chrome (latest)
- ✅ Firefox (latest)
- ✅ Safari (latest)
- ✅ Edge (latest)
- ✅ Mobile browsers

## Performance
- ⚡ **Fast Loading** - Optimized assets
- 📱 **Mobile Optimized** - Responsive images
- 🔍 **SEO Ready** - Clean markup
- ♿ **Accessible** - WCAG compliant

## Support & Updates

### Getting Help
1. Check this README file
2. Review the WordPress Codex
3. Contact the theme author

### Updates
- Theme updates will maintain the 1:1 Ulike design
- New features will be added based on user feedback
- Security updates will be provided as needed

## License
GPL v2 or later

## Credits
- **Original Design**: Ulike (www.ulike.com)
- **WordPress Theme**: jiang
- **Fonts**: Saans font family
- **Icons**: Custom SVG icons
- **Libraries**: GSAP, Swiper.js, jQuery

## Changelog

### Version 1.0.0
- Initial release
- Complete Ulike website clone
- WordPress integration
- Elementor compatibility
- Amazon redirect functionality
- Responsive design
- Performance optimization

---

**Note**: This theme is designed to replicate the Ulike website design for educational and development purposes. All original assets belong to their respective owners.
