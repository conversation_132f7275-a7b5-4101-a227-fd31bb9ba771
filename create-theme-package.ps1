# 7Magic WordPress Theme Package Creator
# This script creates a distributable ZIP package of the theme

Write-Host "Creating 7Magic WordPress Theme Package..." -ForegroundColor Green

# Define paths
$themePath = "wp-content\themes\7magic"
$packageName = "7magic-wordpress-theme-v1.0.0.zip"
$tempDir = "temp-package"

# Check if theme directory exists
if (-not (Test-Path $themePath)) {
    Write-Host "Error: Theme directory not found at $themePath" -ForegroundColor Red
    exit 1
}

# Create temporary directory
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir | Out-Null

# Copy theme files
Write-Host "Copying theme files..." -ForegroundColor Yellow
Copy-Item $themePath -Destination "$tempDir\7magic" -Recurse

# Create package info file
$packageInfo = @"
# 7Magic WordPress Theme Package
Version: 1.0.0
Author: jiang
Created: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## Package Contents:
- Complete WordPress theme
- Original Ulike assets (CSS, JS, fonts, images)
- Documentation (README.md, INSTALL.md)
- All required template files

## Installation:
1. Extract this ZIP file
2. Upload the '7magic' folder to wp-content/themes/
3. Activate the theme in WordPress admin
4. Follow INSTALL.md for detailed setup instructions

## Features:
✅ 1:1 Ulike website clone
✅ Responsive design
✅ Elementor compatible
✅ Amazon integration
✅ SEO optimized
✅ Performance optimized

## Support:
- Read README.md for full documentation
- Check INSTALL.md for setup instructions
- Contact theme author for support

---
Original design by Ulike (www.ulike.com)
WordPress theme by jiang
"@

$packageInfo | Out-File -FilePath "$tempDir\PACKAGE-INFO.txt" -Encoding UTF8

# Create installation script
$installScript = @"
@echo off
echo.
echo ================================
echo 7Magic WordPress Theme Installer
echo ================================
echo.
echo This will copy the theme to your WordPress installation.
echo.
set /p wppath="Enter your WordPress path (e.g., C:\xampp\htdocs\mysite): "
echo.

if not exist "%wppath%\wp-config.php" (
    echo Error: WordPress installation not found at %wppath%
    pause
    exit /b 1
)

if not exist "%wppath%\wp-content\themes" (
    echo Error: Themes directory not found
    pause
    exit /b 1
)

echo Copying theme files...
xcopy "7magic" "%wppath%\wp-content\themes\7magic\" /E /I /Y

if %errorlevel% == 0 (
    echo.
    echo ✅ Theme installed successfully!
    echo.
    echo Next steps:
    echo 1. Login to WordPress admin
    echo 2. Go to Appearance ^> Themes
    echo 3. Activate "7Magic" theme
    echo 4. Read INSTALL.md for configuration
    echo.
) else (
    echo.
    echo ❌ Installation failed!
    echo Please copy the 7magic folder manually to wp-content/themes/
    echo.
)

pause
"@

$installScript | Out-File -FilePath "$tempDir\install-theme.bat" -Encoding ASCII

# Create file list
Write-Host "Generating file list..." -ForegroundColor Yellow
$fileList = @"
# 7Magic Theme File List
Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")

## Core Files:
"@

Get-ChildItem "$tempDir\7magic" -Recurse -File | ForEach-Object {
    $relativePath = $_.FullName.Replace("$PWD\$tempDir\", "")
    $fileSize = [math]::Round($_.Length / 1KB, 2)
    $fileList += "`n$relativePath ($fileSize KB)"
}

$fileList | Out-File -FilePath "$tempDir\FILE-LIST.txt" -Encoding UTF8

# Create ZIP package
Write-Host "Creating ZIP package..." -ForegroundColor Yellow

if (Test-Path $packageName) {
    Remove-Item $packageName -Force
}

# Use PowerShell compression
Compress-Archive -Path "$tempDir\*" -DestinationPath $packageName -CompressionLevel Optimal

# Clean up temporary directory
Remove-Item $tempDir -Recurse -Force

# Display results
if (Test-Path $packageName) {
    $packageSize = [math]::Round((Get-Item $packageName).Length / 1MB, 2)
    Write-Host "`n✅ Package created successfully!" -ForegroundColor Green
    Write-Host "📦 File: $packageName" -ForegroundColor Cyan
    Write-Host "📏 Size: $packageSize MB" -ForegroundColor Cyan
    Write-Host "`n📋 Package Contents:" -ForegroundColor Yellow
    Write-Host "   • 7magic/ - Complete WordPress theme" -ForegroundColor White
    Write-Host "   • PACKAGE-INFO.txt - Package information" -ForegroundColor White
    Write-Host "   • install-theme.bat - Windows installer script" -ForegroundColor White
    Write-Host "   • FILE-LIST.txt - Complete file listing" -ForegroundColor White
    
    Write-Host "`n🚀 Ready for distribution!" -ForegroundColor Green
    Write-Host "   Users can extract and install this theme package." -ForegroundColor White
} else {
    Write-Host "`n❌ Package creation failed!" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
