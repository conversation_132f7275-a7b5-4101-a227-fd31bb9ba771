<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff8e8e;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="30" cy="30" r="25" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- Main logo text -->
  <text x="65" y="25" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="url(#logoGradient)">7Magic</text>
  
  <!-- Tagline -->
  <text x="65" y="42" font-family="Arial, sans-serif" font-size="10" fill="#666">Beauty Technology</text>
  
  <!-- Magic wand icon -->
  <g transform="translate(20, 20)">
    <line x1="5" y1="15" x2="15" y2="5" stroke="url(#logoGradient)" stroke-width="2" stroke-linecap="round"/>
    <circle cx="16" cy="4" r="1.5" fill="url(#logoGradient)"/>
    <circle cx="18" cy="6" r="1" fill="url(#logoGradient)" opacity="0.7"/>
    <circle cx="14" cy="2" r="0.8" fill="url(#logoGradient)" opacity="0.5"/>
  </g>
</svg>
