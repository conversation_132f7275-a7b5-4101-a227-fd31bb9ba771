<?php
/**
 * The template for displaying all pages
 *
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>
                
                <header class="entry-header">
                    <h1 class="entry-title page-title"><?php the_title(); ?></h1>
                </header>
                
                <div class="entry-content">
                    <?php
                    the_content();
                    
                    wp_link_pages(array(
                        'before' => '<div class="page-links">' . esc_html__('Pages:', '7magic'),
                        'after'  => '</div>',
                    ));
                    ?>
                </div>
                
                <?php if (comments_open() || get_comments_number()) : ?>
                    <div class="comments-section">
                        <?php comments_template(); ?>
                    </div>
                <?php endif; ?>
                
            </article>
            
        <?php endwhile; ?>
        
    </div>
</main>

<style>
/* Page-specific styles */
.page-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
    position: relative;
}

.page-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    margin: 1rem auto;
    border-radius: 2px;
}

.entry-content {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #555;
}

.entry-content h2,
.entry-content h3,
.entry-content h4 {
    color: #333;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.entry-content h2 {
    font-size: 2rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.5rem;
}

.entry-content h3 {
    font-size: 1.5rem;
    color: #ff6b6b;
}

.entry-content p {
    margin-bottom: 1.5rem;
}

.entry-content ul,
.entry-content ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.entry-content li {
    margin-bottom: 0.5rem;
}

.entry-content blockquote {
    background: #f9f9f9;
    border-left: 4px solid #ff6b6b;
    padding: 1rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #666;
}

.entry-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin: 1rem 0;
}

.page-links {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
    text-align: center;
}

.page-links a {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 4px;
    background: #f0f0f0;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.page-links a:hover {
    background: #ff6b6b;
    color: white;
}

.comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid #f0f0f0;
}

/* Responsive */
@media (max-width: 768px) {
    .page-content {
        padding: 1rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .entry-content {
        font-size: 1rem;
    }
    
    .entry-content h2 {
        font-size: 1.5rem;
    }
    
    .entry-content h3 {
        font-size: 1.3rem;
    }
}
</style>

<?php get_footer(); ?>
