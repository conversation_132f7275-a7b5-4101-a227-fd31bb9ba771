import.meta;var Ya=Object.defineProperty,Vt=(t,e)=>(e=Symbol[t])?e:Symbol.for("Symbol."+t),Hr=t=>{throw TypeError(t)},Ka=(t,e,n)=>e in t?Ya(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,c=(t,e,n)=>Ka(t,typeof e!="symbol"?e+"":e,n),Gr=(t,e,n)=>e.has(t)||Hr("Cannot "+n),A=(t,e,n)=>(Gr(t,e,"read from private field"),n?n.call(t):e.get(t)),$=(t,e,n)=>e.has(t)?Hr("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),x=(t,e,n,r)=>(Gr(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),X=function(t,e){this[0]=t,this[1]=e},Le=(t,e,n)=>{var r=(o,s,l,u)=>{try{var d=n[o](s),h=(s=d.value)instanceof X,p=d.done;Promise.resolve(h?s[0]:s).then(m=>h?r(o==="return"?o:"next",s[1]?{done:m.done,value:m.value}:m,l,u):l({value:m,done:p})).catch(m=>r("throw",m,l,u))}catch(m){u(m)}},a=o=>i[o]=s=>new Promise((l,u)=>r(o,s,l,u)),i={};return n=n.apply(t,e),i[Vt("asyncIterator")]=()=>i,a("next"),a("throw"),a("return"),i},Z=(t,e,n)=>(e=t[Vt("asyncIterator")])?e.call(t):(t=t[Vt("iterator")](),e={},n=(r,a)=>(a=t[r])&&(e[r]=i=>new Promise((o,s,l)=>(i=a.call(t,i),l=i.done,Promise.resolve(i.value).then(u=>o({value:u,done:l}),s)))),n("next"),n("return"),e);const Qa=":host{display:flex;align-items:center;justify-content:center}";class Ja extends HTMLElement{constructor(){super(...arguments),c(this,"size","18px"),c(this,"color","gray")}connectedCallback(){var e,n;this.attachShadow({mode:"open"}),this.size=(e=this.getAttribute("size"))!=null?e:this.size,this.color=(n=this.getAttribute("color"))!=null?n:this.color,this.render();const r=document.createElement("style");r.textContent=Qa,this.shadowRoot.appendChild(r)}render(){const e=this.shadowRoot,n='\n      width="'.concat(this.size,'"\n      height="').concat(this.size,'"\n      xmlns="http://www.w3.org/2000/svg"\n      aria-hidden="true"\n    ');e.innerHTML="\n      <svg ".concat(n,' viewBox="0 0 20 20" fill="').concat(this.color,'">\n        <path d="M17.1 4.3l-1.4-1.4-5.7 5.7-5.7-5.7-1.4 1.4 5.7 5.7-5.7 5.7 1.4 1.4 5.7-5.7 5.7 5.7 1.4-1.4-5.7-5.7z"/>\n      </svg>\n    ')}}const Xa="#overlay{position:fixed;width:100%;height:100%;background:#0006;top:0;left:0;z-index:2147483647;animation:modalPop .3s ease-out}#modal{position:fixed;top:20%;left:50%;width:100%;max-width:383px;transform:translate(-50%,-100%);background:#fff;color:#000;border-radius:5px;animation:modalSlideInFromTop .3s forwards}@keyframes modalPop{0%{opacity:0}to{opacity:1}}@keyframes modalSlideInFromTop{0%{transform:translate(-50%,-100%)}to{transform:translate(-50%)}}@keyframes modalSlideInFromBottom{0%{transform:translate(-50%,100%)}to{transform:translate(-50%)}}@media only screen and (max-width: 640px){#modal{top:auto;bottom:0;animation:modalSlideInFromBottom .3s forwards}}#modal footer{padding:0 21px 21px}#modal header{display:flex;justify-content:space-between;padding:21px 21px 16px}#title{font-size:21px;font-weight:600;line-height:25.2px;margin:0}.capitalize:first-letter{display:inline-block;text-transform:capitalize}#content{text-align:left;padding:0 21px 16px;overflow:auto;max-height:50vh}#modal #content p{margin:0;font-size:14px;line-height:21px}#close-icon,#close-button{cursor:pointer}#close-icon{min-width:24px;background:transparent;padding:0;border:none}#close-button{width:100%;padding:16px 21px;color:#fff;background-color:#1773b0;border:none;border-radius:5px;font-size:14px;line-height:21px;font-family:inherit}#close-button:hover,#close-button:active{background:#136f99}#close-button:active,#close-button:focus{box-shadow:0 0 0 4px #1990c640}";function Za(t){return Tn(t).map(e=>e instanceof Error?e:new Nn("[".concat(typeof e,"] ").concat(ei(e).slice(0,10240))))}function Tn(t,e=0){return e>=20?[t,"Truncated cause stack"]:t instanceof Error&&t.cause?[t,...Tn(t.cause,e+1)]:[t]}function ei(t){var e;if(typeof t!="string")try{return(e=JSON.stringify(t))!=null?e:typeof t}catch(n){}return"".concat(t)}var Nn=class extends Error{constructor(){super(...arguments),c(this,"name","BugsnagInvalidError")}},kn=/^\s*at .*(\S+:\d+|\(native\))/m,ti=/^(eval@)?(\[native code])?$/;function ri(t){return t.stack?t.stack.match(kn)?ni(t):ai(t):[]}function Ln(t){if(t.indexOf(":")===-1)return[t];let e=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(t.replace(/[()]/g,""));return[e[1],e[2]?Number(e[2]):void 0,e[3]?Number(e[3]):void 0]}function ni(t){return t.stack.split("\n").filter(e=>!!e.match(kn)).map(e=>{let n=e.replace(/^\s+/,"").replace(/^.*?\s+/,""),r=n.match(/ (\(.+\)$)/);n=r?n.replace(r[0],""):n;let a=Ln(r?r[1]:n),i=r&&n||void 0,o=["eval","<anonymous>"].indexOf(a[0])>-1?void 0:a[0];return{method:i,file:o,lineNumber:a[1],columnNumber:a[2]}})}function ai(t){return t.stack.split("\n").filter(e=>!e.match(ti)).map(e=>{if(e.indexOf("@")===-1&&e.indexOf(":")===-1)return{method:e};let n=/((.*".+"[^@]*)?[^@]*)(?:@)/,r=e.match(n),a=r&&r[1]?r[1]:void 0,i=Ln(e.replace(n,""));return{method:a,file:i[0],lineNumber:i[1],columnNumber:i[2]}})}var qt="5",ii=class{constructor(t){c(this,"breadcrumbs",[]),c(this,"apiKey"),c(this,"plugins"),c(this,"appId"),c(this,"appType"),c(this,"appVersion"),c(this,"releaseStage"),c(this,"locale"),c(this,"userAgent"),c(this,"metadata"),c(this,"persistedMetadata"),c(this,"onError"),c(this,"onPostErrorListeners",[]),c(this,"endpoints"),c(this,"session");var e,n,r;this.apiKey=t.apiKey,this.appType=t.appType,this.appId=t.appId,this.appVersion=t.appVersion,this.releaseStage=t.releaseStage,this.locale=t.locale,this.userAgent=t.userAgent,this.metadata=t.metadata,this.onError=t.onError,this.persistedMetadata={},this.endpoints=(e=t.endpoints)!=null?e:{notify:"https://error-analytics-production.shopifysvc.com",sessions:"https://error-analytics-sessions-production.shopifysvc.com/observeonly"},this.plugins=(n=t.plugins)!=null?n:[],this.plugins.forEach(a=>a.load(this)),this.leaveBreadcrumb("Bugsnag started",void 0,"state"),((r=t.withSessionTracking)==null||r)&&(this.session={id:this.getRandomUUID(),startedAt:new Date().toISOString(),events:{handled:0,unhandled:0}},this.startSession())}addMetadata(t){for(let e of Object.keys(t))this.persistedMetadata[e]=t[e]}getSessionId(){var t;return(t=this.session)==null?void 0:t.id}leaveBreadcrumb(t,e,n="manual"){this.breadcrumbs.push({name:t,metaData:e,type:n,timestamp:new Date().toISOString()})}notify(t,{errorClass:e,severity:n,severityType:r,handled:a=!0,metadata:i,context:o,groupingHash:s}={}){var l,u;let d=Za(t),h={...this.metadata,...this.persistedMetadata,...i},p=this.buildBugsnagEvent(d,{errorClass:e,severityType:r,handled:a,severity:n,metadata:h,context:o,groupingHash:s});if(((u=(l=this.onError)==null?void 0:l.call(this,p,t))==null||u)&&this.releaseStage!=="development"){this.updateAndAppendSessionInformation(p);let m=this.sendToBugsnag(p);return this.onPostErrorListeners.forEach(_=>_(p)),m}return Promise.resolve()}addOnPostError(t){this.onPostErrorListeners.push(t)}updateAndAppendSessionInformation(t){this.session&&(t.unhandled?this.session.events.unhandled++:this.session.events.handled++,t.session=this.session)}buildBugsnagEvent(t,{errorClass:e,severity:n="error",severityType:r="handledException",handled:a,metadata:i={},context:o,groupingHash:s}){let l=new Date().toISOString(),{breadcrumbs:u,appId:d,appType:h,appVersion:p,releaseStage:m,locale:_,userAgent:w}=this,E=t.map((S,z)=>({errorClass:z===0&&e!=null?e:S.name,stacktrace:On(d,S),message:S.message,type:"browserjs"}));return{payloadVersion:qt,exceptions:E,severity:n,severityReason:{type:r},unhandled:!a,app:{id:d,type:h,version:p,releaseStage:m},device:{time:l,locale:_,userAgent:w},breadcrumbs:u,context:o,metaData:i,groupingHash:s}}async startSession(){var t,e;if(this.releaseStage==="development"){console.log("Skipping error logging session tracking in development mode");return}let{apiKey:n}=this,r={notifier:{name:"Bugsnag JavaScript",version:"7.22.2",url:"https://github.com/bugsnag/bugsnag-js"},app:{version:this.appVersion,releaseStage:this.releaseStage,type:this.appType},device:{id:this.appId,locale:this.locale,userAgent:this.userAgent},sessions:[this.session]};try{await fetch(this.endpoints.sessions,{method:"POST",headers:{"Content-Type":"application/json","Bugsnag-Api-Key":n,"Bugsnag-Payload-Version":qt,"Bugsnag-Sent-At":(e=(t=this.session)==null?void 0:t.startedAt)!=null?e:new Date().toISOString()},body:JSON.stringify(r)})}catch(a){console.warn("[bugsnag-light] failed to start session"),console.warn(a)}}async sendToBugsnag(t){let{apiKey:e}=this,n={apiKey:e,notifier:{name:"Bugsnag JavaScript",version:"7.22.2",url:"https://github.com/bugsnag/bugsnag-js"},events:[t]};try{await fetch(this.endpoints.notify,{method:"POST",headers:{"Content-Type":"application/json","Bugsnag-Api-Key":e,"Bugsnag-Payload-Version":qt,"Bugsnag-Sent-At":t.device.time},body:JSON.stringify(n)})}catch(r){console.warn("[bugsnag-light] failed to send an event"),console.warn(r)}}getRandomUUID(){try{return crypto.randomUUID()}catch(t){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let n=Math.random()*16|0;return(e==="x"?n:n&3|8).toString(16)})}}};function On(t,e){let n=ri(e).map(r=>{var a,i,o,s;let l=(a=r.file)==null?void 0:a.includes(t);return{method:(i=r.method)!=null?i:"",file:(o=r.file)!=null?o:"",lineNumber:(s=r.lineNumber)!=null?s:0,columnNumber:r.columnNumber,inProject:l}});if(e instanceof Nn){let r=n.findIndex(a=>a.method.endsWith("notify"));r>-1&&(n=n.slice(r+1))}return n}var oi=class Su extends Error{constructor(e){super(e),c(this,"reason"),this.name="BreadcrumbsPluginFetchError",Object.setPrototypeOf(this,Su.prototype)}};function si(t,{metadata:e}={}){let n=window.onerror;window.onerror=(r,a,i,o,s)=>{s&&t.notify(s,{severityType:"unhandledException",handled:!1,metadata:e}),typeof n=="function"&&n.apply(window.onerror,[r,a,i,o,s])}}function li(t,{metadata:e}={}){window.addEventListener("unhandledrejection",n=>{n.reason&&!(n.reason instanceof oi)&&t.notify(n.reason,{severityType:"unhandledPromiseRejection",handled:!1,metadata:e})})}function ct(t){try{const e=new RegExp("(^| )".concat(t,"=([^;]+)")).exec(document.cookie);if(e){const n=e[2];try{return decodeURIComponent(n)}catch(r){return n}}return null}catch(e){return null}}const or={BRANDED_BUTTON:"shopify-payment-button__button shopify-payment-button__button--branded",UNBRANDED_BUTTON:"shopify-payment-button__button shopify-payment-button__button--unbranded",MORE_PAYMENT_OPTION_BUTTON:"shopify-payment-button__more-options"},ci={DARK:"apple-pay--dark",LIGHT:"apple-pay--light"},di=44,ui="_shopify_y",Mn=6,hi="discount_code",wr="https://static-na.payments-amazon.com",_r="".concat(wr,"/checkout.js"),Un="https://www.paypal.com",Bn="https://pay.google.com",we={CURRENCY_CHANGE:"wallet_currency_change",CAPTCHA_REQUIRED:"wallet_captcha_required",NOT_ENOUGH_STOCK:"wallet_not_enough_stock",CART_NOT_READY:"wallet_cart_not_ready",DYNAMIC_TAX:"wallet_dynamic_tax",PAYMENT_METHOD_NOT_APPLICABLE:"wallet_payment_method_not_applicable",INVALID_PAYMENT_DEFERRED_PAYMENT_REQUIRED:"wallet_invalid_payment_deferred_payment_required"},Et="VALIDATION_CUSTOM",Fn="portable-wallets",Te={BUTTON:"accelerated-checkout-button",CONTAINER:"accelerated-checkout-button-container"},$n="unknown",zn={name:"buy_it_now",wallet_params:{}};function Hn(t){try{return!t.toString().includes("[native code]")}catch(e){return!0}}const Wt=new Map,pi={checkDuplicate({errorClass:t,message:e}){if(Wt.has(t)){const n=Wt.get(t);if(n.has(e))return!0;n.add(e)}else Wt.set(t,new Set([e]));return!1},reset(){}},mt="DeveloperError";class J extends Error{constructor({code:e,message:n},r){const a="An unexpected error happened likely because of customizations made to HTML/JavaScript on this site ".concat(n);super("[".concat(e,"]: ").concat(a),r),c(this,"name",mt),c(this,"code"),this.code=e}}const Gn="SilencedError";class re extends Error{constructor(){super(...arguments),c(this,"name",Gn)}}class mi extends Error{constructor(){super(...arguments),c(this,"name","MissingConfigError")}}class _e extends Error{constructor(){super(...arguments),c(this,"name","NoResponseError")}}class Er extends Error{constructor(e,{errorDetails:n,groupingHash:r}){super(e),c(this,"errorDetails"),c(this,"groupingHash"),this.name="GraphQLResponseError",this.errorDetails=n,this.groupingHash=r}}const Vn="IgnoredGraphQLResponseError";class sr extends Er{constructor(e,{reason:n,errorDetails:r}){super(e,{errorDetails:r}),c(this,"reason"),this.name=Vn,this.reason=n}}class qn extends Error{constructor(e,{groupingHash:n,...r}){super(e,r),c(this,"groupingHash"),this.groupingHash=n}}function D(t,e){if(!{}.hasOwnProperty.call(t,e))throw new TypeError("attempted to use private field on non-instance");return t}var yi=0;function ye(t){return"__private_"+yi+++"_"+t}function Ot(t){return Object.entries(t).map(([e,n])=>({key:e,value:{stringValue:String(n)}}))}function gi(t){return Object.entries(t).map(([e,n])=>({key:e,value:Wn(n)}))}function Wn(t){if(Array.isArray(t))return{arrayValue:{values:t.map(e=>Wn(e))}};switch(typeof t){case"boolean":return{boolValue:!!t};case"number":return{doubleValue:Number(t)};case"string":default:return{stringValue:String(t)}}}const Ar=1,fi=bi(5,2,12);function bi(t,e,n){const r=[0];for(let a=0;a<n;a++){const i=Math.floor(t*e**a);r.push(i)}return r}var Ee=ye("exporter"),K=ye("attributes"),V=ye("metrics"),Ae=ye("logs");class wi{constructor({exporter:e,attributes:n}){Object.defineProperty(this,Ee,{writable:!0,value:void 0}),Object.defineProperty(this,K,{writable:!0,value:void 0}),Object.defineProperty(this,V,{writable:!0,value:[]}),Object.defineProperty(this,Ae,{writable:!0,value:[]}),D(this,Ee)[Ee]=e,D(this,K)[K]=n!=null?n:{}}addAttributes(e){D(this,K)[K]={...D(this,K)[K],...e}}histogram({name:e,value:n,unit:r,bounds:a,attributes:i,scale:o}){const s=Date.now()*1e6;a?D(this,V)[V].push({name:e,type:"histogram",value:n,unit:r,timeUnixNano:s,attributes:i,bounds:a}):D(this,V)[V].push({name:e,type:"exponential_histogram",value:n,unit:r,timeUnixNano:s,attributes:i,scale:o})}counter({name:e,value:n,unit:r,attributes:a}){const i=Date.now()*1e6;D(this,V)[V].push({name:e,type:"counter",value:n,unit:r,timeUnixNano:i,attributes:a})}gauge({name:e,value:n,unit:r,attributes:a}){const i=Date.now()*1e6;D(this,V)[V].push({name:e,type:"gauge",value:n,unit:r,timeUnixNano:i,attributes:a})}log({body:e,attributes:n}){const r=Date.now()*1e6;D(this,Ae)[Ae].push({timeUnixNano:r,body:e,attributes:n})}async exportMetrics(){const e={};D(this,V)[V].forEach(r=>{switch(r.attributes={...D(this,K)[K],...r.attributes},r.type){case"histogram":_i(e,r);break;case"exponential_histogram":Ei(e,r);break;case"counter":Ai(e,r);break;case"gauge":Ci(e,r);break}});const n=Object.values(e);n.length!==0&&(D(this,V)[V]=[],await D(this,Ee)[Ee].exportMetrics(n))}async exportLogs(){const e=D(this,Ae)[Ae].map(n=>{const r={timeUnixNano:n.timeUnixNano,observedTimeUnixNano:n.timeUnixNano,attributes:gi({...D(this,K)[K],...n.attributes})};return n.body&&(r.body={stringValue:n.body}),r});e.length!==0&&(D(this,Ae)[Ae]=[],await D(this,Ee)[Ee].exportLogs(e))}}function _i(t,e){var n;const{name:r,value:a,unit:i,timeUnixNano:o,attributes:s}=e,l=(n=e.bounds)!==null&&n!==void 0?n:fi,u=new Array(l.length+1).fill(0);t[r]||(t[r]={name:r,unit:i||"1",histogram:{aggregationTemporality:Ar,dataPoints:[]}});for(let d=0;d<u.length;d++){const h=l[d];if(h===void 0)u[d]=1;else if(a<=h){u[d]=1;break}}t[r].histogram.dataPoints.push({startTimeUnixNano:o,timeUnixNano:o,count:1,sum:a,min:a,max:a,bucketCounts:u,explicitBounds:l,attributes:Ot(s!=null?s:{})})}function Ei(t,e){const{name:n,value:r,unit:a,timeUnixNano:i,attributes:o,scale:s}=e;t[n]||(t[n]={name:n,unit:a||"1",exponentialHistogram:{aggregationTemporality:Ar,dataPoints:[]}});const l=r<=0?0:r,u=s||3,d=2**u/Math.log(2),h=Math.ceil(Math.log(r)*d)-1,p=r<=0?1:0,m={offset:0,bucketCounts:[]},_={offset:r>0?h:0,bucketCounts:r>0?[1]:[]};t[n].exponentialHistogram.dataPoints.push({attributes:Ot(o!=null?o:{}),startTimeUnixNano:i,timeUnixNano:i,count:1,sum:l,scale:u,zeroCount:p,positive:_,negative:m,min:l,max:l,zeroThreshold:0})}function Ai(t,e){const{name:n,value:r,unit:a,timeUnixNano:i,attributes:o}=e;t[n]||(t[n]={name:n,unit:a||"1",sum:{aggregationTemporality:Ar,isMonotonic:!0,dataPoints:[]}}),t[n].sum.dataPoints.push({startTimeUnixNano:i,timeUnixNano:i,asDouble:r,attributes:Ot(o!=null?o:{})})}function Ci(t,e){const{name:n,value:r,unit:a,timeUnixNano:i,attributes:o}=e;t[n]||(t[n]={name:n,unit:a||"1",gauge:{dataPoints:[]}}),t[n].gauge.dataPoints.push({startTimeUnixNano:i,timeUnixNano:i,asDouble:r,attributes:Ot(o!=null?o:{})})}var Xe=ye("url"),Ce=ye("serviceName"),Ze=ye("logger"),et=ye("fetchFn");class Si{constructor(e,n,r){Object.defineProperty(this,Xe,{writable:!0,value:void 0}),Object.defineProperty(this,Ce,{writable:!0,value:void 0}),Object.defineProperty(this,Ze,{writable:!0,value:void 0}),Object.defineProperty(this,et,{writable:!0,value:void 0}),D(this,Xe)[Xe]=e.replace(/\/v1\/(logs|metrics|traces)\/?$/,""),D(this,Ce)[Ce]=n,D(this,Ze)[Ze]=r==null?void 0:r.logger,D(this,et)[et]=r==null?void 0:r.fetchFn}async exportMetrics(e){const n={resourceMetrics:[{resource:{attributes:[{key:"service.name",value:{stringValue:D(this,Ce)[Ce]}}]},scopeMetrics:[{scope:{name:"open-telemetry-mini-client",version:"1.1.0",attributes:[]},metrics:e}]}]};await this.exportTo(n,"/v1/metrics")}async exportLogs(e){const n={resourceLogs:[{resource:{attributes:[{key:"service.name",value:{stringValue:D(this,Ce)[Ce]}}]},scopeLogs:[{scope:{name:"open-telemetry-mini-client",version:"1.1.0",attributes:[]},logRecords:e}]}]};await this.exportTo(n,"/v1/logs")}async exportTo(e,n){var r;const a=await this.exporterFetch()("".concat(D(this,Xe)[Xe]).concat(n),{method:"POST",keepalive:!0,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if((r=D(this,Ze)[Ze])===null||r===void 0||r.log({status:a.status}),!a.ok){if(a.status===400){const i=await a.text();throw new it("Invalid OpenTelemetry Data: ".concat(i))}if(a.status===429||a.status===503){const i=await a.text(),o=a.headers.get("Retry-After"),s=o?{seconds:Number(o)}:void 0;throw new it("Server did not accept data",{errorData:i,retryAfter:s,body:e})}throw new it("Server responded with ".concat(a.status))}}exporterFetch(){return D(this,et)[et]||fetch}}class it extends Error{constructor(e,n){super(e),this.metadata=void 0,this.name="OpenTelemetryClientError",this.metadata=n}}const Ii="https://otlp-http-production.shopifysvc.com",Pi="portable_wallets";var He;class vi{constructor(e){$(this,He),x(this,He,e)}async exportMetrics(e){var n;try{await A(this,He).exportMetrics(e)}catch(r){if(r instanceof it){const a=(n=r.metadata)==null?void 0:n.retryAfter;if(a){await new Promise(i=>{setTimeout(()=>{this.exportMetrics(e),i()},a.seconds*1e3)});return}}throw new Vr("Unable to export metrics",{cause:r})}}async exportLogs(e){var n;try{await A(this,He).exportLogs(e)}catch(r){if(r instanceof it){const a=(n=r.metadata)==null?void 0:n.retryAfter;if(a){await new Promise(i=>{setTimeout(()=>{this.exportLogs(e),i()},a.seconds*1e3)});return}}throw new Vr("Unable to export logs",{cause:r})}}}He=new WeakMap;const Di=new Si(Ii,Pi),Ri=new vi(Di),C=new wi({exporter:Ri}),jn="TelemetryExportError";class Vr extends Error{constructor(){super(...arguments),c(this,"name",jn)}}const xt="GraphQL Client",qr=0,Wr=3,Yn="An error occurred while fetching from the API. Review 'graphQLErrors' for details.",Kn="Response returned unexpected Content-Type:",Qn="An unknown error has occurred. The API did not return a data object or any errors in its response.",lr={json:"application/json",multipart:"multipart/mixed"},jr="X-SDK-Variant",Yr="X-SDK-Version",Ti="shopify-graphql-client",Ni="1.4.1",Jn=1e3,ki=[429,503],Xn=/@(defer)\b/i,Kr="\r\n",Li=/boundary="?([^=";]+)"?/i,Qr=Kr+Kr;function he(t,e=xt){return t.startsWith("".concat(e))?t:"".concat(e,": ").concat(t)}function Qe(t){return t instanceof Error?t.message:JSON.stringify(t)}function Zn(t){return t instanceof Error&&t.cause?t.cause:void 0}function ea(t){return t.flatMap(({errors:e})=>e!=null?e:[])}function ta({client:t,retries:e}){if(e!==void 0&&(typeof e!="number"||e<qr||e>Wr))throw new Error("".concat(t,': The provided "retries" value (').concat(e,") is invalid - it cannot be less than ").concat(qr," or greater than ").concat(Wr))}function G(t,e){return e&&(typeof e!="object"||Array.isArray(e)||typeof e=="object"&&Object.keys(e).length>0)?{[t]:e}:{}}function ra(t,e){if(t.length===0)return e;const n={[t.pop()]:e};return t.length===0?n:ra(t,n)}function na(t,e){return Object.keys(e||{}).reduce((n,r)=>(typeof e[r]=="object"||Array.isArray(e[r]))&&t[r]?(n[r]=na(t[r],e[r]),n):(n[r]=e[r],n),Array.isArray(t)?[...t]:{...t})}function aa([t,...e]){return e.reduce(na,{...t})}function Oi({clientLogger:t,customFetchApi:e=fetch,client:n=xt,defaultRetryWaitTime:r=Jn,retriableCodes:a=ki}){const i=async(o,s,l)=>{const u=s+1,d=l+1;let h;try{if(h=await e(...o),t({type:"HTTP-Response",content:{requestParams:o,response:h}}),!h.ok&&a.includes(h.status)&&u<=d)throw new Error;const p=(h==null?void 0:h.headers.get("X-Shopify-API-Deprecated-Reason"))||"";return p&&t({type:"HTTP-Response-GraphQL-Deprecation-Notice",content:{requestParams:o,deprecationNotice:p}}),h}catch(p){if(u<=d){const m=h==null?void 0:h.headers.get("Retry-After");return await xi(m?parseInt(m,10):r),t({type:"HTTP-Retry",content:{requestParams:o,lastResponse:h,retryAttempt:s,maxRetries:l}}),i(o,u,l)}throw new Error(he("".concat(l>0?"Attempted maximum number of ".concat(l," network retries. Last message - "):"").concat(Qe(p)),n))}};return i}async function xi(t){return new Promise(e=>setTimeout(e,t))}function Mi({headers:t,url:e,customFetchApi:n=fetch,retries:r=0,logger:a}){ta({client:xt,retries:r});const i={headers:t,url:e,retries:r},o=Ui(a),s=Oi({customFetchApi:n,clientLogger:o,defaultRetryWaitTime:Jn}),l=Bi(s,i),u=Fi(l),d=Wi(l);return{config:i,fetch:l,request:u,requestStream:d}}function Ui(t){return e=>{t&&t(e)}}async function ia(t){const{errors:e,data:n,extensions:r}=await t.json();return{...G("data",n),...G("extensions",r),headers:t.headers,...e||!n?{errors:{networkStatusCode:t.status,message:he(e?Yn:Qn),...G("graphQLErrors",e),response:t}}:{}}}function Bi(t,{url:e,headers:n,retries:r}){return async(a,i={})=>{const{variables:o,headers:s,url:l,retries:u,keepalive:d,signal:h}=i,p=JSON.stringify({query:a,variables:o});ta({client:xt,retries:u});const m=Object.entries({...n,...s}).reduce((w,[E,S])=>(w[E]=Array.isArray(S)?S.join(", "):S.toString(),w),{});!m[jr]&&!m[Yr]&&(m[jr]=Ti,m[Yr]=Ni);const _=[l!=null?l:e,{method:"POST",headers:m,body:p,signal:h,keepalive:d}];return t(_,1,u!=null?u:r)}}function Fi(t){return async(...e)=>{if(Xn.test(e[0]))throw new Error(he("This operation will result in a streamable response - use requestStream() instead."));let n=null;try{n=await t(...e);const{status:r,statusText:a}=n,i=n.headers.get("content-type")||"";return n.ok?i.includes(lr.json)?await ia(n):{errors:{networkStatusCode:r,message:he("".concat(Kn," ").concat(i)),response:n}}:{errors:{networkStatusCode:r,message:he(a),response:n}}}catch(r){return{errors:{message:Qe(r),...n==null?{}:{networkStatusCode:n.status,response:n}}}}}}function $i(t){return Le(this,null,function*(){const e=new TextDecoder;if(t.body[Symbol.asyncIterator])try{for(var n=Z(t.body),r,a,i;r=!(a=yield new X(n.next())).done;r=!1){const o=a.value;yield e.decode(o)}}catch(o){i=[o]}finally{try{r&&(a=n.return)&&(yield new X(a.call(n)))}finally{if(i)throw i[0]}}else{const o=t.body.getReader();let s;try{for(;!(s=yield new X(o.read())).done;)yield e.decode(s.value)}finally{o.cancel()}}})}function zi(t,e){return{[Symbol.asyncIterator](){return Le(this,null,function*(){try{let o="";try{for(var n=Z(t),r,a,i;r=!(a=yield new X(n.next())).done;r=!1){const s=a.value;if(o+=s,o.indexOf(e)>-1){const l=o.lastIndexOf(e),u=o.slice(0,l).split(e).filter(d=>d.trim().length>0).map(d=>d.slice(d.indexOf(Qr)+Qr.length).trim());u.length>0&&(yield u),o=o.slice(l+e.length),o.trim()==="--"&&(o="")}}}catch(s){i=[s]}finally{try{r&&(a=n.return)&&(yield new X(a.call(n)))}finally{if(i)throw i[0]}}}catch(o){throw new Error("Error occured while processing stream payload - ".concat(Qe(o)))}})}}}function Hi(t){return{[Symbol.asyncIterator](){return Le(this,null,function*(){yield{...yield new X(ia(t)),hasNext:!1}})}}}function Gi(t){return t.map(e=>{try{return JSON.parse(e)}catch(n){throw new Error("Error in parsing multipart response - ".concat(Qe(n)))}}).map(e=>{const{data:n,incremental:r,hasNext:a,extensions:i,errors:o}=e;if(!r)return{data:n||{},...G("errors",o),...G("extensions",i),hasNext:a};const s=r.map(({data:l,path:u,errors:d})=>({data:l&&u?ra(u,l):{},...G("errors",d)}));return{data:s.length===1?s[0].data:aa([...s.map(({data:l})=>l)]),...G("errors",ea(s)),hasNext:a}})}function Vi(t,e){if(t.length>0)throw new Error(Yn,{cause:{graphQLErrors:t}});if(Object.keys(e).length===0)throw new Error(Qn)}function qi(t,e){var n,r;const a=(e!=null?e:"").match(Li),i="--".concat(a?a[1]:"-");if(!((n=t.body)!=null&&n.getReader)&&!((r=t.body)!=null&&r[Symbol.asyncIterator]))throw new Error("API multipart response did not return an iterable body",{cause:t});const o=$i(t);let s={},l;return{[Symbol.asyncIterator](){return Le(this,null,function*(){var u,d;try{let w=!0;try{for(var h=Z(zi(o,i)),p,m,_;p=!(m=yield new X(h.next())).done;p=!1){const E=m.value,S=Gi(E);l=(d=(u=S.find(O=>O.extensions))==null?void 0:u.extensions)!=null?d:l;const z=ea(S);s=aa([s,...S.map(({data:O})=>O)]),w=S.slice(-1)[0].hasNext,Vi(z,s),yield{...G("data",s),...G("extensions",l),hasNext:w}}}catch(E){_=[E]}finally{try{p&&(m=h.return)&&(yield new X(m.call(h)))}finally{if(_)throw _[0]}}if(w)throw new Error("Response stream terminated unexpectedly")}catch(w){const E=Zn(w);yield{...G("data",s),...G("extensions",l),errors:{message:he(Qe(w)),networkStatusCode:t.status,...G("graphQLErrors",E==null?void 0:E.graphQLErrors),response:t},hasNext:!1}}})}}}function Wi(t){return async(...e)=>{if(!Xn.test(e[0]))throw new Error(he("This operation does not result in a streamable response - use request() instead."));try{const n=await t(...e),{statusText:r}=n;if(!n.ok)throw new Error(r,{cause:n});const a=n.headers.get("content-type")||"";switch(!0){case a.includes(lr.json):return Hi(n);case a.includes(lr.multipart):return qi(n,a);default:throw new Error("".concat(Kn," ").concat(a),{cause:n})}}catch(n){return{[Symbol.asyncIterator](){return Le(this,null,function*(){const r=Zn(n);yield{errors:{message:he(Qe(n)),...G("networkStatusCode",r==null?void 0:r.status),...G("response",r)},hasNext:!1}})}}}}}var g=(t=>(t.InvalidLanguage="Variable $language of type LanguageCode was provided invalid value",t.InvalidCountry="Variable $country of type CountryCode was provided invalid value",t.MissingCartId="Variable $cartId of type ID! was provided invalid value",t.BuyerIdentityInvalidCountry="Variable $buyerIdentity of type CartBuyerIdentityInput! was provided invalid value for countryCode",t.BuyerIdentityInvalidPhone="INVALID: buyerIdentity.phone",t.BuyerIdentityEmailRequired="BUYER_IDENTITY_EMAIL_REQUIRED",t.BuyerIdentityEmailDomainInvalid="BUYER_IDENTITY_EMAIL_DOMAIN_IS_INVALID",t.BuyerIdentityEmailNotExpectedPattern="BUYER_IDENTITY_EMAIL_DOES_NOT_MATCH_EXPECTED_PATTERN",t.BuyerIdentityEmailInvalid="INVALID: buyerIdentity.email",t.CaptchaCompletionRequired="CAPTCHA_COMPLETION_REQUIRED",t.CustomValidation="".concat(Et),t.RedirectToCheckoutRequired="REDIRECT_TO_CHECKOUT_REQUIRED",t.DeliveryFirstNameInvalid="DELIVERY_FIRST_NAME_INVALID",t.DeliveryFirstNameRequired="DELIVERY_FIRST_NAME_REQUIRED",t.BuyerIdentityDeliveryFirstNameRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryFirstNameContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryFirstNameTooLong="ADDRESS_FIELD_IS_TOO_LONG: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryFirstNameContainsUrl="ADDRESS_FIELD_CONTAINS_URL: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryFirstNameContainsHtmlTags="ADDRESS_FIELD_CONTAINS_HTML_TAGS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryFirstNameDoesNotMatchExpectedPattern="ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName",t.DeliveryLastNameInvalid="DELIVERY_LAST_NAME_INVALID",t.DeliveryLastNameRequired="DELIVERY_LAST_NAME_REQUIRED",t.BuyerIdentityDeliveryLastNameRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.DeliveryLastNameContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.DeliveryLastNameTooLong="ADDRESS_FIELD_IS_TOO_LONG: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.DeliveryLastNameContainsUrl="ADDRESS_FIELD_CONTAINS_URL: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.DeliveryLastNameContainsHtmlTags="ADDRESS_FIELD_CONTAINS_HTML_TAGS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.DeliveryLastNameDoesNotMatchExpectedPattern="ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName",t.BuyerIdentityDeliveryAddress1Required="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address1",t.DeliveryAddress1Required="DELIVERY_ADDRESS1_REQUIRED",t.DeliveryAddress1Invalid="DELIVERY_ADDRESS1_INVALID",t.DeliveryAddress1TooLong="ADDRESS_FIELD_IS_TOO_LONG: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address1",t.DeliveryAddress1ContainsHtmlTags="ADDRESS_FIELD_CONTAINS_HTML_TAGS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address1",t.DeliveryAddress1ContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address1",t.DeliveryAddress2Required="DELIVERY_ADDRESS2_REQUIRED",t.DeliveryAddress2AddressFieldRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address2",t.DeliveryAddress2Invalid="DELIVERY_ADDRESS2_INVALID",t.DeliveryAddress2TooLong="ADDRESS_FIELD_IS_TOO_LONG: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address2",t.DeliveryAddress2ContainsHtmlTags="ADDRESS_FIELD_CONTAINS_HTML_TAGS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address2",t.DeliveryAddress2ContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address2",t.DeliveryCityRequired="DELIVERY_CITY_REQUIRED",t.DeliveryCityAddressFieldRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.city",t.DeliveryCityInvalid="DELIVERY_CITY_INVALID",t.DeliveryCityTooLong="ADDRESS_FIELD_IS_TOO_LONG: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.city",t.DeliveryCityContainsHtmlTags="ADDRESS_FIELD_CONTAINS_HTML_TAGS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.city",t.DeliveryCityContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.city",t.DeliveryZoneNotFound="DELIVERY_ZONE_NOT_FOUND",t.DeliveryZoneRequiredForCountry="DELIVERY_ZONE_REQUIRED_FOR_COUNTRY",t.DeliveryProvinceRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.province",t.DeliveryPostalCodeInvalid="DELIVERY_POSTAL_CODE_INVALID",t.DeliveryInvalidPostalCodeForZone="DELIVERY_INVALID_POSTAL_CODE_FOR_ZONE",t.DeliveryInvalidPostalCodeForCountry="DELIVERY_INVALID_POSTAL_CODE_FOR_COUNTRY",t.DeliveryPostalCodeRequired="DELIVERY_POSTAL_CODE_REQUIRED",t.DeliveryPostalCodeAddressFieldRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip",t.DeliveryZipInvalidForProvince="INVALID_ZIP_CODE_FOR_PROVINCE",t.BuyerIdentityDeliveryZipInvalidForProvince="INVALID_ZIP_CODE_FOR_PROVINCE: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip",t.DeliveryZipInvalidForCountry="INVALID_ZIP_CODE_FOR_COUNTRY: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip",t.DeliveryPostalCodeContainsEmojis="ADDRESS_FIELD_CONTAINS_EMOJIS: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip",t.DeliveryCountryRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.country",t.DeliveryCountryInvalid="INVALID: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.country",t.DeliveryPhoneNumberRequired="DELIVERY_PHONE_NUMBER_REQUIRED",t.BuyerIdentityDeliveryPhoneNumberRequired="ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.phone",t.DeliveryOptionsPhoneNumberRequired="DELIVERY_OPTIONS_PHONE_NUMBER_REQUIRED",t.DeliveryPhoneNumberInvalid="DELIVERY_PHONE_NUMBER_INVALID",t.DeliveryPhoneDoesNotMatchExpectedPattern="ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.phone",t.MerchandiseOutOfStock="MERCHANDISE_OUT_OF_STOCK",t.MerchandiseNotEnoughStock="MERCHANDISE_NOT_ENOUGH_STOCK",t.InvalidQuantity="INVALID: input.lines.0.quantity",t.DeliveryOutOfStockAtOrigin="DELIVERY_OUT_OF_STOCK_AT_ORIGIN_LOCATION",t.DeliveryExternalPromiseUnfulfillable="DELIVERY_EXTERNAL_PROMISE_UNFULFILLABLE",t.MerchandiseNotApplicable="MERCHANDISE_NOT_APPLICABLE",t.MerchandiseVariantNotFound="MERCHANDISE_PRODUCT_VARIANT_NOT_FOUND",t.MerchandiseNotFound="MERCHANDISE_NOT_FOUND",t.MerchandiseNotPublished="MERCHANDISE_PRODUCT_NOT_PUBLISHED",t.MerchandiseNotSupportedForB2B="MERCHANDISE_SELLING_PLANS_NOT_SUPPORTED_FOR_B2B",t.MerchandiseBundleRequiresComponents="MERCHANDISE_BUNDLE_REQUIRES_COMPONENTS",t.MerchandiseGiftCardsNotSupported="MERCHANDISE_GIFT_CARDS_COMPONENTS_NOT_SUPPORTED",t.MerchandiseGiftCardGreaterThanZero="MERCHANDISE_GIFT_CARD_PRICE_MUST_BE_GREATER_THAN_ZERO",t.MerchandiseGiftCardCannotExceedLimit="MERCHANDISE_GIFT_CARD_PRICE_MUST_NOT_EXCEED_LIMIT",t.MerchandiseIdInvalid="INVALID: input.lines.0.merchandiseId",t.UnacceptablePaymentsAmount="PAYMENTS_UNACCEPTABLE_PAYMENT_AMOUNT",t.DiscountNotApplicable="DISCOUNT_NOT_APPLICABLE",t.DeliveryNotAvailable="DELIVERY_NO_DELIVERY_AVAILABLE",t.DeliveryNoStrategyAvailable="DELIVERY_NO_DELIVERY_STRATEGY_AVAILABLE",t.DeliveryNoLocalPickupStrategyAvailable="DELIVERY_LOCAL_PICKUP_NO_DELIVERY_STRATEGY_AVAILABLE",t.DeliveryLineChanged="DELIVERY_DELIVERY_LINE_DETAIL_CHANGED",t.DeliveryLocalPickupLineChanged="DELIVERY_LOCAL_PICKUP_DELIVERY_LINE_DETAIL_CHANGED",t.DeliveryNoDeliveryAvailableForMerchandise="DELIVERY_NO_DELIVERY_STRATEGY_AVAILABLE_FOR_MERCHANDISE_LINE",t.DeliveryFulfillmentConstraintNotSatisfied="DELIVERY_MUST_FULFILL_FROM_CONSTRAINT_NOT_SATISFIED",t.DeliveryFulfillmentLocationConstraint="DELIVERY_MUST_FULFILL_FROM_SAME_LOCATION_CONSTRAINT_NOT_SATISFIED",t.NoDeliveryGroupSelected="NO_DELIVERY_GROUP_SELECTED",t.DeliveryOptionInvalid="INVALID_DELIVERY_OPTION: selectedDeliveryOptions",t.DeliveryGroupInvalid="INVALID_DELIVERY_GROUP: selectedDeliveryOptions",t.DeliveryDetailChanged="DELIVERY_DETAIL_CHANGED",t.PendingDeliveryGroups="PENDING_DELIVERY_GROUPS",t.InvalidPaymentAmount="INVALID_PAYMENT: amount",t.InvalidPaymentPaymentAmount="INVALID_PAYMENT: payment.amount",t.InvalidPaymentPayment="INVALID_PAYMENT: payment",t.NewTaxMustBeAccepted="TAX_NEW_TAX_MUST_BE_ACCEPTED",t.MerchandiseExpectedPriceMismatch="MERCHANDISE_EXPECTED_PRICE_MISMATCH",t.InvalidPaymentApplePayBillingAddress="INVALID_PAYMENT: payment.walletPaymentMethod.applePayWalletContent.billingAddress",t.InvalidPaymentApplePayBillingAddressRequired="ADDRESS_FIELD_IS_REQUIRED: payment.walletPaymentMethod.applePayWalletContent.billingAddress",t.InvalidPaymentGooglePayBillingAddress="INVALID_PAYMENT: payment.walletPaymentMethod.googlePayWalletContent.billingAddress",t.InvalidPaymentBillingAddress="INVALID_PAYMENT: payment.freePaymentMethod.billingAddress",t.UnsupportedGooglePayPaymentMethod="PAYMENT_METHOD_NOT_SUPPORTED: payment.walletPaymentMethod.googlePayWalletContent",t.UnsupportedApplePayPaymentMethod="PAYMENT_METHOD_NOT_SUPPORTED: payment.walletPaymentMethod.applePayWalletContent",t.PaymentMethodNotApplicable="PAYMENT_METHOD_NOT_APPLICABLE: payment",t.InvalidPaymentDeferredPaymentRequired="INVALID_PAYMENT_DEFERRED_PAYMENT_REQUIRED: payment",t.PaymentsMethodRequired="PAYMENTS_METHOD_REQUIRED",t.PaymentsMethodUnavailable="PAYMENTS_METHOD_UNAVAILABLE",t.SellingPlanNotApplicable="SELLING_PLAN_NOT_APPLICABLE: input.lines.0.sellingPlanId",t.VariantRequiresSellingPlan="VARIANT_REQUIRES_SELLING_PLAN: input.lines.0",t.PaymentsCountryInvalid="PAYMENTS_COUNTRY_INVALID",t))(g||{});const ji=["DELIVERY_FIRST_NAME_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName"],Yi=["DELIVERY_FIRST_NAME_INVALID","ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.firstName"],Ki=["DELIVERY_LAST_NAME_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName"],Qi=["DELIVERY_LAST_NAME_INVALID","ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.lastName"],Ji=["DELIVERY_ADDRESS1_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address1"],Xi=["DELIVERY_ADDRESS2_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.address2"],Zi=["DELIVERY_CITY_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.city"],eo=["DELIVERY_POSTAL_CODE_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip"],oa=["DELIVERY_POSTAL_CODE_INVALID","INVALID_ZIP_CODE_FOR_COUNTRY: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip","INVALID_ZIP_CODE_FOR_PROVINCE","DELIVERY_INVALID_POSTAL_CODE_FOR_ZONE","DELIVERY_INVALID_POSTAL_CODE_FOR_COUNTRY","INVALID_ZIP_CODE_FOR_PROVINCE: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.zip"],to=["DELIVERY_PHONE_NUMBER_REQUIRED","ADDRESS_FIELD_IS_REQUIRED: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.phone"],ro=["INVALID: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.country","Variable $buyerIdentity of type CartBuyerIdentityInput! was provided invalid value for countryCode"],no=["DELIVERY_PHONE_NUMBER_INVALID","INVALID: buyerIdentity.phone","ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN: buyerIdentity.deliveryAddressPreferences.0.deliveryAddress.phone"],sa=["INVALID_DELIVERY_OPTION: selectedDeliveryOptions","INVALID_DELIVERY_GROUP: selectedDeliveryOptions","DELIVERY_NO_DELIVERY_AVAILABLE","DELIVERY_DETAIL_CHANGED","NO_DELIVERY_GROUP_SELECTED","DELIVERY_NO_DELIVERY_STRATEGY_AVAILABLE","DELIVERY_LOCAL_PICKUP_NO_DELIVERY_STRATEGY_AVAILABLE"],la=["INVALID_PAYMENT: amount","INVALID_PAYMENT: payment.amount","INVALID_PAYMENT: payment"],Cr=["TAX_NEW_TAX_MUST_BE_ACCEPTED","PAYMENTS_UNACCEPTABLE_PAYMENT_AMOUNT"],ao=["DELIVERY_DELIVERY_LINE_DETAIL_CHANGED","DELIVERY_LOCAL_PICKUP_DELIVERY_LINE_DETAIL_CHANGED"],io=["INVALID_PAYMENT: payment.freePaymentMethod.billingAddress","INVALID_PAYMENT: payment.walletPaymentMethod.applePayWalletContent.billingAddress","ADDRESS_FIELD_IS_REQUIRED: payment.walletPaymentMethod.applePayWalletContent.billingAddress"],ca=["MERCHANDISE_NOT_APPLICABLE","MERCHANDISE_PRODUCT_VARIANT_NOT_FOUND","MERCHANDISE_NOT_FOUND","MERCHANDISE_PRODUCT_NOT_PUBLISHED"],da=["MERCHANDISE_NOT_ENOUGH_STOCK","DELIVERY_NO_DELIVERY_STRATEGY_AVAILABLE_FOR_MERCHANDISE_LINE","DELIVERY_OUT_OF_STOCK_AT_ORIGIN_LOCATION","DELIVERY_MUST_FULFILL_FROM_CONSTRAINT_NOT_SATISFIED","DELIVERY_MUST_FULFILL_FROM_SAME_LOCATION_CONSTRAINT_NOT_SATISFIED","DELIVERY_EXTERNAL_PROMISE_UNFULFILLABLE","MERCHANDISE_SELLING_PLANS_NOT_SUPPORTED_FOR_B2B","MERCHANDISE_BUNDLE_REQUIRES_COMPONENTS","MERCHANDISE_GIFT_CARDS_COMPONENTS_NOT_SUPPORTED","MERCHANDISE_GIFT_CARD_PRICE_MUST_BE_GREATER_THAN_ZERO","MERCHANDISE_GIFT_CARD_PRICE_MUST_NOT_EXCEED_LIMIT","INVALID: input.lines.0.quantity"],oo="mutation cartCreate($input:CartInput!$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false)@inContext(country:$country language:$language){result:cartCreate(input:$input){...@defer(if:$withCarrierRates){cart{...CartParts}errors:userErrors{...on CartUserError{message field code}}warnings:warnings{...on CartWarning{code}}}}}",so="mutation cartAttributesUpdate($cartId:ID!$attributes:[AttributeInput!]!$country:CountryCode=CA$language:LanguageCode=EN$withCarrierRates:Boolean=false$prepareCart:Boolean=false)@inContext(country:$country language:$language){result:cartAttributesUpdate(cartId:$cartId attributes:$attributes){...@defer(if:$withCarrierRates)@skip(if:$prepareCart){cart{...CartParts}errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}...@include(if:$prepareCart){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}...PreparedCartFragment}",lo="mutation cartBillingAddressUpdate($cartId:ID!$billingAddress:MailingAddressInput$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false$prepareCart:Boolean=false)@inContext(country:$country language:$language){result:cartBillingAddressUpdate(cartId:$cartId billingAddress:$billingAddress){...@defer(if:$withCarrierRates)@skip(if:$prepareCart){cart{...CartParts}errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}...@include(if:$prepareCart){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}...PreparedCartFragment}",co="mutation cartBuyerIdentityUpdate($cartId:ID!$buyerIdentity:CartBuyerIdentityInput!$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false$prepareCart:Boolean=false)@inContext(country:$country language:$language){result:cartBuyerIdentityUpdate(cartId:$cartId buyerIdentity:$buyerIdentity){...@defer(if:$withCarrierRates)@skip(if:$prepareCart){cart{...CartParts}errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}...@include(if:$prepareCart){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}...PreparedCartFragment}",uo="mutation cartSelectedDeliveryOptionsUpdate($cartId:ID!$selectedDeliveryOptions:[CartSelectedDeliveryOptionInput!]!$country:CountryCode=CA$language:LanguageCode=EN$withCarrierRates:Boolean=false$prepareCart:Boolean=false)@inContext(country:$country language:$language){result:cartSelectedDeliveryOptionsUpdate(cartId:$cartId selectedDeliveryOptions:$selectedDeliveryOptions){...@defer(if:$withCarrierRates)@skip(if:$prepareCart){cart{...CartParts}errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}...@include(if:$prepareCart){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}...PreparedCartFragment}",ho="mutation applePaySessionCreate($validationUrl:URL!){applePaySessionCreate(validationUrl:$validationUrl){...@defer{applePaySession{body}}}}",po="mutation PayPalBuyerDetailsFetch($token:String!$cartId:ID){paypalBuyerDetailsFetch(token:$token cartId:$cartId){...@defer{paypalDetails{email expiresAt remoteOrderId destinationAddress{...on MailingAddress{address1 address2 city company country firstName lastName phone province zip}}billingAddress{...on MailingAddress{address1 address2 city company country firstName lastName phone province zip}}}}}}",mo="mutation PayPalTokenCreate($cartId:ID!){paypalTokenCreate(cartId:$cartId){...@defer{paypalToken{token}}}}",yo="query cartQuery($id:ID!$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false)@inContext(country:$country language:$language){cart(id:$id){...@defer(if:$withCarrierRates){...CartParts}}}",go="query limitedCartQuery($id:ID!$country:CountryCode$language:LanguageCode)@inContext(country:$country language:$language){cart(id:$id){id lines(first:50){edges{node{merchandise{...on ProductVariant{requiresShipping}}sellingPlanAllocation{sellingPlan{id}}}}}}}",fo="query subscriptionPolicyQuery($country:CountryCode$language:LanguageCode)@inContext(country:$country language:$language){shop{subscriptionPolicy(sanitized:true){body}}}",bo="mutation cartPaymentUpdate($cartId:ID!$input:CartPaymentInput!$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false$prepareCart:Boolean=false)@inContext(country:$country language:$language){result:cartPaymentUpdate(cartId:$cartId payment:$input){...@defer(if:$withCarrierRates)@skip(if:$prepareCart){cart{...CartParts}errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}...@include(if:$prepareCart){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}...PreparedCartFragment}",bt="fragment CartParts on Cart{id checkoutUrl deliveryGroups(first:10 withCarrierRates:$withCarrierRates){edges{node{id groupType selectedDeliveryOption{code title handle deliveryPromise deliveryMethodType estimatedCost{amount currencyCode}}deliveryOptions{code title handle deliveryPromise deliveryMethodType estimatedCost{amount currencyCode}}}}}cost{subtotalAmount{amount currencyCode}totalAmount{amount currencyCode}totalTaxAmount{amount currencyCode}totalDutyAmount{amount currencyCode}}discountAllocations{discountedAmount{amount currencyCode}...on CartCodeDiscountAllocation{code}...on CartAutomaticDiscountAllocation{title}...on CartCustomDiscountAllocation{title}}discountCodes{code applicable}lines(first:10){edges{node{quantity cost{subtotalAmount{amount currencyCode}totalAmount{amount currencyCode}}discountAllocations{discountedAmount{amount currencyCode}...on CartCodeDiscountAllocation{code}...on CartAutomaticDiscountAllocation{title}...on CartCustomDiscountAllocation{title}}merchandise{...on ProductVariant{requiresShipping}}sellingPlanAllocation{priceAdjustments{price{amount currencyCode}}sellingPlan{billingPolicy{...on SellingPlanRecurringBillingPolicy{interval intervalCount}}priceAdjustments{orderCount}recurringDeliveries}}}}}}",jt="fragment CartErrorParts on CartUserError{message field code}",Jr="fragment PreparedCartFragment on Mutation{preparedCart:cartPrepareForCompletion(cartId:$cartId)@include(if:$prepareCart){...@defer{result{__typename ...on CartStatusReady{cart{...CartParts}}...on CartStatusNotReady{cart{...CartParts}errors{code message}}...on CartThrottled{cart{...CartParts}pollAfter}}errors:userErrors{...CartErrorParts}}}}",wo="mutation prepareCart($cartId:ID!$country:CountryCode$language:LanguageCode$withCarrierRates:Boolean=false$prepareCart:Boolean=true)@inContext(country:$country language:$language){...PreparedCartFragment}",_o="mutation cartSubmitForCompletion($cartId:ID!$attemptToken:String!$country:CountryCode$language:LanguageCode)@inContext(country:$country language:$language){cartSubmitForCompletion(cartId:$cartId attemptToken:$attemptToken){result{__typename ...on SubmitSuccess{redirectUrl}...on SubmitThrottled{pollAfterV2:pollAfter}...on SubmitFailed{errors{code message}checkoutUrl}}errors:userErrors{...on CartUserError{code field message}}}}",Eo="mutation cartRemovePersonalData($cartId:ID!){cartRemovePersonalData(cartId:$cartId){errors:userErrors{...CartErrorParts}warnings:warnings{...on CartWarning{code}}}}";async function Ao(t,e){var n,r;if(!e.errors)return;const a=Do(e.errors,t),i=await vo(e.errors.response),o=((r=(n=e.errors.response)==null?void 0:n.headers)==null?void 0:r.get("content-type"))||void 0,s=[];if(e.errors.graphQLErrors)for(const l of e.errors.graphQLErrors)l.code&&s.push(l.code);return{operationName:t,statusCode:e.errors.networkStatusCode,contentType:o,message:a,responseBody:i,errorCodes:s}}const Co=[/API multipart response did not return an iterable body/,/Error in parsing multipart response/,/GraphQL Client: Response returned unexpected Content-Type:/,/Internal error/,/Carrier-calculated rates through @defer is temporarily unavailable/,/Throttled/,/Request blocked/,/Network request failed/],So=[/signal is aborted/i,/fetch is aborted/i,/operation was aborted/i,/\[Apple Pay\] Payment sheet cancelled/],Io=[/timed?.out/i,/error occured while processing stream payload/i,/response stream terminated/i];function Po(t,{hasBeenVisible:e=!1}={}){if(t.statusCode&&t.statusCode!==200)return{isIgnorable:!0,reason:"statusCode-".concat(t.statusCode)};if(t.message){if(Io.some(n=>n.test(t.message)))return{isIgnorable:!0,reason:"timeout"};if(So.some(n=>n.test(t.message)))return{isIgnorable:!0,reason:"abort"};if(Co.some(n=>n.test(t.message)))return{isIgnorable:!0,reason:"message"}}return e?{isIgnorable:!1,reason:void 0}:{isIgnorable:!0,reason:"component-not-visible"}}async function vo(t){if(!t||t.bodyUsed)return"";try{return(await t.text()).slice(0,1024)}catch(e){return console.error("Failed to read response body:",e),""}}function Do(t,e){var n,r;if(!t)return"";const a=(r=(n=t.graphQLErrors)==null?void 0:n.map(s=>s.message).join(", "))!=null?r:"",i="".concat(t.message).concat(a.length>0?" - ".concat(a):""),o=t.networkStatusCode?"with status ".concat(t.networkStatusCode," => ").concat(i):"=> ".concat(i);return"GraphQL response error for ".concat(e,": ").concat(o)}function Ro(t){const{operationName:e,errorCodes:n}=t,r=n.join("-")||"unknown";return"".concat(e,"-").concat(r)}const Xr="UNKNOWN";function To({url:t,accessToken:e}){return{headers:{"Content-Type":"application/json",Accept:"application/json","X-Shopify-Storefront-Access-Token":e,"X-SDK-Variant":"portable-wallets"},url:t,retries:0,customFetchApi:(n,r)=>(b.leaveBreadcrumb("Request ".concat((r==null?void 0:r.method)||Xr,": ").concat(n),{url:n,method:r==null?void 0:r.method},"request"),fetch(n,r)),logger:n=>{try{if(n.type==="HTTP-Response"){const[r,a]=n.content.requestParams,{status:i,statusText:o,headers:s,type:l}=n.content.response;b.leaveBreadcrumb("Response ".concat((a==null?void 0:a.method)||Xr,": ").concat(r),{statusCode:i,statusText:o,responseHeaders:No(s),responseType:l,url:r,method:a==null?void 0:a.method,headers:a==null?void 0:a.headers,body:a==null?void 0:a.body},"log")}}catch(r){}}}}function No(t){if("fromEntries"in Object)return Object.fromEntries(t);const e={};for(const[n,r]of t.entries())e[n]=r;return e}var f=(t=>(t.BuyItNow="BuyItNow",t.MoreOptions="MoreOptions",t.ApplePay="ApplePay",t.PayPal="PayPal",t.Venmo="Venmo",t.GooglePay="GooglePay",t.ShopPay="ShopPay",t.MetaPay="FacebookPay",t.BuyWithPrime="BuyWithPrime",t.AmazonPay="AmazonPay",t.Unknown="Unknown",t))(f||{}),q=(t=>(t.InvalidQuantity="InvalidQuantity",t.VariantRequiresSellingPlan="VariantRequiresSellingPlan",t.SellingPlanNotApplicable="SellingPlanNotApplicable",t.MerchandiseIdInvalid="MerchandiseIdInvalid",t))(q||{}),P=(t=>(t.CartPage="cart_page",t.CartAjax="cart_ajax",t.ProductPage="product",t.Checkout="checkout",t.Unknown="unknown",t))(P||{}),rt=(t=>(t.CartChange="/cart/change",t.CartUpdate="/cart/update",t.CartAdd="/cart/add",t.CartClear="/cart/clear",t))(rt||{}),De=(t=>(t.OneTimePurchase="ONE_TIME_PURCHASE",t.Subscription="SUBSCRIPTION",t))(De||{});function Zr(t){const e=document.querySelectorAll("shopify-accelerated-checkout .shopify-payment-button__skeleton, shopify-accelerated-checkout-cart .wallet-cart-button__skeleton"),n=document.getElementById("shopify-buyer-consent");for(let r=0;r<e.length;r++)e[r].remove();n&&n.remove()}function ua(t){!(t instanceof ErrorEvent)||!(typeof t.message=="string"&&t.message.includes("import.meta"))||!(typeof t.filename=="string"&&t.filename.includes("portable-wallets"))||(window.removeEventListener("error",ua),window.Shopify.PaymentButton.failedToLoad=t,document.readyState==="loading"?document.addEventListener("DOMContentLoaded",window.Shopify.PaymentButton.init):window.Shopify.PaymentButton.init())}window.addEventListener("error",ua);function ko(t,e,n){return(e=Oo(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function en(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),n.push.apply(n,r)}return n}function tn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?en(Object(n),!0).forEach(function(r){ko(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):en(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Lo(t,e){if(typeof t!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var r=n.call(t,e);if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Oo(t){var e=Lo(t,"string");return typeof e=="symbol"?e:e+""}const xo="https://monorail-edge.shopifysvc.com",Mo="/v1/produce";function Uo(){if(typeof crypto<"u"&&crypto&&typeof crypto.randomUUID=="function")return crypto.randomUUID();const t=new Array(36);for(let e=0;e<36;e++)t[e]=Math.floor(Math.random()*16);return t[14]=4,t[19]=t[19]&=-5,t[19]=t[19]|=8,t[8]=t[13]=t[18]=t[23]="-",t.map(e=>e.toString(16)).join("")}function Bo(t,e=!0){return t&&Object.keys(t).length&&e?Object.keys(t).map(n=>({[Fo(n)]:t[n]})).reduce((n,r)=>tn(tn({},n),r)):t}function Fo(t){return t.split(/(?=[A-Z])/).join("_").toLowerCase()}function $o(t){const e={"Content-Type":"application/json; charset=utf-8","X-Monorail-Edge-Event-Created-At-Ms":(t&&t.eventCreatedAtMs||Date.now()).toString(),"X-Monorail-Edge-Event-Sent-At-Ms":Date.now().toString(),"X-Monorail-Edge-Client-Message-Id":(t&&t.clientMessageId||Uo()).toString()};return t&&t.userAgent&&(e["User-Agent"]=t.userAgent),t&&t.remoteIp&&(e["X-Forwarded-For"]=t.remoteIp),t&&t.deviceInstallId&&(e["X-Monorail-Edge-Device-Install-Id"]=t.deviceInstallId),t&&t.client&&(e["X-Monorail-Edge-Client"]=t.client),t&&t.clientOs&&(e["X-Monorail-Edge-Client-OS"]=t.clientOs),e}async function zo({endpoint:t,event:e,keepalive:n}){var r,a,i,o,s;const l=e.metadata?{clientMessageId:(r=e.metadata)===null||r===void 0?void 0:r.clientMessageId,eventCreatedAtMs:(a=e.metadata)===null||a===void 0?void 0:a.eventCreatedAtMs,consent:(i=e.metadata)===null||i===void 0?void 0:i.consent,consent_provider:(o=e.metadata)===null||o===void 0?void 0:o.consent_provider,consent_version:(s=e.metadata)===null||s===void 0?void 0:s.consent_version}:void 0;return fetch(t!=null?t:xo+Mo,{method:"post",headers:$o(e.metadata),body:JSON.stringify({schema_id:e.schemaId,payload:e.payload,metadata:l&&Bo(l,!0)}),keepalive:n})}const Ho="2.1",Go="3",Yt={NO_VALUE:"",ACCEPTED:"1",DECLINED:"0"},j={PREFERENCES:"p",ANALYTICS:"a",MARKETING:"m",SALE_OF_DATA:"t"},le={MARKETING:"m",ANALYTICS:"a",PREFERENCES:"p",SALE_OF_DATA:"s"},Vo=()=>typeof __CtaTestEnv__>"u"?!1:__CtaTestEnv__==="true",qo="_tracking_consent";function rn(t){try{return decodeURIComponent(t)}catch(e){return""}}function ha(t,e=!1){const n=document.cookie?document.cookie.split("; "):[];for(let r=0;r<n.length;r++){const[a,i]=n[r].split("=");if(t===rn(a))return rn(i)}if(e&&t==="_tracking_consent"&&!window.localStorage.getItem("tracking_consent_fetched"))return Vo()?void 0:(console.debug("_tracking_consent missing"),Wo(),window.localStorage.setItem("tracking_consent_fetched","true"),ha(t,!1))}function Wo(t="/"){const e=new XMLHttpRequest;e.open("HEAD",t,!1),e.withCredentials=!0,e.send()}function jo(){return ha(qo)}const Yo="_cs";function Ko(){return new URLSearchParams(window.location.search).get(Yo)}function Qo(){const t=Ko()||jo();if(t!==void 0)return Jo(t)}function Jo(t){const e=t.slice(0,1);if(e=="{")return Xo(t);if(e=="3")return Zo(t)}function Xo(t){var e;let n;try{n=JSON.parse(t)}catch(r){return}if(n.v===Ho&&(e=n.con)!==null&&e!==void 0&&e.CMP)return n}function Zo(t){const e=t.slice(1).split("_"),[n,r,a,i,o]=e;let s;try{s=e[5]?JSON.parse(e.slice(5).join("_")):void 0}catch(h){}let l;if(o){const h=o.replace(/\*/g,"/").replace(/-/g,"+"),p=Array.from(atob(h)).map(m=>m.charCodeAt(0).toString(16).padStart(2,"0")).join("");l=[8,13,18,23].reduce((m,_)=>m.slice(0,_)+"-"+m.slice(_),p)}function u(h){const p=n.split(".")[0];return p.includes(h.toLowerCase())?Yt.DECLINED:p.includes(h.toUpperCase())?Yt.ACCEPTED:Yt.NO_VALUE}function d(h){return n.includes(h.replace("t","s").toUpperCase())}return{v:Go,con:{CMP:{[le.ANALYTICS]:u(le.ANALYTICS),[le.PREFERENCES]:u(le.PREFERENCES),[le.MARKETING]:u(le.MARKETING),[le.SALE_OF_DATA]:u(le.SALE_OF_DATA)}},region:r||"",cus:s,purposes:{[j.ANALYTICS]:d(j.ANALYTICS),[j.PREFERENCES]:d(j.PREFERENCES),[j.MARKETING]:d(j.MARKETING),[j.SALE_OF_DATA]:d(j.SALE_OF_DATA)},sale_of_data_region:i=="t",display_banner:a=="t",consent_id:l}}function Mt(t){const e=Qo();if(!e||!e.purposes)return!0;const n=e.purposes[t];return typeof n=="boolean"?n:!0}function es(){return Mt(j.PREFERENCES)}function ts(){return Mt(j.ANALYTICS)}function rs(){return Mt(j.MARKETING)}function ns(){return Mt(j.SALE_OF_DATA)}function as(){return ts()}function is(){return es()}function os(){return rs()}function ss(){return ns()}function ls(){return os()}function cs(){return as()}function ds(){return is()}function us(){return ss()}function hs(){const t=[];return cs()&&t.push("analytics"),ls()&&t.push("marketing"),us()&&t.push("sale_of_data"),ds()&&t.push("preferences"),t}function ps(t,e){switch(e){case"v1":{const n=hs();return{...t,metadata:{...t==null?void 0:t.metadata,consent:n,consent_provider:"consent-tracking-api",consent_version:e}}}default:throw new Sr(e)}}class Sr extends Error{constructor(e){super("Version ".concat(e," is not supported by the consent-tracking-api provider")),this.name="MonorailConsentTrackingApiProviderVersionError",Object.setPrototypeOf(this,Sr.prototype)}}var I=(t=>(t.ButtonRender="portable_wallets_button_render",t.Decelerated="portable_wallets_decelerated",t.InitCompleted="portable_wallets_init_completed",t.InstrumentLoadEligibility="portable_wallets_instrument_load_eligibility",t.InstrumentLoadTime="portable_wallets_instrument_load_time",t.InstrumentLoadTimeFromPageLoad="portable_wallets_instrument_load_time_from_page_load",t.MonorailProduceError="portable_wallets_monorail_produce_error",t.SheetClicked="portable_wallets_sheet_clicked",t.SheetCancelled="portable_wallets_sheet_cancelled",t.SheetFailed="portable_wallets_sheet_failed",t.UpdateFailed="portable_wallets_update_failed",t.PayPalTokenCreationError="portable_wallets_paypal_token_creation_error",t.AuthorizationAttempt="portable_wallets_authorization_attempt",t.AuthorizationComplete="portable_wallets_authorization_complete",t.CartTokenMissing="portable_wallets_cart_token_missing",t.RemovePersonalDataResult="portable_wallets_cart_remove_personal_data_result",t.GooglePayNotEligibleWebview="portable_wallets_google_pay_not_eligible_webview",t.WalletConfigDeveloperError="portable_wallets_wallet_config_developer_error",t.LegacyCartCookie="portable_wallets_legacy_cart_cookie",t.StyleBackwardsCompatibility="portable_wallets_style_backwards_compatibility",t.StyleBackwardsCompatibilityExecutionTime="portable_wallets_style_backwards_compatibility_execution_time",t.StyleBackwardsCompatibilityRules="portable_wallets_style_backwards_compatibility_rules",t.AmazonPayLoadRetries="portable_wallets_amazon_pay_sdk_load_retries",t.DynamicTaxFailed="portable_wallets_dynamic_tax_failed",t.DynamicShippingFailed="portable_wallets_dynamic_shipping_failed",t.SheetLoaded="portable_wallets_sheet_loaded",t.ErrorTriggered="portable_wallets_error_triggered",t.AmazonAdExperimentAcceleratedCheckoutButtonClicked="portable_wallets_amazon_ad_experiment_accelerated_checkout_button_clicked",t.AmazonAdExperimentAcceleratedCheckoutButtonSetup="portable_wallets_amazon_ad_experiment_accelerated_checkout_button_setup",t.AmazonAdExperimentPaymentButtonHidden="portable_wallets_amazon_ad_experiment_payment_button_hidden",t.AmazonAdExperimentRecommendedWalletOverriden="portable_wallets_amazon_ad_experiment_recommended_wallet_overriden",t.UnrecoverableCartError="portable_wallets_unrecoverable_cart_error",t.ShopPayPersonalizedIframeLoadTime="portable_wallets_shop_pay_personalized_iframe_load_time",t.ShopPayPersonalizedIframeLoadTimeFromPageLoad="portable_wallets_shop_pay_personalized_iframe_load_time_from_page_load",t))(I||{}),Pe=(t=>(t.InstrumentSdkLoaded="portable_wallets_instrument_sdk_loaded",t.InstrumentSdkFailed="portable_wallets_instrument_sdk_failed",t.InstrumentSdkEligible="portable_wallets_instrument_sdk_eligible",t.InstrumentSdkNotEligible="portable_wallets_instrument_sdk_not_eligible",t))(Pe||{});const ms=["Chrome-Lighthouse","Googlebot"];function ys(){const t=navigator.userAgent;return ms.some(e=>t.includes(e))}async function gs(t){if(ys())return;const e=ps(t,"v1");let n;try{n=await zo({event:e})}catch(r){}if(!(n!=null&&n.ok))try{C.counter({name:I.MonorailProduceError,value:1,attributes:{schemaId:e.schemaId}})}catch(r){}}const Kt="shopify_wallet_checkout_track/6.2";async function H(t){var e,n,r,a,i,o,s;const l=await fs();if(!l)return;const u={schemaId:Kt,payload:{app_name:"storefront",page_type:al(),checkout_one:!0,event:t.event,event_subtype:t.eventSubtype,checkout_token:t.checkoutToken,instrument_id:t.instrumentId,ttl:t.ttl,error_reason:t.errorReason,uniq_token:(e=l==null?void 0:l.uniqToken)!=null?e:"",visit_token:(n=l==null?void 0:l.visitToken)!=null?n:"",micro_session_id:(r=l==null?void 0:l.microSessionId)!=null?r:"",micro_session_count:(a=l==null?void 0:l.microSessionCount)!=null?a:0,shop_id:(i=l==null?void 0:l.shopId)!=null?i:0,theme_id:l==null?void 0:l.themeId,theme_city_hash:(o=l==null?void 0:l.themeCityHash)!=null?o:"",content_language:(s=l==null?void 0:l.contentLanguage)!=null?s:"",referer:l==null?void 0:l.referer}};b.leaveBreadcrumb("monorail event produced to ".concat(Kt),{schemaId:Kt,payload:t});try{await gs(u)}catch(d){b.notify(new bs("Failed to send monorail event: ".concat(d),{cause:d}),{metadata:{request:{monorail:{payload:JSON.stringify(u)}}}})}}async function fs(){var t,e,n,r,a;return(e=(t=window==null?void 0:window.ShopifyAnalytics)==null?void 0:t.lib)!=null&&e.trekkie||await new Promise(i=>{const o=setInterval(()=>{var l,u;(u=(l=window==null?void 0:window.ShopifyAnalytics)==null?void 0:l.lib)!=null&&u.trekkie&&(clearInterval(o),i())},100),s=setTimeout(()=>{clearInterval(o),clearTimeout(s),i()},7e3)}),(a=(r=(n=window.ShopifyAnalytics)==null?void 0:n.lib)==null?void 0:r.trekkie)==null?void 0:a.defaultAttributes}class bs extends Error{constructor(){super(...arguments),c(this,"name","MonorailError")}}var U=(t=>(t.CartInitCalled="portable_wallets_cart_init_called",t.ClickSheetCancelled="portable_wallets_instrument_click_sheet_cancelled",t.ClickSheetFailed="portable_wallets_instrument_click_sheet_failure",t.ClickSheetSuccess="portable_wallets_instrument_click_sheet_success",t.SheetFailed="portable_wallets_instrument_sheet_failed",t.AuthorizationAttempt="portable_wallets_authorization_attempt",t.AuthorizationComplete="portable_wallets_authorization_complete",t.InitCalled="portable_wallets_init_called",t.InitFailed="portable_wallets_init_failed",t.InitSuccess="portable_wallets_init_success",t.UpdateFailed="portable_wallets_instrument_update_failed",t.InstrumentLoaded="portable_wallets_instrument_loaded",t.InstrumentLoadFailed="portable_wallets_instrument_load_failed",t.FirstVisible="portable_wallets_first_visible",t.FirstVisibleNotAvailable="portable_wallets_first_visible_not_available",t.SheetLoaded="portable_wallets_sheet_loaded",t.ShopPayButtonInView="portable_wallets_shop_pay_button_in_view",t))(U||{});const nn={[P.Checkout]:"bwp_checkout_widget_click",[P.CartAjax]:"bwp_cart_widget_click",[P.CartPage]:"bwp_cart_widget_click",[P.ProductPage]:"bwp_widget_click",[P.Unknown]:"bwp_widget_click"};function ws({apiClientId:t,skus:e,pageType:n}){var r;if(!((r=window.Shopify)!=null&&r.analytics))return;const a=n&&nn[n]||"bwp_widget_click";a===nn[P.Checkout]&&window.Shopify.analytics.publish("shopify:app:pixels:load:".concat(t),{},{sendTo:"PIXEL-LOADER"}),e&&window.Shopify.analytics.publish(a,{skus:e},{sendTo:t})}let Ut=_s();function _s(){return{shopId:$n,debug:!1,pageType:P.Unknown}}function Es(t){Object.assign(Ut,t)}function Ir(){const{debug:t,...e}=Ut;return e}function As(){return Ut.debug}function At(){return Ut.pageType}function T(t){return{pageType:At(),...t}}function Cs(){const t=At()===P.CartPage?U.CartInitCalled:U.InitCalled;H({event:t})}function Ss(t){var e,n;const r=t?"Failed":"Success",a=t&&Vs(t)?"[".concat(t.name,"]: ").concat(t.code):void 0,i=U["Init".concat(r)];H({event:i,eventSubtype:a}),C.counter({name:I.InitCompleted,value:1,attributes:T({result:r.toLowerCase(),eventType:a,recoveredFailure:!!((n=(e=window.Shopify)==null?void 0:e.PaymentButton)!=null&&n.failedToLoad)})})}function Is({instrumentOrComponentName:t,result:e,measurement:n}){H({event:e==="success"?U.InstrumentLoaded:U.InstrumentLoadFailed,instrumentId:t,ttl:n}),n!=null&&(C.histogram({name:I.InstrumentLoadTime,value:n,attributes:T({instrument:t}),unit:"ms"}),C.histogram({name:I.InstrumentLoadTimeFromPageLoad,value:window.performance.now(),attributes:T({instrument:t}),unit:"ms"}))}function Ps({instrument:t,measurement:e,result:n}){const r=n==="success"?Pe.InstrumentSdkLoaded:Pe.InstrumentSdkFailed;H({event:r,instrumentId:t,ttl:e,errorReason:n==="success"?void 0:n}),e!=null&&C.histogram({name:Pe.InstrumentSdkLoaded,value:e,attributes:T({instrument:t,result:n}),unit:"ms"})}function vs({instrument:t,result:e,reason:n}){const r=e==="success"?Pe.InstrumentSdkEligible:Pe.InstrumentSdkNotEligible;H({event:r,instrumentId:t}),C.counter({name:Pe.InstrumentSdkEligible,value:1,attributes:T({instrument:t,reason:n,result:e})})}function Ds({instrument:t,result:e,reason:n}){C.counter({name:I.InstrumentLoadEligibility,value:1,attributes:T({instrument:t,result:e,reason:n})})}function Rs(t){C.counter({name:I.ButtonRender,value:1,attributes:T({result:"success",instrument:t})})}function Ts(t){C.counter({name:I.ButtonRender,value:1,attributes:T({result:"failed",instrument:t})})}function Ns({instrument:t,result:e,webPixelMetaData:n}){H({event:e==="success"?U.ClickSheetSuccess:U.ClickSheetFailed,instrumentId:t}),C.counter({name:I.SheetClicked,value:1,attributes:T({instrument:t,result:e})}),t===f.BuyWithPrime&&n&&ws(n)}function ks(t,e){H({event:"".concat(U.UpdateFailed,"-").concat(e),instrumentId:t}),C.counter({name:I.UpdateFailed,value:1,attributes:T({instrument:t,request:e})})}function Ls({errorCode:t}){C.counter({name:I.PayPalTokenCreationError,value:1,attributes:T({errorCode:t})})}function Os(t){H({event:U.ClickSheetCancelled,instrumentId:t}),C.counter({name:I.SheetCancelled,value:1,attributes:T({instrument:t})})}function xs(t,e){H({event:U.SheetFailed,instrumentId:t,errorReason:e==null?void 0:e.message}),C.counter({name:I.SheetFailed,value:1,attributes:T({instrument:t})})}function Ms(t){H({event:U.AuthorizationAttempt,instrumentId:t}),C.counter({name:I.AuthorizationAttempt,value:1,attributes:T({instrument:t})})}function Us({instrument:t,measurement:e,result:n}){H({event:U.AuthorizationComplete,eventSubtype:n,ttl:e,instrumentId:t}),e!=null&&C.histogram({name:I.AuthorizationComplete,value:e,attributes:T({instrument:t,result:n}),unit:"ms"})}function Bs({instrument:t,reason:e}){C.counter({name:I.Decelerated,value:1,attributes:T({instrument:t,reason:e})}),y.flushTelemetry()}function Fs({reason:t}){C.counter({name:I.CartTokenMissing,value:1,attributes:{reason:t}})}function $s({success:t}){C.counter({name:I.RemovePersonalDataResult,value:1,attributes:T({success:t})})}function zs(){C.counter({name:I.WalletConfigDeveloperError,value:1})}function Hs(){C.counter({name:I.LegacyCartCookie,value:1})}function Gs(){window.setInterval(()=>{pa()},1e3)}function Vs(t){return(t==null?void 0:t.name)===mt}function pa(){C.exportMetrics(),C.exportLogs()}function qs(t){const{body:e,attributes:n}=t;C.log({body:e,attributes:{...Ir(),...n}})}function Ws({usedBackwardsCompatibility:t}){C.counter({name:I.StyleBackwardsCompatibility,value:1,attributes:{usedBackwardsCompatibility:t}})}function js({measurement:t,pageType:e,stylesWritten:n}){C.histogram({name:I.StyleBackwardsCompatibilityExecutionTime,value:t,attributes:T({pageType:e,stylesWritten:n}),unit:"ms"})}function Ys({rule:t,count:e,pageType:n}){C.counter({name:I.StyleBackwardsCompatibilityRules,value:e,attributes:T({rule:t,pageType:n})})}function Ks({retries:t}){C.counter({name:I.AmazonPayLoadRetries,value:1,attributes:{retries:t}})}function Qs({instrument:t}){C.counter({name:I.DynamicTaxFailed,value:1,attributes:{instrument:t}}),y.log({body:"Dynamic tax failed",attributes:{instrument:t}})}function Js({instrument:t}){C.counter({name:I.DynamicShippingFailed,value:1,attributes:{instrument:t}}),y.log({body:"Dynamic shipping failed",attributes:{instrument:t}})}function Xs({instrument:t,measurement:e}){C.histogram({name:I.SheetLoaded,value:e,attributes:T({instrument:t}),unit:"ms"})}function Zs({instrument:t,result:e}){C.counter({name:I.AmazonAdExperimentAcceleratedCheckoutButtonClicked,value:1,attributes:T({instrument:t,result:e})})}function el({instrument:t,result:e}){C.counter({name:I.AmazonAdExperimentAcceleratedCheckoutButtonSetup,value:1,attributes:T({instrument:t,result:e})})}function tl({instrument:t}){C.counter({name:I.AmazonAdExperimentPaymentButtonHidden,value:1,attributes:{instrument:t}}),y.log({body:"Payment button hidden",attributes:{instrument:t}})}function rl({instrument:t}){C.counter({name:I.AmazonAdExperimentRecommendedWalletOverriden,value:1,attributes:{instrument:t}}),y.log({body:"Recommended wallet overriden",attributes:{instrument:t}})}function nl({instrument:t,errorName:e}){C.counter({name:I.UnrecoverableCartError,value:1,attributes:T({instrument:t,errorName:e})}),y.log({body:"Unrecoverable error detected",attributes:{instrument:t,errorName:e}})}const y={initStarted:v(Cs),initCompleted:v(Ss),instrumentLoaded:v(Is),instrumentLoadEligibility:v(Ds),instrumentSDKLoaded:v(Ps),instrumentSDKEligible:v(vs),renderSucceeded:v(Rs),renderFailed:v(Ts),sheetClicked:v(Ns),sheetCancelled:v(Os),sheetFailed:v(xs),updateFailed:v(ks),authorizationAttempt:v(Ms),authorizationComplete:v(Us),startExporter:v(Gs),decelerated:v(Bs),cartTokenMissing:v(Fs),removePersonalDataResult:v($s),walletConfigDeveloperError:v(zs),legacyCartCookie:v(Hs),flushTelemetry:v(pa),styleBackwardsCompatibility:v(Ws),styleBackwardsCompatibilityExecutionTime:v(js),styleBackwardsCompatibilityRules:v(Ys),amazonPaySDKLoadRetries:v(Ks),dynamicTaxFailed:v(Qs),dynamicShippingFailed:v(Js),sheetLoaded:v(Xs),amazonAdExperimentAcceleratedCheckoutButtonClicked:v(Zs),amazonAdExperimentAcceleratedCheckoutButtonSetup:v(el),amazonAdExperimentPaymentButtonHidden:v(tl),amazonAdExperimentRecommendedWalletOverriden:v(rl),unrecoverableCartError:v(nl),log:v(qs,{logErrors:!1}),paypalTokenCreationError:v(Ls)};function v(t,{logErrors:e=!0}={}){return(...n)=>{try{t(...n)}catch(r){e&&y.log({body:"Error calling telemetry function",attributes:{error:"".concat(r)}})}}}const nt=[];function ae(t,e,n){if(window.customElements==null)return b.leaveBreadcrumb("customElements is null on window"),Zr(),!1;try{const r=e;return window.customElements.get(t)||(window.customElements.define(t,r),n!=null&&n.isChildCustomElement&&nt.push(t)),!0}catch(r){return b.notify(r),Zr(),!1}}function Ct(){const t=new URLSearchParams(window.location.search);return function(e){return!!t.get(e)}}function al(){var t,e,n,r;const a=(t=window.Shopify)==null?void 0:t.CartType;if(a===P.CartAjax)return P.CartAjax;if(a===P.CartPage)return P.CartPage;const i=(r=(n=(e=window.ShopifyAnalytics)==null?void 0:e.meta)==null?void 0:n.page)==null?void 0:r.pageType;if(i)switch(i.toLowerCase()){case"product":return P.ProductPage;case"cart":return P.CartPage}const o=document.querySelector("#shop-js-analytics");if(o)try{const s=JSON.parse(o.textContent||"{}");if(s.pageType)switch(s.pageType.toLowerCase()){case"product":return P.ProductPage;case"cart":return P.CartPage}}catch(s){console.error("Error parsing JSON script tag:",s)}return P.Unknown}function ve(t){return Number(t).toFixed(2)}function Pr(t){if(!(!t||!t.id||!t.id.includes("gid://shopify/Cart/")))return t.id.replace("gid://shopify/Cart/","").split("?")[0]}function ce(){let t;return[new Promise(e=>t=e),t]}const il={force_shop_pay_product:"direct_checkout_product",force_shop_pay_cart:"direct_checkout_cart"};function pe(t,e="no_redirect_for_checkout"){y.flushTelemetry();const n=new URL(t);switch(e){case"skip_shop_pay":n.searchParams.set("skip_shop_pay","true");break;case"no_redirect_for_checkout":n.searchParams.set("storefront_wallet","true");break;case"force_shop_pay_cart":case"force_shop_pay_product":case"force_shop_pay":{n.searchParams.set("payment","shop_pay");const r=il[e];r&&n.searchParams.set("redirect_source",r);break}}window.location.assign(n.toString())}function ma(){return!!document.querySelector('[data-source-attribution="shopify.shop-promise-pdp"]')}function ne(){return new Date().getTime()}function vr(t){return t.find(e=>typeof e=="object"&&(e==null?void 0:e.code)===Et)}const ol={currency_changed:we.CURRENCY_CHANGE,captcha_required:we.CAPTCHA_REQUIRED,not_enough_stock:we.NOT_ENOUGH_STOCK,cart_not_ready:we.CART_NOT_READY,dynamic_tax:we.DYNAMIC_TAX,payment_method_not_applicable:we.PAYMENT_METHOD_NOT_APPLICABLE,invalid_payment_deferred_payment_required:we.INVALID_PAYMENT_DEFERRED_PAYMENT_REQUIRED,cart_throttled:null};function an({cart:t,warnings:e}){const n=t&&t.lineItems.length>0&&t.lineItems.every(r=>r.quantity===0);if(e!=null&&e.includes("MERCHANDISE_OUT_OF_STOCK")&&n)return"out_of_stock"}function Qt({warnings:t,cart:e,cartStatus:n,errors:r}){if(r!=null&&r.includes(g.PaymentMethodNotApplicable))return"payment_method_not_applicable";if(r!=null&&r.includes(g.InvalidPaymentDeferredPaymentRequired))return"invalid_payment_deferred_payment_required";if(n==="CartStatusNotReady")return"cart_not_ready";if(n==="CartThrottled")return"cart_throttled";if(!t||t.length===0)return;if(t.includes("MERCHANDISE_NOT_ENOUGH_STOCK"))return"not_enough_stock";const a=e==null?void 0:e.lineItems.some(i=>i.quantity>0);if(t.includes("MERCHANDISE_OUT_OF_STOCK")&&a)return"not_enough_stock"}function Dr({currentCartTotal:t,initialBuyerCurrency:e}){const n=t.amount;return!(!n||Number(n)===0||e.toUpperCase()===t.currencyCode.toUpperCase())}function M({checkoutUrl:t,instrumentName:e,reason:n}){if(!t)throw new Error("Invalid Checkout URL");const r=new URL(t),a=ol[n];a&&r.searchParams.set(a,"true"),y.decelerated({instrument:e,reason:n}),pe(r.toString())}function ya(t){return window[ga(t)]}function ga(t){return"paypal-".concat(t.replace(/[^a-zA-Z0-9]/g,""))}const sl="RESTRICTED_WALLET_ACCOUNT",ll="GENERIC_ERROR",cl="NO_PENDING_TASK",dl="WALLET_SERVICE_DISABLED",ul="FAILED_TO_ENQUEUE_BACKGROUND_JOB",hl="REQUEST_MISSING_VALID_MERCHANDISE_SOURCE",pl="THROTTLE_EXCEEDED",ml="INTERNAL_SERVER_ERROR",yl=[ll,cl,dl,ul,hl,pl,ml];function on(t){return t==null?void 0:t.map(e=>{var n,r;return{code:(n=e.extensions)==null?void 0:n.code,isLoggable:yl.includes((r=e.extensions)==null?void 0:r.code)}}).find(({code:e,isLoggable:n})=>e===sl||n)}const gl="unstable";class Bt{constructor({accessToken:e,country:n,locale:r,apiVersion:a=gl,withCarrierRates:i=!1,cartPrepareMigrationEnabled:o=!1}){if(c(this,"OPERATION_NAME_REGEX",/[^ ]+ (\w+)/),c(this,"LANGUAGES_WITH_REGION",["zh-CN","zh-TW","pt-BR","pt-PT"]),c(this,"client"),c(this,"country"),c(this,"url"),c(this,"locale"),c(this,"withCarrierRates"),c(this,"lastValidCart"),c(this,"cartPrepareMigrationEnabled"),c(this,"hasBeenVisible",!1),!e||!n)throw new J({code:"invalid-webcomponent",message:"accelerated checkout rendered without required webcomponent attributes"});if(!r)throw new Error("StorefrontApiClient constructed with no locale");this.country=n,this.locale=r,this.withCarrierRates=i,this.cartPrepareMigrationEnabled=o,this.url="/api/".concat(a,"/graphql.json"),this.client=Mi(To({url:this.url,accessToken:e}))}async createCart(e){var n,r,a;const i=await this.requestStream([oo,bt],{input:{lines:[{merchandiseId:e.merchandiseId,quantity:e.quantity,sellingPlanId:e.sellingPlanId,attributes:this.stringifyAttributes(e.lineItemProperties)}],discountCodes:e.discountCodes,buyerIdentity:e.buyerIdentity},country:this.country,language:this.formatLanguage(this.locale)},{instrumentName:e.instrumentName,startingCheckout:!0,operationName:"createCart"});try{for(var o=Z(i),s,l,u;s=!(l=await o.next()).done;s=!1){const d=l.value;d.errors&&await this.onError({operationName:"createCart",response:d});const h=(n=d.data)==null?void 0:n.result;if(h&&!d.hasNext){h.errors&&this.onResultError(h.errors);const p=this.parseMutationWarnings((a=(r=d.data)==null?void 0:r.result)==null?void 0:a.warnings),m=h.cart?this.parseCartResponse(h.cart):null;return{data:m,errors:this.parseMutationErrors(d,"result"),warnings:p,decelerationReason:Qt({cart:m,warnings:p}),abortReason:an({cart:m,warnings:p})}}}}catch(d){u=[d]}finally{try{s&&(l=o.return)&&await l.call(o)}finally{if(u)throw u[0]}}throw new _e("Cart create operation returned no response")}async updateCartBillingAddress(e){const{cartId:n,billingAddress:r,instrumentName:a}=e;return this.requestDeferredCart(lo,{cartId:n,billingAddress:r},{instrumentName:a,operationName:"updateCartBillingAddress"})}async updateCartBuyerIdentity(e,n){const{cartId:r,instrumentName:a}=e;return this.requestDeferredCart(co,{cartId:r,buyerIdentity:bl(e)},{instrumentName:a,operationName:"updateCartBuyerIdentity",abortSignal:n})}async updateSelectedDeliveryOptions(e,n,r,a){let i=n;const o=ne()+2e4;for(;ne()<o;){const s=await this.requestDeferredCart(uo,{cartId:e,selectedDeliveryOptions:i},{instrumentName:r,operationName:"updateSelectedDeliveryOptions",abortSignal:a});if(!(!this.cartPrepareMigrationEnabled&&s.errors.some(u=>typeof u=="string"&&u.startsWith(g.PendingDeliveryGroups))))return s;const l=s.data.deliveryGroups.flatMap(u=>u.deliveryOptions.map(d=>({deliveryOptionHandle:d.handle,deliveryGroupId:u.id})));i=i.map(u=>{var d;return(d=l.find(h=>h.deliveryOptionHandle===u.deliveryOptionHandle))!=null?d:u})}throw new Error("Selected delivery options update operation timed out")}async applePaySessionCreate(e){var n,r,a;const i=await this.requestStream([ho],{validationUrl:e.toString()},{instrumentName:f.ApplePay,operationName:"applePaySessionCreate"});try{for(var o=Z(i),s,l,u;s=!(l=await o.next()).done;s=!1){const d=l.value;d.errors&&await this.onError({operationName:"applePaySessionCreate",response:d});const h=(a=(r=(n=d.data)==null?void 0:n.applePaySessionCreate)==null?void 0:r.applePaySession)==null?void 0:a.body;if(h)return h}}catch(d){u=[d]}finally{try{s&&(l=o.return)&&await l.call(o)}finally{if(u)throw u[0]}}throw new _e("Apple Pay session create operation returned no response")}async paypalBuyerDetailsFetch(e,n){var r,a;const i=await this.requestStream([po],{token:e,cartId:n},{instrumentName:f.PayPal,operationName:"paypalBuyerDetailsFetch"});try{for(var o=Z(i),s,l,u;s=!(l=await o.next()).done;s=!1){const d=l.value;if(d.hasNext)continue;if(d.errors){if(d.errors.graphQLErrors){const p=on(d.errors.graphQLErrors);if(p!=null&&p.code)throw new re(p.code)}await this.onError({operationName:"paypalBuyerDetailsFetch",response:d})}const h=(a=(r=d.data)==null?void 0:r.paypalBuyerDetailsFetch)==null?void 0:a.paypalDetails;if(h)return h}}catch(d){u=[d]}finally{try{s&&(l=o.return)&&await l.call(o)}finally{if(u)throw u[0]}}throw new _e("PayPal buyer details fetch operation returned no response")}async paypalTokenCreate(e){var n,r,a;const i=await this.requestStream([mo],{cartId:e},{instrumentName:f.PayPal,operationName:"paypalTokenCreate"});try{for(var o=Z(i),s,l,u;s=!(l=await o.next()).done;s=!1){const d=l.value;if(d.errors){if(d.errors.graphQLErrors){const p=on(d.errors.graphQLErrors);if(p!=null&&p.code)throw p.isLoggable&&(y.log({body:"PayPal Token Create ".concat(p.code," Error")}),y.paypalTokenCreationError({errorCode:p.code})),new re(p.code)}await this.onError({operationName:"paypalTokenCreate",response:d})}const h=(a=(r=(n=d.data)==null?void 0:n.paypalTokenCreate)==null?void 0:r.paypalToken)==null?void 0:a.token;if(h)return h}}catch(d){u=[d]}finally{try{s&&(l=o.return)&&await l.call(o)}finally{if(u)throw u[0]}}throw new _e("PayPal token create operation returned no response")}async fetchLimitedCart({id:e,startingCheckout:n,instrumentName:r}){var a,i;const o=await this.request([go],{id:e},{instrumentName:r,startingCheckout:n});if(o.errors&&await this.onError({operationName:"limitedCartQuery",response:o}),!((a=o.data)!=null&&a.cart))throw new wl("Limited cart query returned no response");return b.leaveBreadcrumb("limitedCartQuery response",{operationName:"limitedCartQuery",body:(i=o.data)==null?void 0:i.cart},"log"),{id:o.data.cart.id,shippingRequired:o.data.cart.lines.edges.some(s=>s.node.merchandise.requiresShipping),sellingPlan:o.data.cart.lines.edges.some(s=>s.node.sellingPlanAllocation!=null)}}async fetchCart({id:e,startingCheckout:n,instrumentName:r}){var a;const i=this.formatLanguage(this.locale),o=this.cartPrepareMigrationEnabled?"Prepare cart":"Fetch cart",s=this.cartPrepareMigrationEnabled?wo:yo,l=this.cartPrepareMigrationEnabled?[bt,jt,Jr]:[bt],u=this.cartPrepareMigrationEnabled?{cartId:e,country:this.country,language:i,withCarrierRates:this.withCarrierRates,prepareCart:!0}:{id:e,country:this.country,language:i,withCarrierRates:this.withCarrierRates},d=await this.requestStream([s,...l],u,{instrumentName:r,startingCheckout:n,operationName:"fetchCart"});try{for(var h=Z(d),p,m,_;p=!(m=await h.next()).done;p=!1){const w=m.value;w.errors&&await this.onError({operationName:"fetchCart",response:w});let E=null,S;if(!w.data)throw new _e("".concat(o," operation returned no data in response"));if(fl(w.data)){const z=w.data.preparedCart;E=(a=z==null?void 0:z.result)==null?void 0:a.cart,S=this.parsePreparedCartMutationErrors(w.errors,this.extractPreparedCartMutationErrors(z))}else E=w.data.cart;if(b.leaveBreadcrumb("".concat(o," response"),{operationName:o,body:E,errors:S,hasNext:w.hasNext},"log"),E&&!w.hasNext){const z=this.parseCartResponse(E);return this.lastValidCart=z,z}}}catch(w){_=[w]}finally{try{p&&(m=h.return)&&await m.call(h)}finally{if(_)throw _[0]}}throw new _e("".concat(o," operation returned no response"))}async updateCartPayment(e,n,r,a){var i,o;const s={cartId:e,input:n},{amount:l,currencyCode:u}=n.amount;let d;const h=ne()+1e4;for(;ne()<h;){const p=await this.requestDeferredCart(bo,s,{instrumentName:r,operationName:"updateCartPayment",abortSignal:a});if(d||(d=p),!this.cartPrepareMigrationEnabled&&p.errors.includes(g.InvalidPaymentAmount)){parseFloat((o=(i=p.data.totalTaxAmount)==null?void 0:i.amount)!=null?o:"0")===0&&await new Promise(m=>setTimeout(m,500)),s.input.amount={amount:p.data.totalAmount.amount,currencyCode:p.data.totalAmount.currencyCode};continue}return(p.data.totalAmount.amount===l||parseFloat(p.data.totalAmount.amount)<=parseFloat(l))&&p.data.totalAmount.currencyCode===u?p:d}throw new Error("Payment update operation timed out")}async updateCartAttributes({cartId:e,attributes:n,instrumentName:r,startingCheckout:a}){const i={cartId:e,attributes:n};return await this.requestDeferredCart(so,i,{instrumentName:r,operationName:"updateCartAttributes",startingCheckout:a})}async submitForCompletion(e,n,r){var a,i;const o=this.formatLanguage(this.locale),s=await this.request([_o],{cartId:e,attemptToken:n,country:this.country,language:o,withCarrierRates:this.withCarrierRates},{instrumentName:r,operationName:"submitForCompletion"});return{data:(i=(a=s.data)==null?void 0:a.cartSubmitForCompletion)==null?void 0:i.result,errors:this.parseCompletionErrors(s)}}async fetchSubscriptionPolicy(){var e,n;const{data:r}=await this.request([fo],{country:this.country,language:this.formatLanguage(this.locale)},{operationName:"fetchSubscriptionPolicy"});return(n=(e=r==null?void 0:r.shop)==null?void 0:e.subscriptionPolicy)==null?void 0:n.body}async cartRemovePersonalData({cartId:e,shopId:n,instrumentName:r}){var a,i;let o,s,l;try{const d=await this.request([Eo,jt],{cartId:e},{instrumentName:r,keepalive:!0});o=this.parseMutationWarnings((i=(a=d.data)==null?void 0:a.cartRemovePersonalData)==null?void 0:i.warnings),s=this.parseMutationErrors(d,"cartRemovePersonalData")}catch(d){l=d.toString()}const u=!(o!=null&&o.length||s!=null&&s.length||l);y.removePersonalDataResult({success:u}),u||y.log({body:"cartRemovePersonalData failed",attributes:{cartId:e,shopId:n,warnings:o,errors:s,exception:l}})}setVisible(){this.hasBeenVisible=!0}generateURLWithOperationName(e){const n=e.match(this.OPERATION_NAME_REGEX);if(n){const r=this.url.includes("?")?"&":"?",a="operation_name=".concat(n[1]);return"".concat(this.url).concat(r).concat(a)}return this.url}async request(e,n,r){return this.client.request(e.join(""),{variables:n,headers:sn(r),url:this.generateURLWithOperationName(e[0]),keepalive:r.keepalive})}async requestStream(e,n,r){return this.client.requestStream(e.join(""),{variables:n,headers:sn(r),url:this.generateURLWithOperationName(e[0]),signal:r.abortSignal})}async requestDeferredCart(e,n,r){var a,i,o,s,l,u,d,h,p;const m=this.formatLanguage(this.locale),_=await this.requestStream([e,bt,jt,Jr],{...n,country:this.country,language:m,withCarrierRates:this.withCarrierRates,prepareCart:this.cartPrepareMigrationEnabled},r);try{for(var w=Z(_),E,S,z;E=!(S=await w.next()).done;E=!1){const O=S.value;O.errors&&await this.onError({operationName:r.operationName,response:O,abortSignal:r.abortSignal});const oe=this.extractMutationErrors(O),R=(i=(a=O.data)==null?void 0:a.preparedCart)==null?void 0:i.result,B=(l=(s=(o=O.data)==null?void 0:o.result)==null?void 0:s.cart)!=null?l:R==null?void 0:R.cart;if(B&&!O.hasNext){let F;this.cartPrepareMigrationEnabled||(F=await this.fetchCart({id:B.id,instrumentName:r.instrumentName,startingCheckout:!1}));const L=this.parseMutationWarnings((d=(u=O.data)==null?void 0:u.result)==null?void 0:d.warnings),ie=this.parsePreparedCartMutationErrors(O.errors,oe),se=F!=null?F:this.parseCartResponse(B);return{data:se,errors:ie,warnings:L,abortReason:an({cart:se,warnings:L}),decelerationReason:Qt({warnings:L,cart:se,cartStatus:R==null?void 0:R.__typename,errors:ie})}}if(this.lastValidCart&&oe.length>0){this.onResultError(oe);const F=this.parseMutationWarnings((p=(h=O.data)==null?void 0:h.result)==null?void 0:p.warnings);return{data:this.lastValidCart,errors:this.parsePreparedCartMutationErrors(O.errors,oe),warnings:F,decelerationReason:Qt({warnings:F,cart:this.lastValidCart})}}}}catch(O){z=[O]}finally{try{E&&(S=w.return)&&await S.call(w)}finally{if(z)throw z[0]}}throw new _e("Deferred cart operation returned no response")}async onError({operationName:e,response:n,abortSignal:r}){if(r!=null&&r.aborted)throw new sr("Request aborted",{reason:"abort",errorDetails:{operationName:e}});const a=await Ao(e,n);if(!a)return;const{message:i,...o}=a,{isIgnorable:s,reason:l}=Po(a,{hasBeenVisible:this.hasBeenVisible});throw b.leaveBreadcrumb(i,{ignored:s,ignoredReason:l,...n,...o},"error"),s?new sr(i,{reason:l,errorDetails:o}):new Er(i,{errorDetails:o,groupingHash:Ro(o)})}onResultError(e){b.leaveBreadcrumb("GraphQL result error",e,"error")}formatLanguage(e){return e.includes("-")?this.LANGUAGES_WITH_REGION.includes(e)?e.toUpperCase().replace("-","_"):e.split("-")[0].toUpperCase():e.toUpperCase()}parseCartResponse(e){var n,r,a;const i=e.lines.edges.map(s=>{var l,u;const d=s.node;return{quantity:d.quantity,totalAmount:{amount:d.cost.totalAmount.amount,currencyCode:d.cost.totalAmount.currencyCode},subtotalAmount:{amount:(l=d.cost.subtotalAmount)==null?void 0:l.amount,currencyCode:(u=d.cost.subtotalAmount)==null?void 0:u.currencyCode},merchandise:{requiresShipping:d.merchandise.requiresShipping},discountAllocations:d.discountAllocations,sellingPlanAllocation:d.sellingPlanAllocation}}),o=e.deliveryGroups.edges.map(s=>s.node);return{id:e.id,totalAmount:e.cost.totalAmount,subtotalAmount:(n=e.cost.subtotalAmount)!=null?n:void 0,totalTaxAmount:(r=e.cost.totalTaxAmount)!=null?r:void 0,totalDutyAmount:(a=e.cost.totalDutyAmount)!=null?a:void 0,discountAllocations:e.discountAllocations,discountCodes:e.discountCodes,lineItems:i,deliveryGroups:o,checkoutUrl:e.checkoutUrl}}parseGraphQlErrors(e){var n,r;return(r=(n=e==null?void 0:e.graphQLErrors)==null?void 0:n.map(a=>a.message.replace(/\(Expected.*\)/,"").trim()))!=null?r:[]}parseMutationError(e){return e.code===Et?{code:Et,message:e.message}:e.field&&e.field.length>0?"".concat(e.code,": ").concat(e.field.join(".")):e.code}parseMutationErrors(e,n){var r,a,i;const o=this.parseGraphQlErrors(e.errors),s=((i=(a=(r=e.data)==null?void 0:r[n])==null?void 0:a.errors)==null?void 0:i.map(this.parseMutationError).filter(u=>u))||[],l=[...o,...s];return b.leaveBreadcrumb("GraphQL errors on mutation",l,"error"),l}parseMutationWarnings(e){return!e||e.length===0?[]:e.map(n=>n.code)}stringifyAttributes(e){return e&&e.map(n=>({key:n.key,value:typeof n.value=="string"?n.value:JSON.stringify(n.value)}))}filterErrors(e){var n;return!((n=e.code)!=null&&n.startsWith("PENDING_DELIVERY_GROUPS")||e.code==="INVALID_PAYMENT")}extractPreparedCartMutationErrors(e){var n,r;const a=(n=e==null?void 0:e.errors)!=null?n:[];return((r=e==null?void 0:e.result)==null?void 0:r.__typename)==="CartStatusNotReady"&&a.push(...e.result.errors),a}extractMutationErrors(e){var n,r,a,i;let o=(a=(r=(n=e.data)==null?void 0:n.result)==null?void 0:r.errors)!=null?a:[];return this.cartPrepareMigrationEnabled&&(o=o.filter(this.filterErrors)),[...o,...this.extractPreparedCartMutationErrors((i=e.data)==null?void 0:i.preparedCart)]}parsePreparedCartMutationErrors(e,n){const r=this.parseGraphQlErrors(e),a=n.map(this.parseMutationError).filter(o=>o),i=[...r,...a];return b.leaveBreadcrumb("GraphQL errors on mutation",i,"error"),i}parseCompletionErrors(e){var n,r,a,i;const o=this.parseGraphQlErrors(e.errors);let s=[];((a=(r=(n=e.data)==null?void 0:n.cartSubmitForCompletion)==null?void 0:r.result)==null?void 0:a.__typename)==="SubmitFailed"&&(s=(i=e.data.cartSubmitForCompletion.result.errors.map(u=>u.code))!=null?i:[]);const l=[...o,...s];return b.leaveBreadcrumb("GraphQL errors on completion",l,"error"),l}}function fl(t){return!("cart"in t)}function bl({emailAddress:t,countryCode:e,phone:n,streetAddress:r,validateAddress:a,preferences:i}){return{email:t,countryCode:e!=null?e:r==null?void 0:r.country,phone:n!=null?n:r==null?void 0:r.phone,deliveryAddressPreferences:r?[{deliveryAddressValidationStrategy:a?"STRICT":"COUNTRY_CODE_ONLY",deliveryAddress:r}]:[],preferences:i}}function sn({instrumentName:t,startingCheckout:e}){const n={};return t&&(n["X-Wallet-Name"]="".concat(t)),e&&(n["X-Start-Wallet-Checkout"]="".concat(e)),n}const fa="LimitedCartQueryError";class wl extends Error{constructor(){super(...arguments),c(this,"name",fa)}}const ba=["OpenTelemetryClientError",Gn,jn,Vn,mt],_l=["Load failed","Failed to fetch","when attempting to fetch resource","GraphQL mutation failed with status 404","Component closed",...ba],ln=new Map([["TypeError","Illegal constructor"],["BugsnagInvalidError",'"isTrusted":true'],[fa,"Limited cart query returned no response"],["AbortError","Fetch is aborted"],["SyntaxError","The string did not match the expected pattern."]]),El=["chrome-lighthouse","meta-externalads","crawler","spider","scraper","facebookexternalhit","bot"];function Al(t){const e=t.exceptions[0];return Cl()?"silenced-user-agent":Sl(t)?"empty-stack-trace":Il(e)?"spin":Pl()?"monkey-patch":Rl(t)?"external-error":Nl(t)?"developer-error":Tl(e.message)?"discarded-message":kl(t)?"discarded-error-class":Ll(e)?"discarded-class-message":Ol(t)?"unactionable-invalid-error":vl(t)||null}function Cl(){const t=navigator.userAgent.toLowerCase();return El.some(e=>t.includes(e))}function Sl(t){return t.exceptions[0].stacktrace.length===0}function Il(t){return t.stacktrace.some(e=>{var n;return(n=e.file)==null?void 0:n.includes("spin.dev")})}function Pl(){return[HTMLElement,Event,document.createElement].some(Hn)}function vl(t){const e=t.exceptions.map(n=>n.stacktrace.filter(r=>!Dl(r)));return e.some(n=>{var r;return!((r=n[0])!=null&&r.inProject)})?"external-trigger":e.some(n=>n.filter(r=>r.inProject).every(r=>[Un,wr,Bn].some(a=>r.file.startsWith(a))))?"wallet-sdk-stack-trace":null}function Dl(t){return t.file.includes("native code")||t.file.includes("/cdn/wpm/")}function Rl(t){return!t.exceptions[0].stacktrace.some(e=>e.inProject)}function Tl(t){return _l.some(e=>t==null?void 0:t.includes(e))}function Nl(t){const e=t.exceptions.some(n=>n.errorClass===mt);return e&&console.error(t.exceptions[0]),e}function kl(t){return t.exceptions.some(e=>ba.includes(e.errorClass))}function Ll(t){const e=t.errorClass,n=t.message;return ln.has(e)&&(n==null?void 0:n.includes(ln.get(e)))}function Ol(t){return t.exceptions[0].errorClass==="BugsnagInvalidError"&&t.unhandled}function xl(t,e){const n=t.exceptions[0];if(!n||!n.message||pi.checkDuplicate(n))return!1;const r=Bl(e);Ml(t,r);let a=null;try{a=Al(t)}catch(i){const o=i;console.error(o);const s={errorClass:"UnfilterableError",message:"Could not properly filter error with message: ".concat(o.message),stacktrace:On(Fn,o)};t.exceptions.unshift(s)}return zl(a),As()&&Hl({errorClass:n.errorClass,message:n.message,stacktrace:JSON.stringify(n.stacktrace),filterResult:a,metadata:JSON.stringify(t.metaData)}),a==null}function Ml(t,e){var n,r,a,i,o;const s=(a=(r=(n=window.ShopifyAnalytics)==null?void 0:n.lib)==null?void 0:r.trekkie)==null?void 0:a.defaultAttributes;t.context=window.location.pathname,t.request||(t.request={}),t.request.url=window.location.href,t.device.orientation=(o=(i=window.screen)==null?void 0:i.orientation)==null?void 0:o.type,Fl(t),$l(t),t.groupingHash=Ul(e);const l=Ir();t.metaData={...t.metaData,shop:{...l,...t.metaData.shop},custom:{...t.metaData.custom,customFetch:Hn(fetch)},error_source:{shop_id:l.shopId},monorail:s},e[0]instanceof Er&&(t.metaData.graphql={...e[0].errorDetails},e[0]instanceof sr&&(t.metaData.graphql.reason=e[0].reason))}function Ul(t){for(const e of t)if("groupingHash"in e&&e.groupingHash)return e.groupingHash.toString()}function Bl(t){const e=[];let n=t;for(;n instanceof Error&&e.length<20;){e.push(n);const r="cause"in n?n.cause:null;if(!r)break;n=r}return e}function Fl(t){for(const e of t.exceptions)for(const n of e.stacktrace)[Un,wr,Bn].some(r=>n.file.startsWith(r))&&(n.inProject=!0)}function $l(t){var e;for(const n of t.exceptions)for(const r of n.stacktrace)(e=r.file)!=null&&e.includes("portable-wallets")&&(r.file=r.file.split("#")[0].split("?")[0])}function zl(t){C.counter({name:I.ErrorTriggered,value:1,attributes:{filterResult:t||"valid-error"}})}function Hl(t){C.log({body:"Bugsnag Error",attributes:{...Ir(),...t}})}var Rn;const Gl={apiKey:"e6b446c1ebe782e2b2fc7eb8ef0dc791",appId:Fn,appVersion:"0.0.0-4bcf5646a343804d5175755068e920f5dd3cebd4",releaseStage:"production",locale:"es",userAgent:navigator.userAgent,metadata:{user:{id:(Rn=ct(ui))!=null?Rn:"unknown"}},withSessionTracking:!1,onError:xl},b=new ii(Gl);b.addOnPostError(t=>{b.leaveBreadcrumb("Bugsnag Error Notified",{errorClass:t.exceptions[0].errorClass,message:t.exceptions[0].message},"error")});var wa={es:{instruments_copy:{checkout:{buy_now:"Comprar ahora"}},error_dialogs:{checkout:{title:"Transacci\xF3n fallida",generic_error:"La pantalla de pago no est\xE1 disponible actualmente debido a problemas t\xE9cnicos. Int\xE9ntalo de nuevo dentro de unos minutos.",button_text:"Cerrar"},wallet:{title:"%{wallet} no disponible",generic_error:"Ha habido un problema con %{wallet}. Int\xE9ntalo de nuevo o utiliza otra forma de pago.",eligibility_error:"Los art\xEDculos que eran elegibles para %{wallet} ya no est\xE1n en tu carrito."},product:{out_of_stock:"Este art\xEDculo ya no est\xE1 disponible."}},more_payment_options:"M\xE1s opciones de pago",brand:{apple_pay:"Apple Pay",amazon_pay:"Amazon Pay",shop_pay:"Shop Pay",buy_with_prime:"Buy with Prime",paypal:"PayPal",google_pay:"Google Pay"},buy_with_button_content:"Comprar con %{wallet}",order_summary:{total:"Total",subtotal:"Subtotal",duties:"Aranceles",taxes:"Impuestos",shipping:"Env\xEDo",discount:"Descuento",shipping_one_time_purchase:"Env\xEDo (compra \xFAnica)",shipping_subscription:"Env\xEDo (suscripci\xF3n)",subscriptions:{recurring_total_tooltip_line:"No incluye env\xEDo, impuestos, aranceles ni descuentos aplicables.",recurring_totals:"%{fixedPrice} cada %{interval}",recurring_totals_with_policies:{one:"Primer pago de %{fixedPrice} y luego %{recurringPrice} cada %{interval}",other:"Primeros %{count} pagos de %{fixedPrice} cada uno y luego %{recurringPrice} cada %{interval}",many:"Primeros %{count} pagos de %{fixedPrice} cada uno y luego %{recurringPrice} cada %{interval}"},recurring_total_intervals:{day:{one:"d\xEDa",other:"%{count} d\xEDas",many:"%{count} d\xEDas"},month:{one:"mes",other:"%{count} meses",many:"%{count} meses"},week:{one:"semana",other:"%{count} semanas",many:"%{count} semanas"},year:{one:"a\xF1o",other:"%{count} a\xF1os",many:"%{count} a\xF1os"}}}},errors:{missing:{address2:"Introduce un apartamento, oficina, etc.",phone:"Introduce un n\xFAmero de tel\xE9fono",first_name:"Introduce un nombre.",email:"Introduce una direcci\xF3n de correo electr\xF3nico",last_name:"Introduce un apellido.",address1:"Introduce una direcci\xF3n.",city:"Introduce una ciudad.",zone:"Selecciona un estado o una provincia.",postal_code:"Introduce un c\xF3digo postal.",shipping_option:"Selecciona un m\xE9todo de env\xEDo",country:"Selecciona un pa\xEDs",emirate:"Ingresa un emirato"},invalid:{email:"Introduce un correo electr\xF3nico v\xE1lido",country:"Selecciona un pa\xEDs o una regi\xF3n.",phone:"Introduce un n\xFAmero de tel\xE9fono v\xE1lido",first_name:"Introduce un nombre v\xE1lido",last_name:"Introduce un apellido v\xE1lido",address1:"Introduce una direcci\xF3n v\xE1lida",address2:"Introduce un departamento, una suite, etc. v\xE1lidos",city:"Introduce una ciudad v\xE1lida",zone:"Selecciona un estado o una provincia v\xE1lidos.",postal_code:"Introduce un c\xF3digo postal v\xE1lido",billing_address:"Direcci\xF3n de facturaci\xF3n no v\xE1lida",shipping_address:"Direcci\xF3n de env\xEDo no v\xE1lida",payment_method:"Forma de pago no v\xE1lida",discount:"Descuento no v\xE1lido",emirate:"Ingresa un emirato v\xE1lido"},emojis:{first_name:"El nombre no puede contener emojis.",last_name:"El apellido no puede contener emojis",city:"La ciudad no puede contener emojis.",address1:"La l\xEDnea de la direcci\xF3n no puede contener emojis.",address2:"La segunda l\xEDnea de la direcci\xF3n no puede contener emojis.",postal_code:"El c\xF3digo postal no puede contener emojis."},too_long:{address1:"La l\xEDnea de la direcci\xF3n es demasiado larga.",address2:"La segunda l\xEDnea de la direcci\xF3n es demasiado larga.",first_name:"El nombre es demasiado largo.",last_name:"El apellido es demasiado largo.",city:"El nombre de la ciudad es demasiado largo."},url:{first_name:"El nombre no puede contener una URL.",last_name:"El apellido no puede contener una URL."},html_tags:{first_name:"El nombre no puede contener etiquetas HTML.",last_name:"El apellido no puede contener etiquetas HTML.",city:"La ciudad no puede contener etiquetas HTML.",address1:"La l\xEDnea de direcci\xF3n no puede contener etiquetas HTML.",address2:"La segunda l\xEDnea de la direcci\xF3n no puede contener etiquetas HTML."},currency:{mismatch:"Tu carrito y tu moneda se modificaron en funci\xF3n del pa\xEDs de env\xEDo."},address_unserviceable:"%{shopName} no ofrece entregas en esta direcci\xF3n actualmente. Usa otra direcci\xF3n para completar tu compra."},subscriptions:{cancellation_policy:"Pol\xEDtica de cancelaci\xF3n",policy_not_found:"No se ha podido encontrar la pol\xEDtica de suscripci\xF3n. Vuelve a cargar la p\xE1gina o cont\xE1ctanos para obtener m\xE1s informaci\xF3n."},shipping_methods:{connect_shipping_methods:"%{methodOne} y %{methodTwo}",choose_delivery_strategy:"Elige una forma de entrega"},shop_promise_delivery:{same_day:"Entrega el mismo d\xEDa",next_day:"Entrega al d\xEDa siguiente",two_day:"Entrega en dos d\xEDas"},action:{view:"Ver %{content}"},express_checkout:"Pago expr\xE9s",delivery_promises:{connect_delivery_promises:"%{promiseOne} y %{promiseTwo}"}}};wa.es;let Jt;class cn extends Error{constructor(e,n){const r="i18n: Missing or invalid translation '".concat(e,"' in '").concat(n,"'");super(r)}}class Vl extends Error{constructor(e,n,r){const a="i18n: Missing translation template key '".concat(e,"' for '").concat(n,"' in '").concat(r,"'");super(a)}}class ql extends Error{constructor(e,n,r){const a="i18n: Invalid pluralization for '".concat(e,"':'").concat(n,"' in '").concat(r,"'");super(a)}}function Ft(){return Jt==null&&(Jt=Wl(wa)),Jt}function Wl(t){const e=(i,o)=>typeof i!="string"&&o.count!=="undefined",n=(i,o)=>{let s=o===1?"one":"other";return o===0&&typeof i!="string"&&i.zero!=="undefined"&&(s="zero"),s},r=(i,o={})=>{const s=i.match(/%\{.+?\}/g);return s?s.reduce((l,u)=>{const d=u.replace(/%\{(.*)\}/,"$1");return o[d]?l.replace(u,o[d]):(b.notify(new Vl(d,i,a)),l)},i):i},a=jl();return{locale:a,translate(i,o={}){var s;const l=i.split(".");let u=t[a];try{for(const d of l)switch(typeof u){case"object":u=u[d];break;case"string":case"undefined":throw new cn(i,a)}if(e(u,o)){if(typeof u=="string")throw new ql(i,u,a);u=u[n(u,o.count)]}if(typeof u!="string")throw new cn(i,a);return r(u,o)}catch(d){return b.notify(d),(s=o.defaultValue)!=null?s:i}}}}function jl(){return"es"}class Yl extends HTMLElement{constructor(){super(...arguments),c(this,"overflow",""),c(this,"formerFocus",null)}connectedCallback(){this.attachShadow({mode:"open"}),this.formerFocus=document.activeElement;const e=Ft();this.render(e);const n=this.shadowRoot,r=n.getElementById("modal");n.querySelectorAll("button").forEach(o=>o.addEventListener("click",this.closeModal.bind(this)));const a=n.getElementById("overlay");a==null||a.addEventListener("click",o=>this.handleOutsideClick(o,r)),document.addEventListener("keydown",this.handleEscapeKey.bind(this));const i=document.createElement("style");i.textContent=Xa,n.appendChild(i),this.overflow=document.body.style.overflow,document.body.style.overflow="hidden",this.trapFocus(r,n),ae("svg-icon",Ja)}trapFocus(e,n){if(!e)return;const r=e.querySelectorAll("a[href], button"),a=Array.from(r),i=a[0],o=a[a.length-1];e.addEventListener("keydown",s=>{s.key==="Tab"&&!s.shiftKey&&n.activeElement===o&&(s.preventDefault(),i.focus()),s.key==="Tab"&&s.shiftKey&&n.activeElement===i&&(s.preventDefault(),o.focus())}),i.focus()}handleOutsideClick(e,n){n&&(n.contains(e.target)||this.closeModal())}handleEscapeKey(e){e.key==="Escape"&&this.closeModal()}closeModal(){var e;document.body.style.overflow=this.overflow,this.remove(),this.formerFocus&&"focus"in(this==null?void 0:this.formerFocus)&&typeof this.formerFocus.focus=="function"&&((e=this.formerFocus)==null||e.focus())}render(e){const n=this.getAttribute("title"),r=this.getAttribute("has-close-button"),a=e.translate("error_dialogs.checkout.button_text"),i='\n      <footer>\n        <button type="button" id="close-button" tabindex="0">'.concat(a,"</button>\n      </footer>\n    ");this.shadowRoot.innerHTML='\n      <div id="overlay">\n        <div id="modal" role="dialog" aria-modal="true" aria-labelledby="title">\n          <header>\n            <h2 id="title">\n              <span class="capitalize">'.concat(n,'</span>\n            </h2>\n            <button type="button" id="close-icon" aria-label=').concat(a,' tabindex="0">\n              <svg-icon color="gray"></svg-icon>\n            </button>\n          </header>\n          <div id="content">\n            <p><slot></slot></p>\n          </div>\n          ').concat(r?i:"","\n        </div>\n      </div>\n    ")}}const Kl="@keyframes topLevelModalLoadingSkeleton{50%{opacity:1}75%{opacity:.5}to{opacity:1}}top-level-modal .text-skeleton{display:inline-block;width:100%;height:14px;margin-bottom:7px;animation:topLevelModalLoadingSkeleton 4s ease infinite;animation-delay:-.168s;text-decoration:none!important;background-color:#dedede}top-level-modal .text-skeleton:last-of-type{width:50%}";function $t({type:t="button",label:e=void 0}={}){const n=document.createElement("button");return n.type=t,e&&n.setAttribute("aria-label",e),n}function zt(t,e){for(const[n,r]of Object.entries(e))t.setAttribute(n,r)}function dn(t,e){for(const[n,r]of Object.entries(e))t.style.setProperty(n,r)}function yt(t,e){const n=document.createElement("top-level-modal");n.textContent=e,zt(n,{"data-testid":"top-level-modal","has-close-button":"true",title:t}),document.body.appendChild(n)}function Rr(t,e,n){const r=document.createElement("top-level-modal");zt(r,{"data-testid":"top-level-modal",title:t}),e instanceof Promise?(r.innerHTML="\n      <style>".concat(Kl,'</style>\n      <span class="text-skeleton">&nbsp;</span>\n      <span class="text-skeleton">&nbsp;</span>\n      <span class="text-skeleton">&nbsp;</span>\n      <span class="text-skeleton">&nbsp;</span>\n      <span class="text-skeleton">&nbsp;</span>\n    '),e.then(a=>{a&&a!==""?r.innerHTML=a:r.innerHTML=n!=null?n:""}).catch(()=>{r.innerHTML=n!=null?n:""})):r.innerHTML=e,document.body.appendChild(r)}function N(t,e){yt(e.translate("error_dialogs.wallet.title",{wallet:t}),e.translate("error_dialogs.wallet.generic_error",{wallet:t}))}function gt(t){yt(t.translate("error_dialogs.checkout.title"),t.translate("error_dialogs.checkout.generic_error"))}function Ne(t,e){yt(t.translate("error_dialogs.checkout.title"),e)}function ke(t,e){switch(e){case q.InvalidQuantity:case q.VariantRequiresSellingPlan:case q.SellingPlanNotApplicable:case q.MerchandiseIdInvalid:gt(t);break}}function me(t){yt(t.translate("error_dialogs.checkout.title"),t.translate("error_dialogs.product.out_of_stock"))}ae("top-level-modal",Yl);class ft extends HTMLElement{get buyerCountry(){return this.getAttribute("buyer-country")}get recommendedInstrument(){return this.getAttribute("recommended-instrument")}get buyerCurrency(){const e=this.getAttribute("buyer-currency");if(e==null)throw new Error("WalletElement buyer-currency is null");return e}get accessToken(){return this.getAttribute("access-token")}get disabled(){return this.hasAttribute("disabled")}set disabled(e){e?this.setAttribute("disabled",""):this.removeAttribute("disabled")}get hasSellingPlan(){const e=this.getAttribute("has-selling-plan");return e==="true"||e===""}set hasSellingPlan(e){e?this.setAttribute("has-selling-plan",""):this.removeAttribute("has-selling-plan")}get isShippingRequired(){const e=this.getAttribute("requires-shipping");return e==="true"||e===""}set isShippingRequired(e){e?this.setAttribute("requires-shipping",""):this.removeAttribute("requires-shipping")}get pciEnabled(){return this.hasAttribute("pci-enabled")}}const Ql="#more-payment-options-link{cursor:pointer}";function Jl(t){const e=n=>{n.persisted&&(t(),window.removeEventListener("pageshow",e))};window.addEventListener("pageshow",e)}class Xl extends Error{constructor(e){e instanceof Error?(super("[".concat(e.name,"] ").concat(e.message),{cause:e.cause}),this.stack=e.stack):super("[HandleCreateCartError] ".concat(String(e))),this.name="HandleCreateCartError"}}async function ge({element:t,instrumentName:e,dataSource:n}){try{const r=await n.getInitialCart(e),{cart:a,errors:i,abortReason:o,decelerationReason:s}=r;Jl(()=>{t&&(t.disabled=!1)});const l=vr(i),u=ec(i),d=u||l;if(u&&y.unrecoverableCartError({instrument:e,errorName:u}),!a&&!d)throw new Error("[".concat(e,"] Failed to create cart: ").concat(JSON.stringify(i)));if(a){if(!a.id)throw new Error("[".concat(e,"] received invalid cart"));if(!a.checkoutUrl)throw new Error("[".concat(e,"] Created cart with no checkout URL"))}if(i.length>0&&!d)throw new Error("Errors present after cart creation: ".concat(JSON.stringify(i)));return{cart:a,customValidationError:l,decelerationReason:s,abortReason:o,...u?{unrecoverableError:u}:{}}}catch(r){throw new Xl(r)}finally{t.disabled=!1}}const Zl={[g.InvalidQuantity]:q.InvalidQuantity,[g.VariantRequiresSellingPlan]:q.VariantRequiresSellingPlan,[g.SellingPlanNotApplicable]:q.SellingPlanNotApplicable,[g.MerchandiseIdInvalid]:q.MerchandiseIdInvalid};function ec(t){if(t.length!==1)return null;const[e]=t;return typeof e=="object"?null:Zl[e]||null}class tc extends ft{constructor(){super(),c(this,"name",f.MoreOptions),c(this,"anchor"),c(this,"i18n"),c(this,"dataSource"),c(this,"classNames"),c(this,"setI18n"),c(this,"setDataSource"),c(this,"setClassNames"),[this.i18n,this.setI18n]=ce(),[this.dataSource,this.setDataSource]=ce(),[this.classNames,this.setClassNames]=ce()}static get observedAttributes(){return["disabled"]}connectedCallback(){this.anchor||this.render()}attributeChangedCallback(e,n,r){n!==r&&e==="disabled"&&this.anchor&&(r===""?this.anchor.setAttribute("aria-disabled","true"):this.anchor.removeAttribute("aria-disabled"),this.setAccessibilityAttributes())}async handleClick(e){var n,r;if(e.preventDefault(),this.disabled||!this.anchor)return;this.disabled=!0;const[a,i]=await Promise.all([this.i18n,this.dataSource]);try{const{decelerationReason:o,cart:s,customValidationError:l,unrecoverableError:u,abortReason:d}=await ge({element:this,instrumentName:f.MoreOptions,dataSource:i});if(u){y.sheetClicked({instrument:this.name,result:"failed"}),ke(a,u);return}if(l){Ne(a,l.message);return}if(d==="out_of_stock"){y.sheetClicked({instrument:this.name,result:"failed"}),me(a);return}if(o){M({checkoutUrl:(n=s==null?void 0:s.checkoutUrl)!=null?n:"",instrumentName:f.MoreOptions,reason:o});return}const h=this.recommendedInstrument===f.ShopPay?"skip_shop_pay":"allow_redirect";y.sheetClicked({instrument:f.MoreOptions,result:"success"}),pe((r=s==null?void 0:s.checkoutUrl)!=null?r:"",h)}catch(o){y.sheetClicked({instrument:f.MoreOptions,result:"failed"}),b.notify(o),gt(a)}}async render(){const e=await this.i18n,n=await this.classNames;this.anchor=document.createElement("a"),this.anchor.textContent=e.translate("more_payment_options"),this.anchor.className=n,this.anchor.setAttribute("id","more-payment-options-link"),this.anchor.onclick=a=>this.handleClick(a),this.setAccessibilityAttributes(),this.appendChild(this.anchor);const r=document.createElement("style");r.textContent=Ql,this.appendChild(r)}setAccessibilityAttributes(){this.anchor&&(this.disabled?(this.anchor.removeAttribute("href"),this.anchor.role="link"):(this.anchor.removeAttribute("role"),this.anchor.href="#"))}}var k=(t=>(t.ButtonDisplay="buttonDisplay",t.LoadInstrument="loadInstrument",t.LoadSdk="loadSDK",t.AuthorizationLatency="authorizationLatency",t.SheetLoad="sheetLoad",t.StyleExtract="styleExtract",t))(k||{});function _a(){var t,e;return!!((t=window.performance)!=null&&t.mark)&&!!((e=window.performance)!=null&&e.measure)}function Ye(t,e){return Je(t,e),()=>Y(t,e)}function Je(t,e){_a()&&window.performance.mark("".concat(t,"-").concat(e,"-start"))}function Y(t,e){var n,r;if(_a())try{const a="".concat(t,"-").concat(e,"-start"),i="".concat(t,"-").concat(e,"-end"),o="".concat(t,"-").concat(e,"-duration");window.performance.mark(i);const s=(r=window.performance.measure(o,a,i))!=null?r:(n=window.performance.getEntriesByName(o,"measure"))==null?void 0:n[0],l=s==null?void 0:s.duration;return l==null?void 0:l}catch(a){return}}function Tr(t,e,n){if(nt!=null&&nt.length){const r=nt.join(",");t.querySelectorAll(r).forEach(a=>{n===null?a.removeAttribute(e):a.setAttribute(e,n)})}}function rc(t,e){try{const n=JSON.parse(t!=null?t:"[]");if(!(n instanceof Array))throw new J({code:"invalid-wallet-configs",message:"[".concat(e,"] invalid walletConfigs found")});return n}catch(n){throw new J({code:"invalid-wallet-configs",message:"[".concat(e,"] Error while parsing walletConfigs JSON: ").concat(n)},{cause:n})}}function nc(t,e){try{return t?JSON.parse(t):null}catch(n){throw new J({code:"invalid-wallet-config",message:"[".concat(e,"] Error while parsing walletConfig JSON: ").concat(n)},{cause:n})}}function St(t,e){var n;const r=e==null?void 0:e.getLoadEligibility(t);return y.instrumentLoadEligibility({instrument:e==null?void 0:e.getInstrumentName(),result:r!=null&&r.eligible?"success":"failed",reason:r!=null&&r.eligible||r==null?void 0:r.reason}),(n=r==null?void 0:r.eligible)!=null?n:!1}function ac(t,e){return e.filter(n=>St(t,n))}async function Ea({walletInstrument:t,instanceNumber:e}){const n=t.getInstrumentName(),r=Ye(k.LoadSdk,"".concat(n,":").concat(e));try{await t.loadWalletSDK(),y.instrumentSDKLoaded({instrument:n,measurement:r(),result:"success"})}catch(i){return b.leaveBreadcrumb("Failed to load wallet SDK",{instrumentName:n,instanceNumber:e},"error"),b.notify(i),y.instrumentSDKLoaded({instrument:n,measurement:r(),result:"failed"}),null}let a;try{a=t.getPartnerSDKEligibility()}catch(i){b.notify(i),a={eligible:!1,reason:"uncaught exception"}}return a.eligible?(y.instrumentSDKEligible({instrument:n,result:"success"}),t):(y.instrumentSDKEligible({instrument:n,reason:a.reason,result:"failed"}),null)}function Re(t,...e){try{const n=window[t];if(typeof n=="function")return new n(...e)}catch(n){}return null}const Aa="d6d12da0",Oe="0402dc2d",ic="1b55bc27";var Ge;class Nr extends ft{constructor(){super(...arguments),c(this,"apiClient"),c(this,"i18n"),c(this,"policy"),c(this,"isFetchingPolicy",!1),c(this,"intersectionObserver",null),$(this,Ge),c(this,"handleIntersection",(e,n)=>{e.some(r=>r.isIntersecting)&&(this.apiClient&&this.apiClient.setVisible(),n.disconnect(),H({event:U.FirstVisible}))})}connectedCallback(){this.mountIntersectionObserver()}get shopId(){return this.getAttribute("shop-id")}get cartId(){return this.getAttribute("cart-id")}get walletConfigs(){return this.getAttribute("wallet-configs")}get recommendedWallet(){return this.getAttribute("recommended")}get amazonWallet(){return this.isFlagEnabled(Oe)?this.getAttribute("amazon-wallet"):null}get fallbackWallet(){return this.getAttribute("fallback")}get isBuyWithPrimeEligible(){return this.isFlagEnabled(Oe)?this.hasAttribute("is-buy-with-prime-eligible"):!1}get isShopPayPersonalizationEnabled(){return this.isFlagEnabled(ic)}get variantParams(){var e;try{return JSON.parse((e=this.getAttribute("variant-params"))!=null?e:"[]")}catch(n){throw new J({code:"invalid-variant-params",message:"variant-params must be a valid JSON string. Received variant-params: ".concat(this.getAttribute("variant-params"),", received error: ").concat(n)})}}get styleExtractorDisabled(){return this.hasAttribute("disable-compat")}get onlySdk(){return this.hasAttribute("only-sdk")}get debug(){return this.hasAttribute("debug")}get hidePaymentButton(){return this.isFlagEnabled(Oe)?this.isBuyWithPrimeEligible&&this.isRedirectedFromAmazon:!1}get isRedirectedFromAmazon(){return this.isFlagEnabled(Oe)?sessionStorage.getItem("shopify_amazon_referral")==="mShop":!1}triggerLoadedEvent(){document.dispatchEvent(new Event("shopify:payment_button:loaded",{bubbles:!0,cancelable:!0}))}async attributeChangedCallback(e,n,r){n!==r&&Tr(this,e,r)}showBuyerConsent(e,n){var r,a,i;!e||!n||(i=(a=(r=window.Shopify)==null?void 0:r.PaymentButton)==null?void 0:a.showBuyerConsent)==null||i.call(a,o=>this.onClickSubscriptionPolicy(o,e,n))}hideBuyerConsent(){var e,n,r;(r=(n=(e=window.Shopify)==null?void 0:e.PaymentButton)==null?void 0:n.hideBuyerConsent)==null||r.call(n)}clearUI(){this.innerHTML=""}isFlagEnabled(e){return A(this,Ge)==null&&x(this,Ge,this.getEnabledFlags()),A(this,Ge).includes(e)}getEnabledFlags(){const e=this.getAttribute("enabled-flags");if(!e)return[];try{return JSON.parse(e)}catch(n){return console.warn("Invalid enabled-flags attribute value:",e),[]}}async onClickSubscriptionPolicy(e,n,r){if(e.preventDefault(),this.isFetchingPolicy)return;if(this.policy){this.showPolicy(this.policy,r);return}this.isFetchingPolicy=!0;const a=this.fetchSubscriptionPolicy(n).then(i=>(this.policy=i,i)).catch(i=>(b.notify(i),r.translate("subscriptions.policy_not_found"))).finally(()=>{this.isFetchingPolicy=!1});this.showPolicy(a,r)}async fetchSubscriptionPolicy(e){const n=await e.fetchSubscriptionPolicy();return this.policy=n,n}showPolicy(e,n){Rr(n.translate("subscriptions.cancellation_policy"),e,n.translate("subscriptions.policy_not_found"))}mountIntersectionObserver(){this.intersectionObserver=Re("IntersectionObserver",this.handleIntersection),this.intersectionObserver?this.intersectionObserver.observe(this):H({event:U.FirstVisibleNotAvailable})}}Ge=new WeakMap;function kr(t){var e,n;Es({shopId:(e=t.shopId)!=null?e:$n,cartId:(n=t.cartId)!=null?n:void 0,debug:t.debug,pageType:t.pageType})}function Ca({type:t,form:e}){if(!e)return[];const n=document.querySelectorAll('[name^="'.concat(t,'"][form^="').concat(CSS.escape(e.getAttribute("id")||""),'"]')),r=e.querySelectorAll('[name^="'.concat(t,'"]')),a=[...n,...r],i={};return a.forEach(o=>{const s=oc(o),l=sc(o);s&&typeof l<"u"&&l.trim()!==""&&(i[s]=l)}),Object.entries(i!=null?i:{}).map(([o,s])=>({key:o,value:s}))}function Ht({page:t,element:e}){const n=t==="product"?'[data-shopify="payment-button"]':'[data-shopify="dynamic-checkout-cart"]',r=un(e,n)||document.querySelector(n);return un(r,"form")}const tt=Element.prototype;function un(t,e){if(tt.matches=tt.matches||tt.webkitMatchesSelector||tt.msMatchesSelector||tt.mozMatchesSelector,!t||t.matches(e))return t;let n=t;for(;n&&n!==document.body;)if(n=n.parentElement,n&&n.matches(e))return n;return null}function oc(t){const e=t.getAttribute("name");if(e===null)return null;const n=e.indexOf("["),r=e.lastIndexOf("]");return n===-1||r===-1?null:e.substring(n+1,r)}function sc(t){if(!(["radio","checkbox"].includes(t.type)&&!t.checked))try{return t.value===""||typeof t.value>"u"?void 0:t.value}catch(e){return}}function Lr(t){const e=Ht({page:"product",element:t});return Sa(e)}function Sa(t){var e;const n=t==null?void 0:t.elements;if(!n)return null;const r=It(n,"id");if(!r||isNaN(Number(r))||Number(r)<=0)return null;const a=Number((e=It(n,"quantity"))!=null?e:"1"),i="gid://shopify/ProductVariant/".concat(r),o=cc(n),s=Ca({type:"properties",form:t});return{variantId:r,quantity:a,merchandiseId:i,sellingPlanId:o,lineItemProperties:s}}function lc(t){const e=t==null?void 0:t.elements;return e?!!It(e,"selling_plan"):!1}function It(t,e){var n,r;let a=t.namedItem(e);if(a instanceof HTMLSelectElement)try{return(n=a.value)!=null?n:null}catch(i){return null}if(a&&"length"in a&&(a=a.item(0)),a instanceof HTMLInputElement||a instanceof HTMLSelectElement)try{return(r=a.value)!=null?r:null}catch(i){return null}return null}function cc(t){const e=It(t,"selling_plan");if(!(!e||e==="undefined"))return"gid://shopify/SellingPlan/".concat(e)}class Or{constructor(e,n){c(this,"addToCartMutationObserver"),c(this,"addToCartForm"),c(this,"addToCartButtons"),this.element=e,this.onFormChanged=n,this.addToCartMutationObserver=null,this.addToCartForm=null,this.addToCartButtons=[]}setupMutationObservers(){if(this.findAndSetAddToCartButtons(),!this.addToCartForm)return;this.syncComponentStateWithForm();const e=new MutationObserver(()=>this.reobserveOnFormChanges());this.addToCartMutationObserver=new MutationObserver(()=>this.syncComponentStateWithForm()),e.observe(this.addToCartForm,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),this.observeAddToCartButtons()}syncComponentStateWithForm(){if(!this.addToCartForm){this.onFormChanged({disabled:!0,hasSellingPlan:!1});return}const e=Sa(this.addToCartForm),n=!!(this.addToCartButtons.length>0&&this.addToCartButtons.every(r=>r.hasAttribute("disabled")||r.getAttribute("aria-disabled")==="true"));this.onFormChanged({disabled:n||e===null,hasSellingPlan:lc(this.addToCartForm),variantId:e==null?void 0:e.variantId,sellingPlanId:e==null?void 0:e.sellingPlanId})}observeAddToCartButtons(){this.addToCartButtons.forEach(e=>{this.addToCartMutationObserver.observe(e,{attributes:!0})})}reobserveOnFormChanges(){var e;(e=this.addToCartMutationObserver)==null||e.disconnect(),this.findAndSetAddToCartButtons(),this.observeAddToCartButtons(),this.syncComponentStateWithForm()}findAndSetAddToCartButtons(){if(this.addToCartForm=Ht({page:"product",element:this.element}),this.addToCartForm){const e=this.addToCartForm.querySelectorAll("[type=submit]");e instanceof NodeList&&(this.addToCartButtons=[...e])}}}function dc(t,e,n){try{const r=JSON.parse(t!=null?t:"{}"),a=JSON.parse(e!=null?e:JSON.stringify(zn));if(r===null||Object.keys(r).length===0){if(!(a instanceof Object))throw y.walletConfigDeveloperError(),new J({code:"invalid-fallback-wallet-config",message:"[".concat(n,"] Invalid fallback wallet configs JSON")});return{recommendedWallet:null,fallbackWallet:a}}return{recommendedWallet:r,fallbackWallet:a}}catch(r){throw y.walletConfigDeveloperError(),new J({code:"invalid-recommended-fallback-config",message:"[".concat(n,"] Error while parsing recommended/fallback JSON: ").concat(r)},{cause:r})}}const uc=".shopify-payment-button__button--hidden{visibility:hidden}.shopify-payment-button__button{height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);min-height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);border-radius:var(--shopify-accelerated-checkout-button-border-radius, 0px);width:100%;border:none;box-shadow:0 0 0 0 transparent;color:#fff;cursor:pointer;display:block;font-size:1em;font-weight:500;line-height:1;text-align:center;transition:background .2s ease-in-out}.shopify-payment-button__button[disabled]{opacity:.6;cursor:default}.shopify-payment-button__button--unbranded{background-color:#1990c6;padding:1em 2em}.shopify-payment-button__button--unbranded:hover:not([disabled]){background-color:#136f99}.shopify-payment-button__more-options{background:transparent;border:0 none;cursor:pointer;display:block;font-size:1em;margin-top:1em;text-align:center;text-decoration:underline;width:100%}.shopify-payment-button__more-options.shopify-payment-button__skeleton{height:auto!important;min-height:0!important;border-radius:4px!important;width:50%;margin-left:25%;margin-right:25%}.shopify-payment-button__more-options[disabled]{opacity:.6;cursor:default!important}.shopify-payment-button__button.shopify-payment-button__button--branded{display:flex;flex-direction:column;position:relative;z-index:1}.shopify-payment-button__button.shopify-payment-button__button--branded .shopify-cleanslate{flex:1!important;display:flex!important;flex-direction:column!important}.shopify-payment-button__button.button.loading{position:relative;color:transparent}.shopify-payment-button__button.button.loading>.loading-overlay__spinner{top:50%;left:50%;transform:translate(-50%,-50%);position:absolute;height:100%;display:flex;align-items:center}.shopify-payment-button__button.button.loading>.loading-overlay__spinner .spinner{width:-moz-fit-content;width:-webkit-fit-content;width:fit-content}.button.loading>.loading-overlay__spinner .path{stroke:#fff}.shopify-payment-button__button .loading-overlay__spinner{width:1.8rem;display:inline-block}.shopify-payment-button__button .spinner{animation:shopify-rotator 1.4s linear infinite}@keyframes shopify-rotator{0%{transform:rotate(0)}to{transform:rotate(270deg)}}.shopify-payment-button__button .path{stroke-dasharray:280;stroke-dashoffset:0;transform-origin:center;stroke:#121212;animation:shopify-dash 1.4s ease-in-out infinite}@media screen and (forced-colors: active){.shopify-payment-button__button .path{stroke:CanvasText}}@keyframes shopify-dash{0%{stroke-dashoffset:280}50%{stroke-dashoffset:75;transform:rotate(135deg)}to{stroke-dashoffset:280;transform:rotate(450deg)}}#shopify-buyer-consent{margin-top:1em;display:inline-block;width:100%}#shopify-buyer-consent.hidden{display:none}#shopify-subscription-policy-button{background:none;border:none;padding:0;text-decoration:underline;font-size:inherit;cursor:pointer}#shopify-subscription-policy-button:before{box-shadow:none}@keyframes acceleratedCheckoutLoadingSkeleton{50%{opacity:var(--shopify-accelerated-checkout-skeleton-animation-opacity-start, 1)}75%{opacity:var(--shopify-accelerated-checkout-skeleton-animation-opacity-end, .5)}to{opacity:var(--shopify-accelerated-checkout-skeleton-animation-opacity-start, 1)}}.shopify-payment-button__skeleton{animation:acceleratedCheckoutLoadingSkeleton var(--shopify-accelerated-checkout-skeleton-animation-duration, 4s) var(--shopify-accelerated-checkout-skeleton-animation-timing-function, ease) infinite;animation-delay:-.168s;background-color:var(--shopify-accelerated-checkout-skeleton-background-color, #dedede);box-sizing:border-box;text-decoration:none!important;height:var(--shopify-accelerated-checkout-button-block-size, inherit);min-height:25px;max-height:55px;border-radius:var( --shopify-accelerated-checkout-button-border-radius, inherit )}",hc='.accelerated-checkout-button{height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);min-height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);border-radius:var(--shopify-accelerated-checkout-button-border-radius, 0px);box-shadow:var(--shopify-accelerated-checkout-button-box-shadow)}:host([page-type="cart_page"]) .accelerated-checkout-button{height:100%;width:100%;border-radius:var(--shopify-accelerated-checkout-button-border-radius, 4px);box-shadow:var(--shopify-accelerated-checkout-button-box-shadow)}:host([page-type="product"]) .accelerated-checkout-button{min-width:150px}@media (forced-colors: active){.accelerated-checkout-button{border:1px solid transparent!important}:host([page-type="cart_page"]) .accelerated-checkout-button{border:1px solid transparent!important}}',pc=70;class ot{constructor(e,n,r,a){c(this,"red"),c(this,"green"),c(this,"blue"),c(this,"opacity"),this.red=e||0,this.green=n||0,this.blue=r||0,this.opacity=typeof a>"u"?1:a}getLuminance(){const e=this.red&255,n=this.green&255,r=this.blue&255;return e*.2126+n*.7152+r*.0722}isDark(){return this.opacity===0?!1:this.getLuminance()/this.opacity<pc}blendWith(e,n){this.opacity=1-(1-e.opacity)*(1-n.opacity),this.red=this.blend(e.red,n.red,e,n),this.green=this.blend(e.green,n.green,e,n),this.blue=this.blend(e.blue,n.blue,e,n)}blend(e,n,r,a){return Math.round(e*r.opacity/this.opacity+n*a.opacity*(1-r.opacity)/this.opacity)}}function mc(t){const e=[...t];let n=new ot(0,0,0,0),r=new ot(255,255,255,1),a=e.shift();for(;a;)n.opacity>0&&a.opacity>0?(r=new ot(0,0,0,0),r.blendWith(a,n)):a.opacity>0?r=a:r=n,n=r,a=e.shift();return r}const cr=new ot(255,255,255,1);function yc(t){const e=[];let n=t;for(;n.parentElement;){n=n.parentElement;const r=window.getComputedStyle(n).backgroundColor,a=gc(r);if(r&&e.push(a),a.opacity===1)break}try{return mc(e.reverse())}catch(r){return b.notify(new Ia("[BackgroundDetection] Failed to blend colors for element ".concat(t.outerHTML),{cause:r})),cr}}function gc(t){if(!t)return cr;try{const e=t.split("(")[1].split(")")[0].replace(" ","").split(",");return new ot(Number(e[0]),Number(e[1]),Number(e[2]),typeof e[3]>"u"?1:Number(e[3]))}catch(e){b.notify(new Ia("[BackgroundDetection] Failed to convert rgbString to array ".concat(t),{cause:e}))}return cr}class Ia extends Error{constructor(){super(...arguments),c(this,"name","ColorProcessingError")}}var de;class fe extends ft{constructor(){super(),c(this,"name",f.Unknown),c(this,"i18n"),c(this,"dataSource"),c(this,"apiClient"),c(this,"classNames"),c(this,"containerInstanceNumber"),c(this,"onRendered"),c(this,"setI18n"),c(this,"setDataSource"),c(this,"setApiClient"),c(this,"setClassNames"),c(this,"setContainerInstanceNumber"),c(this,"parsedWalletParams"),$(this,de),this.onRendered=()=>{},[this.i18n,this.setI18n]=ce(),[this.dataSource,this.setDataSource]=ce(),[this.apiClient,this.setApiClient]=ce(),[this.classNames,this.setClassNames]=ce(),[this.containerInstanceNumber,this.setContainerInstanceNumber]=ce()}async cleanupOnFailure(e,n){var r;try{await e()}catch(a){b.notify(new fc("An error occurred requiring cleanup when attempting to render the ".concat(n," instrument: ").concat(a),{cause:a}));const i=await this.containerInstanceNumber;y.instrumentLoaded({instrumentOrComponentName:n,result:"failed",measurement:Y(k.ButtonDisplay,"".concat(n,":").concat(i))}),(r=this.parentElement)==null||r.remove()}}attributeChangedCallback(e,n,r,a){n!==r&&e==="disabled"&&a&&(r===""?(a.setAttribute("aria-disabled","true"),a.setAttribute("disabled","")):(a.removeAttribute("aria-disabled"),a.removeAttribute("disabled")))}get pageType(){return this.getAttribute("page-type")||P.Unknown}get buttonTheme(){return yc(this).isDark()?"LIGHT":"DARK"}get isCTA(){return this.hasAttribute("call-to-action")}get walletParams(){var e;if(this.parsedWalletParams===void 0)try{this.parsedWalletParams=JSON.parse((e=this.getAttribute("wallet-params"))!=null?e:"{}")}catch(n){throw new J({code:"invalid-wallet-params",message:"[".concat(this.name,"] Error while parsing wallet-params JSON: ").concat(n)},{cause:n})}return this.parsedWalletParams}async initializeShadowStyles(e,n=""){const r=document.createElement("style");r.innerHTML=[hc,n].join("\n"),e.appendChild(r)}async ensureLightDOMIsNotEmpty(){this.textContent="\xA0"}dispatchWalletEvent({eventName:e,detail:n={}}){var r;(r=this.parentElement)==null||r.dispatchEvent(new CustomEvent(e,{detail:n}))}getOrCreateShadowRoot(){return A(this,de)||x(this,de,this.attachShadow({mode:"closed"})),A(this,de)}clearShadowRoot(){A(this,de)&&(A(this,de).innerHTML="")}}de=new WeakMap;class fc extends Error{constructor(){super(...arguments),c(this,"name","CleanupOnFailureError")}}class bc extends fe{constructor(){super(...arguments),c(this,"name",f.BuyItNow),c(this,"button",null)}static get observedAttributes(){return["disabled"]}connectedCallback(){this.cleanupOnFailure(this.render.bind(this),this.name)}attributeChangedCallback(e,n,r){super.attributeChangedCallback(e,n,r,this.button)}async handleClick(){var e,n;if(this.disabled||!this.button||this.button.getAttribute("aria-disabled"))return;this.disabled=!0;const[r,a]=await Promise.all([this.i18n,this.dataSource]);try{const{decelerationReason:i,cart:o,customValidationError:s,unrecoverableError:l,abortReason:u}=await ge({element:this,instrumentName:this.name,dataSource:a});if(l){y.sheetClicked({instrument:this.name,result:"failed"}),ke(r,l);return}if(u==="out_of_stock"){y.sheetClicked({instrument:this.name,result:"failed"}),me(r);return}if(y.sheetClicked({instrument:this.name,result:"success"}),s){Ne(r,s.message);return}if(i){M({checkoutUrl:(e=o==null?void 0:o.checkoutUrl)!=null?e:"",instrumentName:this.name,reason:i});return}pe((n=o==null?void 0:o.checkoutUrl)!=null?n:"","allow_redirect")}catch(i){b.notify(i),y.sheetClicked({instrument:this.name,result:"failed"}),gt(r)}}async render(){var e;this.button||(this.button=$t());const n=await this.classNames;this.button.textContent=(await this.i18n).translate("instruments_copy.checkout.buy_now"),this.button.className=n,this.disabled&&this.button.setAttribute("aria-disabled","true"),this.button.onclick=()=>this.handleClick(),this.appendChild(this.button),(e=this.onRendered)==null||e.call(this)}}const wc=1e3;function _c(){const t=navigator.userAgent,e=t.indexOf("Android")>-1,n=t.indexOf("Chrome/")>-1,r=parseInt((/Chrome\/([0-9]+)/.exec(t)||["0","0"])[1],10);return e&&n&&r===114}function Ec(t){return Promise.race([t,new Promise((e,n)=>setTimeout(()=>n(new Error("timed out")),wc))])}class be{constructor(e){c(this,"walletParams"),this.walletParams=e.wallet_params}static walletName(){throw new Error("Must define walletName for WalletInstrument subclass")}async createWebComponent({walletContainer:e,dataSource:n,i18n:r,apiClient:a,containerInstanceNumber:i,classNames:o="",callToAction:s,pageType:l=P.ProductPage,slot:u="button",onRendered:d=()=>{}}){var h,p,m;const _=this.getWebComponentName();if(!ae(_,this.getWebComponentClass(),{isChildCustomElement:!0}))throw new re("Failed to define custom element ".concat(_,", aborting execution"));try{await Ec(customElements.whenDefined(_))}catch(S){throw new re("Timeout waiting for custom element ".concat(_," to be defined"))}const w=document.createElement(_);if(!(w instanceof fe))throw new re("Failed to create web component instance for ".concat(_,", element is not a WalletButtonElement"));const E={"access-token":(h=e.accessToken)!=null?h:"","buyer-country":(p=e.buyerCountry)!=null?p:"","buyer-currency":e.buyerCurrency,"wallet-params":JSON.stringify((m=this.walletParams)!=null?m:{}),"page-type":l,slot:u};try{e.disabled&&w.setAttribute("disabled",""),zt(w,E),e.isShippingRequired&&w.setAttribute("requires-shipping",""),e.hasSellingPlan&&w.setAttribute("has-selling-plan",""),e.pciEnabled&&w.setAttribute("pci-enabled","")}catch(S){const z=S;throw z.message.includes("setAttribute is not a function")&&_c()?new re(z.message):z}if(s&&w.setAttribute("call-to-action",""),typeof w.setDataSource!="function")throw new re("Custom element ".concat(_," is not properly upgraded, aborting execution"));return w.setDataSource(n),w.setApiClient(a),w.setI18n(r),w.setClassNames(o),w.setContainerInstanceNumber(i),w.onRendered=d,w}loadWalletSDK(){return Promise.resolve()}getLoadEligibility(e){return{eligible:!0}}getPartnerSDKEligibility(){return{eligible:!0}}}class Ac extends be{static walletName(){return"buy_it_now"}getWebComponentName(){return"shopify-buy-it-now-button"}getInstrumentName(){return f.BuyItNow}getWebComponentClass(){return bc}}const Cc="shopify-paypal-button[disabled]{opacity:.5;cursor:not-allowed}shopify-paypal-button div.paypal-buttons>iframe{z-index:auto!important;border-radius:0!important;box-shadow:none}",Sc='::slotted(div){height:100%}:host([page-type="cart_page"]) ::slotted(div){width:100%;min-width:100px;display:flex;justify-content:center}:host([page-type="cart_page"]) .accelerated-checkout-button{overflow:hidden;display:flex;justify-content:center}';async function $e(t){const{cartId:e,cartClient:n,instrumentName:r,abortSignal:a}=t;try{if(!e)throw new Error("[".concat(r,"] provided no cart ID when updating buyer identity"));if(!n)throw new Error("[".concat(r,"] provided invalid cart client when updating buyer identity"));return await n.updateCartBuyerIdentity(t,a)}catch(i){throw y.updateFailed(r,"updateBuyerIdentity"),i}}async function xr({cartId:t,totalAmount:e,paymentMethod:n,billingAddress:r,cartClient:a,instrumentName:i,abortSignal:o,hasSellingPlan:s,canUsePaymentMethodForFreeOrder:l}){try{if(!t)throw new Error("[".concat(i,"] provided no cart ID when updating payment"));if(!e)throw new Error("[".concat(i,"] provided no total amount when updating payment"));if(!n)throw new Error("[".concat(i,"] provided no apple pay wallet content when updating payment"));if(!a)throw new Error("[".concat(i,"] provided invalid cart client when updating payment"));const u={freePaymentMethod:{billingAddress:r}};return await a.updateCartPayment(t,{amount:{amount:e.amount,currencyCode:e.currencyCode},...Number(e.amount)<=0&&!l&&!s?u:n},i,o)}catch(u){throw y.updateFailed(i,"updatePayment"),u}}function Pt(t){if(!t)return null;const e=getComputedStyle(t).borderRadius;return e.includes("px")?parseInt(e,10):null}function vt(t){if(!t)return null;const e=getComputedStyle(t).height;return e.includes("px")?parseInt(e,10):null}const Xt=25,hn=55,Ic=["cart_not_ready","cart_throttled","payment_method_not_applicable","invalid_payment_deferred_payment_required"],Zt="PAYPAL_CALLBACK_HANDLED_ERROR";var ue;class Pc extends fe{constructor(){super(...arguments),c(this,"name",f.PayPal),c(this,"abortController",null),c(this,"cartId"),c(this,"sdkButtonsComponent"),c(this,"container",null),c(this,"resizeObserver",null),c(this,"buttonHeight"),c(this,"rendering",!1),c(this,"prevBorderRadius",0),c(this,"prevHeight",0),$(this,ue,!1)}connectedCallback(){this.resizeObserver=Re("ResizeObserver",()=>this.onResize()),this.cleanupOnFailure(this.render.bind(this),this.name)}async disconnectedCallback(){var e;this.innerHTML="",this.clearShadowRoot(),await this.teardownPayPalButton(),(e=this.resizeObserver)==null||e.disconnect()}async teardownPayPalButton(){var e;try{await((e=this.sdkButtonsComponent)==null?void 0:e.close().catch(()=>{}))}catch(n){}finally{this.sdkButtonsComponent=void 0}}async onResize(){const e=Pt(this.container),n=vt(this.container),r=e!==null&&this.prevBorderRadius!==e||n!==null&&this.prevHeight!==n;!this.rendering&&r&&(await this.teardownPayPalButton(),this.container.innerHTML="",await this.cleanupOnFailure(()=>this.renderFromPayPalSDK(),this.name))}async render(){var e;const n=this.getOrCreateShadowRoot(),r=document.createElement("div");r.className=Te.BUTTON;const a=document.createElement("style");a.innerHTML=Cc,this.appendChild(a),await this.initializeShadowStyles(n,Sc),n.appendChild(r),this.container=r,this.renderFromPayPalSDK(),(e=this.resizeObserver)==null||e.observe(this.container)}getHeight(){const e=vt(this.container);return e?e>=Xt&&e<=hn?e:e<Xt?(console.debug("[PayPalButton] Container height is less than the minimum height of the PayPal button. Using the minimum height of 25px."),Xt):(console.debug("[PayPalButton] Container height is greater than the maximum height of the PayPal button. Using the maximum height of 55px."),hn):di}async renderFromPayPalSDK(){var e,n,r;if(!this.container)throw new Error("button container not set when attempting to render");this.rendering=!0;const a=()=>this.createToken(this.apiClient);let i,o;this.requiresBillingAgreement()?o=a:i=a;const s=Pt(this.container),l=this.getHeight();this.prevHeight=l;const u={color:"gold",label:this.isCTA?"pay":"paypal",disableMaxWidth:!0,height:l,shape:"sharp"};s&&s>0&&(u.borderRadius=s,this.prevBorderRadius=s);const d=await this.i18n;this.sdkButtonsComponent=(n=(e=ya(this.walletParams.sdkUrl))==null?void 0:e.Buttons)==null?void 0:n.call(e,{fundingSource:"paypal",style:u,createOrder:i,createBillingAgreement:o,onApprove:async p=>{await this.onApprove(p)},onCancel:()=>{this.cancelPaymentSheet("Payment sheet cancelled")},onClick:this.onClick.bind(this),onError:p=>{this.onError(p,d)}});let h=!1;try{await((r=this.sdkButtonsComponent)==null?void 0:r.render(this.container)),h=!0}catch(p){y.renderFailed(this.name),y.log({body:"PayPal Button not able to render into container.",attributes:{pageType:this.pageType,error:p==null?void 0:p.toString()}})}finally{this.rendering=!1}h&&this.onRendered()}cancelPaymentSheet(e){var n;x(this,ue,!0),y.sheetCancelled(this.name),(n=this.abortController)==null||n.abort("[PayPal] ".concat(e))}async onClick(e,n){var r,a;if(this.disabled)return n.reject();x(this,ue,!1),this.abortController=Re("AbortController");const i=this.getBoundingClientRect(),o=i.left+i.width/2,s=i.top+i.height/2,l=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,clientX:o,clientY:s,screenX:window.screenX+o,screenY:window.screenY+s});this.dispatchEvent(l);const[u,d]=await Promise.all([this.dataSource,this.i18n]);try{const{decelerationReason:h,cart:p,customValidationError:m,unrecoverableError:_,abortReason:w}=await ge({element:this,instrumentName:this.name,dataSource:u});if(_)return y.sheetClicked({instrument:this.name,result:"failed"}),ke(d,_),n.reject();if(w==="out_of_stock")return y.sheetClicked({instrument:this.name,result:"failed"}),me(d),n.reject();if(m)return Ne(d,m.message),n.reject();const{hasManagedSellingPlanState:E}=this.walletParams;return E!=null&&E!==this.hasSellingPlan?(pe((r=p==null?void 0:p.checkoutUrl)!=null?r:""),n.reject()):h?(M({checkoutUrl:(a=p==null?void 0:p.checkoutUrl)!=null?a:"",instrumentName:this.name,reason:h}),n.reject()):(this.cartId=p.id,n.resolve())}catch(h){return y.sheetClicked({instrument:this.name,result:"failed"}),b.notify(h),gt(d),n.reject()}}async createToken(e){const n=await e;try{if(!this.cartId)throw new Error("cartId not found when creating token");const r=await n.paypalTokenCreate(this.cartId);return y.sheetClicked({instrument:this.name,result:"success"}),r}catch(r){throw A(this,ue)||(y.sheetClicked({instrument:this.name,result:"failed"}),b.notify(r)),new Error(Zt)}}async onApprove({orderID:e,payerID:n,billingToken:r}){var a;try{if(y.authorizationAttempt(this.name),Je(k.AuthorizationLatency,this.name),!this.cartId)throw new Error("Cart not found");const i=await this.apiClient,o=this.requiresBillingAgreement()&&r!=null?r:e,{billingAddress:s,destinationAddress:l,expiresAt:u,email:d,remoteOrderId:h}=await i.paypalBuyerDetailsFetch(o,this.cartId),p=await $e({cartId:this.cartId,cartClient:i,instrumentName:this.name,emailAddress:d,streetAddress:l});if(!(p!=null&&p.data))throw new Error("Cart not found");if(await this.decelerateOrAbort(p))return;const{id:m,totalAmount:_,checkoutUrl:w}=p.data,E=await xr({abortSignal:(a=this.abortController)==null?void 0:a.signal,cartClient:i,cartId:m,instrumentName:f.PayPal,billingAddress:s!=null?s:l,paymentMethod:{walletPaymentMethod:{paypalWalletContent:{billingAddress:s!=null?s:l,email:d,expiresAt:u,payerId:n,token:h!=null?h:e,acceptedSubscriptionTerms:!1,vaultingAgreement:!1,merchantId:this.walletParams.merchantId}}},canUsePaymentMethodForFreeOrder:!0,totalAmount:_});if(await this.decelerateOrAbort(E))return;y.authorizationComplete({instrument:this.name,result:"success",measurement:Y(k.AuthorizationLatency,this.name)}),pe(w)}catch(i){throw A(this,ue)||(y.authorizationComplete({instrument:this.name,result:"failed",measurement:Y(k.AuthorizationLatency,this.name)}),b.notify(i)),new Error(Zt)}}onError(e,n){if(A(this,ue)||e.message.includes(Zt))return;if(e.message==="Window is closed, can not determine type"){this.cancelPaymentSheet("Window Closed Error");return}if(e.message==="Detected popup close"){this.cancelPaymentSheet("Popup Closed Error");return}e.message==="RESTRICTED_WALLET_ACCOUNT"?y.log({body:"PayPal modal closed due to error: ".concat(e)}):b.notify(new vc(e));const r=n.translate("brand.paypal");N(r,n)}requiresBillingAgreement(){return this.walletParams.hasManagedSellingPlanState||this.walletParams.requiresBillingAgreement}shouldDecelerate(e){return!!(e&&Ic.includes(e))}async decelerateOrAbort(e){if(e.abortReason==="out_of_stock")return await this.handleOutOfStock(),!0;if(!e.data)return!1;const{totalAmount:n,checkoutUrl:r}=e.data;let a;return this.shouldDecelerate(e.decelerationReason)?a=e.decelerationReason:Dr({currentCartTotal:n,initialBuyerCurrency:this.buyerCurrency})&&(a="currency_changed"),a?(M({checkoutUrl:r,instrumentName:this.name,reason:a}),!0):!1}async handleOutOfStock(){y.authorizationComplete({instrument:this.name,result:"failed",measurement:Y(k.AuthorizationLatency,this.name)}),me(await this.i18n)}}ue=new WeakMap;class vc extends Error{constructor(e){e instanceof Error?(super(e.message,{cause:e.cause}),this.stack=e.stack):super(String(e)),this.name="PayPalError"}}const xe=class xn extends be{static walletName(){return"paypal"}constructor(e){super(e)}getWebComponentName(){return"shopify-paypal-button"}getInstrumentName(){return f.PayPal}getWebComponentClass(){return Pc}loadWalletSDK(){let e=xn.paypalSDKPromiseByUrl.get(this.walletParams.sdkUrl);if(e)return e;const n=document.createElement("script");return n.setAttribute("src",this.walletParams.sdkUrl),e=new Promise((r,a)=>{n.setAttribute("data-namespace",ga(this.walletParams.sdkUrl)),n.onload=()=>r(),n.onerror=i=>{xn.paypalSDKPromiseByUrl.delete(this.walletParams.sdkUrl),n&&document.body.contains(n)&&document.body.removeChild(n),a(i)},document.body.appendChild(n)}),xn.paypalSDKPromiseByUrl.set(this.walletParams.sdkUrl,e),e}getLoadEligibility(){return{eligible:!0}}getPartnerSDKEligibility(){var e,n;try{return(n=(e=ya(this.walletParams.sdkUrl))==null?void 0:e.Buttons)!=null&&n.call(e).isEligible()?{eligible:!0}:{eligible:!1,reason:"PayPal SDK not eligible"}}catch(r){throw new J({code:"paypal-sdk-eligibility-check-failed",message:"PayPal SDK 'isEligible' method failed unexpectedly: ".concat(r)})}}};c(xe,"paypalSDKPromiseByUrl",new Map);let dr=xe;const Dc=".apple-pay-button{display:flex;align-items:center;justify-content:center;width:100%;padding:0!important;cursor:pointer;border:none}.apple-pay-button:hover:not(:disabled){filter:brightness(92%)}.apple-pay-button:disabled{opacity:.5;cursor:not-allowed}.apple-pay-button svg{height:100%;flex-shrink:0}.apple-pay--content{font-size:16px;font-family:San Francisco,sans-serif;width:100%;height:100%;flex-grow:1;display:flex;align-items:center;justify-content:center;white-space:pre}.apple-pay--light{background:#fff;color:#000}.apple-pay--light svg{fill:#000}.apple-pay--dark{background:#000;color:#fff}.apple-pay--dark svg{fill:#fff}",ur={UK:"GB",JA:"JP"},Pa=["AS","GU","MP","PR","VI"];Pa.forEach(t=>{ur[t]="US"});function wt(t){var e,n;const r=t.countryCode,a={firstName:t.givenName||void 0,lastName:t.familyName||void 0,address1:(e=t==null?void 0:t.addressLines)==null?void 0:e[0],address2:((n=t==null?void 0:t.addressLines)==null?void 0:n[1])||void 0,city:t.locality||void 0,zip:t.postalCode||void 0,province:t.administrativeArea||t.subLocality||void 0,country:Rc(t.countryCode),phone:t.phoneNumber||void 0};return a.country==="HK"&&(a.zip=void 0,a.province=t.postalCode),r&&Pa.includes(r)&&(a.province=r),a.lastName||(a.lastName=a.firstName),a}function Rc(t){if(!t)return"ZZ";const e=t.toUpperCase();return Object.keys(ur).includes(e)?ur[e]:e}function Tc(t,e){return t.map(n=>{const{title:r,deliveryPromise:a}=Lc(n,e);return{title:r,estimatedCost:{amount:kc(n),currencyCode:n[0].estimatedCost.currencyCode},handle:Da(n),deliveryPromise:a}})}function Nc(t,e){return t.map(n=>{const{title:r,description:a}=Oc(n,e);return{title:r,description:a,handle:Da(n)}})}function va({deliveryGroups:t}){return t.map(e=>e.deliveryOptions.filter(({deliveryMethodType:n})=>n==="SHIPPING").map(n=>({...n,groupType:e.groupType}))).reduce((e,n)=>e.flatMap(r=>n.map(a=>[...r,a])),[[]])}function kc(t){return t.reduce((e,n)=>e+Number(n.estimatedCost.amount),0).toFixed(2)}function Lc(t,e){const n=t.find(l=>l.groupType===De.OneTimePurchase),r=t.find(l=>l.groupType===De.Subscription),a=[...new Set([n==null?void 0:n.title,r==null?void 0:r.title])].filter(Boolean),i=[...new Set([n==null?void 0:n.deliveryPromise,r==null?void 0:r.deliveryPromise])].filter(Boolean),o=a.length===2?e.translate("shipping_methods.connect_shipping_methods",{methodOne:a[0],methodTwo:a[1]}):a[0],s=i.length===2?e.translate("delivery_promises.connect_delivery_promises",{promiseOne:i[0],promiseTwo:i[1]}):i[0];return{title:o,deliveryPromise:s}}function Oc(t,e){const n=t.find(o=>o.groupType===De.OneTimePurchase),r=t.find(o=>o.groupType===De.Subscription),a=t.map(xc).filter(Boolean).join(", ");let i=(n==null?void 0:n.title)||(r==null?void 0:r.title);return n&&r&&(i=e.translate("shipping_methods.connect_shipping_methods",{methodOne:n.title,methodTwo:r.title})),{title:i,description:a}}function xc(t){const e=Number(t.estimatedCost.amount).toFixed(2),n=t.estimatedCost.currencyCode,r=t.deliveryPromise;return r?"(".concat(r,") ").concat(e," ").concat(n):"".concat(e," ").concat(n)}function Da(t){return t.map(e=>e.handle).join(",")}function Ra({deliveryGroups:t,i18n:e}){const n=t.some(r=>r.groupType===De.Subscription);return t.map(r=>{var a;let i=e.translate("order_summary.shipping");return n&&(i=r.groupType===De.Subscription?e.translate("order_summary.shipping_subscription"):e.translate("order_summary.shipping_one_time_purchase")),{label:i,amount:ve(((a=r.selectedDeliveryOption)==null?void 0:a.estimatedCost.amount)||0)}})}function Mc(t,e){return at({label:t,possibleLines:[e]})[0]}function Uc({subtotal:t,deliveryGroups:e,duties:n,taxes:r,discountAllocations:a,i18n:i,formattedRecurringTotals:o}){var s;const l=at({label:i.translate("order_summary.subtotal"),possibleLines:[t]}),u=Ra({deliveryGroups:e,i18n:i}),d=at({label:i.translate("order_summary.duties"),possibleLines:[n]}),h=at({label:i.translate("order_summary.taxes"),possibleLines:[r]}),p=o.map(w=>({label:w,amount:"0.00",type:"pending"})),m=p.length?[{label:i.translate("order_summary.subscriptions.recurring_total_tooltip_line"),amount:"0.00",type:"pending"}]:[],_=(s=a==null?void 0:a.flatMap(w=>{var E;const S=(E=w.title)!=null?E:w.code;return at({label:S!=null?S:i.translate("order_summary.discount"),possibleLines:[w.discountedAmount],isDiscount:!0})}))!=null?s:[];return[...l,...u,...d,...h,..._,...p,...m]}function at({label:t,possibleLines:e,isDiscount:n=!1}){return e.filter(r=>(r==null?void 0:r.amount)!==void 0).map(r=>({label:t,amount:n?"-".concat(ve(r.amount)):ve(r.amount)}))}function pn(t,e){if(!t.length)return[];const n=va({deliveryGroups:t});return Tc(n,e).map(r=>{var a;return{label:r.title||"",amount:r.estimatedCost.amount,identifier:r.handle,detail:(a=r.deliveryPromise)!=null?a:""}})}const er=async({paymentSheetAction:t,onProceed:e,terminateSession:n})=>{switch(t.action){case"abort":n();break;case"show_error":await e(t.errors);break;case"update":await e(t.errors);break;default:throw new Error('Missing handler for payment sheet action "'.concat(t.action,'"'))}},Bc=["decelerate","abort","show_error","complete","update"];class ze extends qn{constructor(){super(...arguments),c(this,"name","UnhandledActionError")}}class Ta{constructor(){c(this,"generatePrioritizedPaymentSheetAction",(e,n)=>{let r;const a=[],i=[],o=[],s=n.filter(u=>u!==g.UnacceptablePaymentsAmount).some(u=>e[u]!==void 0);for(const u of Object.keys(e))if(!(u===g.UnacceptablePaymentsAmount&&s)){const d=e[u]();d.effects&&i.push(...d.effects),d.action!=="complete"&&d.errors&&o.push(...d.errors),a.push(d)}const l=(u=>Bc.map(d=>u.find(h=>h.action===d)).find(d=>!!d))(a);return l&&(r=l,r.effects=i,r.action!=="complete"&&(r.errors=o)),r})}getMergedPaymentSheetAction({result:e,errorActions:n}){const r={};n.forEach(i=>{i.errors.forEach(o=>{e.errors.includes(o)&&(r[o]=i.generateAction)})});let a;for(const i of e.errors){const o=r[i];if(o){a=o();break}}return{firstPaymentSheetAction:a,prioritizedPaymentSheetAction:this.generatePrioritizedPaymentSheetAction(r,e.errors)}}getUnhandledErrors(e,n){const r=n.flatMap(a=>a.errors);return e.filter(a=>!r.includes(a))}decelerateOrAbort({reason:e,instrumentName:n,errorOrWarning:r,result:a}){var i,o;return((i=a==null?void 0:a.data)==null?void 0:i.__typename)==="SubmitFailed"?{action:"decelerate",redirectUrl:(o=a.data)==null?void 0:o.checkoutUrl,reason:e}:{action:"abort",effects:[()=>{var s;b.notify(new Error("[".concat(n,"] unexpected ").concat(r," received in ").concat((s=a==null?void 0:a.data)==null?void 0:s.__typename," response, aborting")))}]}}}class Fc extends Ta{constructor(e){super(),c(this,"completionResult"),this.i18n=e,this.completionResult=null}mapMutationResultToPaymentSheetAction(e,n){var r;const a=this.mapCustomValidationFunctionErrorToAction(e.errors);if(a)return a;const i=this.getErrorActions({shippingCountryCode:n==null?void 0:n.shippingCountryCode}),o=e.errors,s=this.getUnhandledErrors(o,i);if(s.forEach(u=>{b.notify(new ze("[".concat(f.ApplePay,"] mutation result error not handled: ").concat(u),{groupingHash:"UnhandledActionError:".concat(f.ApplePay,":").concat(u)}))}),((r=e.data)==null?void 0:r.deliveryGroups.length)===0&&n!=null&&n.shippingRequired)return{action:"show_error",errors:[new ApplePayError("addressUnserviceable")]};const{firstPaymentSheetAction:l}=this.getMergedPaymentSheetAction({result:e,errorActions:i});return l||(s.length>0?{action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]}:{action:"update",errors:[]})}mapCompletionResultToPaymentSheetAction(e,n){if(!e.data)throw new Error("[".concat(f.ApplePay,"] completion returned null result"));switch(this.completionResult=e,e.data.__typename){case"SubmitSuccess":return{action:"complete",redirectUrl:e.data.redirectUrl};case"SubmitAlreadyAccepted":case"SubmitThrottled":case"SubmitFailed":{const r=this.mapCustomValidationFunctionErrorToAction(e.errors);if(r)return r;const a=this.getErrorActions({shippingCountryCode:n==null?void 0:n.shippingCountryCode}),i=e.errors;this.getUnhandledErrors(i,a).forEach(s=>{b.notify(new ze("[".concat(f.ApplePay,"] completion result error not handled: ").concat(s),{groupingHash:"UnhandledActionError:".concat(f.ApplePay,":").concat(s)}))});const{prioritizedPaymentSheetAction:o}=this.getMergedPaymentSheetAction({result:e,errorActions:a});return o||{action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]}}default:throw new Error("[".concat(f.ApplePay,"] unknown completion result type: ").concat(e.data.__typename))}}getErrorActions({shippingCountryCode:e}){return[{errors:[g.CaptchaCompletionRequired],generateAction:()=>this.decelerateOrAbort({reason:"captcha_required",instrumentName:f.ApplePay,errorOrWarning:g.CaptchaCompletionRequired,result:this.completionResult})},{errors:[g.InvalidLanguage],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.ApplePay,"] mutation provided invalid language, aborting")))}]})},{errors:[g.InvalidCountry],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.ApplePay,"] mutation provided invalid country, aborting")))}]})},{errors:[g.MissingCartId],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.ApplePay,"] mutation provided invalid cart ID, aborting")))}]})},{errors:la,generateAction:()=>({action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]})},{errors:[g.RedirectToCheckoutRequired],generateAction:()=>({action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]})},{errors:Cr,generateAction:()=>this.decelerateOrAbort({reason:"dynamic_tax",instrumentName:f.ApplePay,errorOrWarning:g.NewTaxMustBeAccepted,result:this.completionResult})},{errors:da,generateAction:()=>this.decelerateOrAbort({reason:"not_enough_stock",instrumentName:f.ApplePay,errorOrWarning:g.MerchandiseNotEnoughStock,result:this.completionResult})},{errors:[g.MerchandiseOutOfStock],generateAction:()=>({action:"abort",effects:[()=>me(this.i18n)]})},{errors:ca,generateAction:()=>({action:"abort",effects:[()=>N(this.i18n.translate("brand.apple_pay"),this.i18n)]})},{errors:sa,generateAction:()=>({action:"show_error",errors:[new ApplePayError("addressUnserviceable")]})},{errors:[g.BuyerIdentityEmailRequired],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","emailAddress",this.i18n.translate("errors.missing.email"))]})},{errors:ji,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.missing.first_name"))]})},{errors:Yi,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.invalid.first_name"))]})},{errors:Ki,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.missing.last_name"))]})},{errors:Qi,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.invalid.last_name"))]})},{errors:Ji,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.missing.address1"))]})},{errors:[g.DeliveryAddress1Invalid],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.invalid.address1"))]})},{errors:Xi,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.missing.address2"))]})},{errors:[g.DeliveryAddress2Invalid],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.invalid.address2"))]})},{errors:Zi,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","locality",this.i18n.translate("errors.missing.city"))]})},{errors:[g.DeliveryCityInvalid],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","locality",this.i18n.translate("errors.invalid.city"))]})},{errors:[g.DeliveryZoneRequiredForCountry],generateAction:()=>({action:"show_error",errors:[this.createApplePayZoneError(this.i18n.translate("errors.missing.zone"),this.i18n.translate("errors.missing.emirate"),"shippingContactInvalid",e)]})},{errors:[g.DeliveryZoneNotFound],generateAction:()=>({action:"show_error",errors:[this.createApplePayZoneError(this.i18n.translate("errors.invalid.zone"),this.i18n.translate("errors.invalid.emirate"),"shippingContactInvalid",e)]})},{errors:eo,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","postalCode",this.i18n.translate("errors.missing.postal_code"))]})},{errors:oa,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","postalCode",this.i18n.translate("errors.invalid.postal_code"))]})},{errors:[g.DeliveryCountryRequired],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","country",this.i18n.translate("errors.missing.country"))]})},{errors:to,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","phoneNumber",this.i18n.translate("errors.missing.phone"))]})},{errors:no,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","phoneNumber",this.i18n.translate("errors.invalid.phone"))]})},{errors:ro,generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","country",this.i18n.translate("errors.invalid.country"))]})},{errors:io,generateAction:()=>({action:"show_error",errors:[new ApplePayError("billingContactInvalid")]})},{errors:[g.UnsupportedApplePayPaymentMethod],generateAction:()=>({action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)},()=>{b.notify(new ze("[".concat(f.ApplePay,"] payment method is not supported"),{groupingHash:"UnhandledActionError:".concat(f.ApplePay,":payment_method_not_supported")}))}]})},{errors:[g.PaymentsMethodRequired],generateAction:()=>({action:"show_error",errors:[new ApplePayError("unknown")]})},{errors:[g.CustomValidation],generateAction:()=>({action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]})},{errors:[g.DeliveryLastNameContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.emojis.last_name"))]})},{errors:[g.DeliveryFirstNameContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.emojis.first_name"))]})},{errors:[g.DeliveryAddress1TooLong],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.too_long.address1"))]})},{errors:[g.DeliveryAddress2TooLong],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.too_long.address2"))]})},{errors:[g.DeliveryFirstNameTooLong],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.too_long.first_name"))]})},{errors:[g.DeliveryLastNameTooLong],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.too_long.last_name"))]})},{errors:[g.DeliveryCityTooLong],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","locality",this.i18n.translate("errors.too_long.city"))]})},{errors:[g.DeliveryFirstNameContainsUrl],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.url.first_name"))]})},{errors:[g.DeliveryLastNameContainsUrl],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.url.last_name"))]})},{errors:[g.DeliveryAddress1ContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.html_tags.address1"))]})},{errors:[g.DeliveryAddress2ContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.html_tags.address2"))]})},{errors:[g.DeliveryCityContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","locality",this.i18n.translate("errors.html_tags.city"))]})},{errors:[g.DeliveryFirstNameContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.html_tags.first_name"))]})},{errors:[g.DeliveryLastNameContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","name",this.i18n.translate("errors.html_tags.last_name"))]})},{errors:[g.DeliveryCityContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","locality",this.i18n.translate("errors.emojis.city"))]})},{errors:[g.DeliveryAddress1ContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.emojis.address1"))]})},{errors:[g.DeliveryAddress2ContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","addressLines",this.i18n.translate("errors.emojis.address2"))]})},{errors:[g.DeliveryPostalCodeContainsEmojis],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","postalCode",this.i18n.translate("errors.emojis.postal_code"))]})},{errors:[g.BuyerIdentityEmailInvalid],generateAction:()=>({action:"show_error",errors:[new ApplePayError("shippingContactInvalid","emailAddress",this.i18n.translate("errors.invalid.email"))]})},{errors:[g.DiscountNotApplicable],generateAction:()=>({action:"show_error",errors:[new ApplePayError("couponCodeInvalid",void 0,this.i18n.translate("errors.invalid.discount"))]})},{errors:[g.PaymentsCountryInvalid],generateAction:()=>({action:"abort",effects:[()=>{N(this.i18n.translate("brand.apple_pay"),this.i18n)}]})}]}mapCustomValidationFunctionErrorToAction(e){const n=vr(e);return n?{action:"abort",effects:[()=>{const r=this.i18n.translate("brand.apple_pay");Rr(this.i18n.translate("error_dialogs.wallet.title",{wallet:r}),n.message)}]}:null}createApplePayZoneError(e,n,r,a){const i=a==="AE"?"subLocality":"administrativeArea";return new ApplePayError(r,i,a==="AE"?n:e)}}const mn=["name","postalAddress"];function $c({shippingRequired:t,walletParams:e,buyerCurrency:n}){const{phoneRequired:r,emailRequired:a}=e,i=[...a?["email"]:[],...r?["phone"]:[]],o=t?[...mn,...i]:i;return{countryCode:e.countryCode,currencyCode:n,merchantCapabilities:e.merchantCapabilities,supportedNetworks:e.supportedNetworks,total:{type:"pending",label:e.merchantName,amount:"1.00"},requiredBillingContactFields:mn,requiredShippingContactFields:o,shippingType:e.shippingType}}const zc=t=>{const e=t.lineItems.flatMap(n=>n.discountAllocations);return[...t.discountCodes.filter(n=>n.applicable&&t.discountAllocations.findIndex(r=>r.code===n.code)===-1&&e.findIndex(r=>r.code===n.code)===-1).map(n=>({code:n.code,discountedAmount:{amount:"0",currencyCode:t.totalAmount.currencyCode}})),...t.discountAllocations,...e]},Na=t=>{const e=t.lineItems.reduce((n,r)=>n+Number(r.subtotalAmount.amount),0);return{amount:String(e),currencyCode:t.totalAmount.currencyCode}};function Hc(t,e){return t.reduce((n,r)=>{if(!r.sellingPlanAllocation)return n;const{sellingPlanAllocation:{sellingPlan:a,priceAdjustments:i},quantity:o}=r;if(!a.recurringDeliveries||!a.billingPolicy)return n;const s=Gc({billingPolicy:a.billingPolicy,i18n:e});if(i.length===0){const l=_t({price:Number(r.subtotalAmount.amount)*o,currencyCode:r.subtotalAmount.currencyCode,locale:e.locale});return[...n,e.translate("order_summary.subscriptions.recurring_totals",{fixedPrice:l,interval:s})]}else if(i.length===1){const l=_t({price:Number(i[0].price.amount)*o,currencyCode:i[0].price.currencyCode,locale:e.locale});return[...n,e.translate("order_summary.subscriptions.recurring_totals",{fixedPrice:l,interval:s})]}else if(i.length===2){const l=_t({price:Number(i[0].price.amount)*o,currencyCode:i[0].price.currencyCode,locale:e.locale}),u=_t({price:Number(i[1].price.amount)*o,currencyCode:i[1].price.currencyCode,locale:e.locale}),d=a.priceAdjustments[0].orderCount;return[...n,e.translate("order_summary.subscriptions.recurring_totals_with_policies",{count:d,fixedPrice:l,recurringPrice:u,interval:s})]}return n},[])}function _t({price:t,currencyCode:e,locale:n}){return Intl.NumberFormat(n,{style:"currency",currency:e,currencyDisplay:"narrowSymbol"}).format(t)}function Gc({billingPolicy:t,i18n:e}){const{interval:n,intervalCount:r}=t,a={YEAR:"order_summary.subscriptions.recurring_total_intervals.year",MONTH:"order_summary.subscriptions.recurring_total_intervals.month",WEEK:"order_summary.subscriptions.recurring_total_intervals.week",DAY:"order_summary.subscriptions.recurring_total_intervals.day"}[n];if(!a)throw new Error("Unknown selling plan interval provided: ".concat(n));return e.translate(a,{count:r})}async function ka({cartId:t,cartClient:e,instrumentName:n,selectedDeliveryOptions:r,abortSignal:a}){try{if(!t)throw new Error("[".concat(n,"] provided no cart ID when updating shipping method"));if(!e)throw new Error("[".concat(n,"] provided invalid cart client when updating shipping method"));return await e.updateSelectedDeliveryOptions(t,r,n,a)}catch(i){throw y.updateFailed(n,"updateSelectedDeliveryOptions"),i}}async function La({cartId:t,token:e,cartClient:n,instrumentName:r}){try{if(!t)throw new Error("[".concat(r,"] provided no cart ID when submitting for completion"));if(!n)throw new Error("[".concat(r,"] provided invalid cart client when submitting for completion"));if(!e)throw new Error("[".concat(r,"] provided no token when submitting for completion"));return await n.submitForCompletion(t,e,r)}catch(a){throw y.updateFailed(r,"submitForCompletion"),a}}function Oa({selectedDeliveryOptionHandles:t,deliveryGroups:e,instrumentName:n}){return e.map(r=>{const a=r.deliveryOptions.filter(i=>t.includes(i.handle));if(!a.length)return{deliveryGroupId:"",deliveryOptionHandle:""};if(a.length>1)throw new qn("[".concat(n,"] found multiple delivery options with selectedDeliverOptionHandle in group: ").concat(r.id),{groupingHash:"getSelectedDeliveryOptions::MultipleOptions"});return{deliveryGroupId:r.id,deliveryOptionHandle:a[0].handle}}).filter(r=>!!r.deliveryGroupId)}async function Vc(t){const{cartClient:e,instrumentName:n}=t;try{if(!t.cartId)throw new Error("[".concat(n,"] provided no cart ID when updating billing address"));if(!e)throw new Error("[".concat(n,"] provided invalid cart client when updating billing address"));return await e.updateCartBillingAddress(t)}catch(r){throw y.updateFailed(n,"updateBillingAddress"),r}}function yn(t,e){return t.some(n=>e.includes(n))}function st({result:t,instrument:e}){var n;const r=(n=t.errors)!=null?n:[];yn(r,Cr)&&y.dynamicTaxFailed({instrument:e}),yn(r,ao)&&y.dynamicShippingFailed({instrument:e})}const gn=["cart_not_ready","cart_throttled","not_enough_stock","payment_method_not_applicable"];class qc{constructor({accessToken:e,buyerCountry:n,buyerCurrency:r,button:a,i18n:i,merchantName:o,dataSource:s,apiClient:l,walletParams:u}){c(this,"name",f.ApplePay),c(this,"accessToken"),c(this,"button"),c(this,"buyerCountry"),c(this,"cart"),c(this,"apiClient"),c(this,"dataSource"),c(this,"i18n"),c(this,"merchantName"),c(this,"session"),c(this,"paymentSheetActionGenerator"),c(this,"cancelled"),c(this,"buyerCurrency"),c(this,"customerAccountEmail"),c(this,"abortController",null),c(this,"isPersonalDataCleared",!1),c(this,"isSheetLoaded",!1),c(this,"onvalidatemerchant",async d=>{var h,p,m;try{const _=new URL(d.validationURL),w=this.getMerchantSession(_),{cart:E,decelerationReason:S,customValidationError:z,unrecoverableError:O}=await ge({element:this.button,instrumentName:this.name,dataSource:this.dataSource});if(O){y.sheetClicked({instrument:this.name,result:"failed"}),this.session.abort(),(h=this.abortController)==null||h.abort("[Apple Pay] Unrecoverable error ".concat(O)),ke(this.i18n,O);return}if(z){this.session.abort(),(p=this.abortController)==null||p.abort("[Apple Pay] Custom validation error"),Ne(this.i18n,z.message);return}this.cart=E;const oe=await w;if(b.leaveBreadcrumb("ApplePay Session completeMerchantValidation",{session:oe},"log"),S&&gn.includes(S)){await this.terminateSession(),M({checkoutUrl:(m=E==null?void 0:E.checkoutUrl)!=null?m:"",instrumentName:this.name,reason:S});return}if(this.sessionIsCancelled())return;this.session.completeMerchantValidation(oe)}catch(_){y.sheetClicked({instrument:f.ApplePay,result:"failed"}),this.terminateSession(_)}}),c(this,"onshippingcontactselected",async d=>{try{const h=wt(d.shippingContact),p=await $e({cartId:this.cart.id,streetAddress:h,cartClient:this.apiClient,instrumentName:this.name});if(!p.data)throw new Error("Missing cart during Apple Pay Session creation");if(await this.decelerateOrAbort(p))return;await this.applyPaymentSheetActions(p,m=>this.completeShippingContactSelection(p.data,m),{shippingRequired:this.isShippingRequired,shippingCountryCode:h.country})}catch(h){this.terminateSession(h)}}),c(this,"onshippingmethodselected",async d=>{var h;try{const p=d.shippingMethod.identifier.split(","),m=Oa({selectedDeliveryOptionHandles:p,deliveryGroups:this.cart.deliveryGroups,instrumentName:this.name});if(m.length===0){this.handleShippingMethodNotFoundInCart(p);return}const _=await ka({cartId:this.cart.id,cartClient:this.apiClient,instrumentName:this.name,selectedDeliveryOptions:m,abortSignal:(h=this.abortController)==null?void 0:h.signal});if(await this.decelerateOrAbort(_))return;await this.applyPaymentSheetActions(_,()=>this.completeShippingMethodSelection(_.data),{shippingRequired:this.isShippingRequired,shippingCountryCode:void 0}),this.onSheetLoaded()}catch(p){this.terminateSession(p)}}),c(this,"onpaymentmethodselected",async d=>{try{const{billingContact:h}=d.paymentMethod;if(!this.isShippingRequired&&h){const _=wt(h),w=await $e({cartId:this.cart.id,countryCode:_.country,cartClient:this.apiClient,instrumentName:this.name});if(!w.data)throw new Error("Missing cart during Apple Pay Session creation");if(await this.decelerateOrAbort(w))return;await this.applyPaymentSheetActions(w,()=>this.cart=w.data,{shippingRequired:this.isShippingRequired,shippingCountryCode:_.country});const E=await Vc({billingAddress:_,cartId:this.cart.id,cartClient:this.apiClient,instrumentName:this.name});await this.applyPaymentSheetActions(E,()=>this.cart=E.data,{shippingRequired:this.isShippingRequired,shippingCountryCode:_.country})}const{newTotal:p,newLineItems:m}=this.getNewTotalAndLineItems(this.cart);if(b.leaveBreadcrumb("ApplePay Session completePaymentMethodSelection",{newTotal:p,newLineItems:m},"log"),this.sessionIsCancelled())return;this.session.completePaymentMethodSelection({newTotal:p,newLineItems:m}),this.isShippingRequired||this.onSheetLoaded()}catch(h){this.terminateSession(h)}}),c(this,"oncancel",()=>{var d;this.cancelled=!0,this.removePersonalData(),Y(k.SheetLoad,this.name),y.sheetCancelled(this.name),this.button.disabled=!1,(d=this.abortController)==null||d.abort("[Apple Pay] Payment sheet cancelled")}),c(this,"onpaymentauthorized",async d=>{y.authorizationAttempt(this.name),Je(k.AuthorizationLatency,this.name);const h=R=>{y.authorizationComplete({instrument:this.name,measurement:Y(k.AuthorizationLatency,this.name),result:"failed"}),this.terminateSession(R)},p=async R=>{var B,F;switch((B=R.effects)==null||B.forEach(L=>L()),R.action){case"complete":{const L=ApplePaySession.STATUS_SUCCESS;try{await this.completePayment(L),pe(R.redirectUrl)}catch(ie){h(ie)}break}case"show_error":{const L=(F=R.errors)!=null?F:[];try{await this.completePayment(ApplePaySession.STATUS_FAILURE,L)}catch(ie){h(ie)}break}case"abort":{try{this.abortPaymentSheetAndShowError(),this.removePersonalData()}catch(L){h(L)}break}case"decelerate":{try{await this.completePayment(ApplePaySession.STATUS_FAILURE),await this.removePersonalData(),M({checkoutUrl:R.redirectUrl,instrumentName:this.name,reason:R.reason})}catch(L){h(L)}break}default:h(new Error("[".concat(this.name,"] completion action not handled: ").concat(R.action)))}},{token:m,billingContact:_,shippingContact:w}=d.payment;let E=null,S=null;try{if(!_){await this.completePayment(ApplePaySession.STATUS_FAILURE);return}if(E=wt({..._,phoneNumber:w==null?void 0:w.phoneNumber}),this.isShippingRequired){if(!w){await this.completePayment(ApplePaySession.STATUS_FAILURE);return}S=wt(w)}}catch(R){h(R);return}const z=async()=>{var R;try{const B=this.isShippingRequired?{validateAddress:!0,streetAddress:S}:{},F=(R=this.customerAccountEmail)!=null?R:w==null?void 0:w.emailAddress,L=await $e({cartId:this.cart.id,emailAddress:F,cartClient:this.apiClient,instrumentName:this.name,...B});if(st({result:L,instrument:this.name}),await this.decelerateOrAbort(L))return;const ie=this.paymentSheetActionGenerator.mapMutationResultToPaymentSheetAction(L,{shippingRequired:this.isShippingRequired,shippingCountryCode:S==null?void 0:S.country});await er({paymentSheetAction:ie,onProceed:async se=>{se&&se.length>0?await this.completePayment(ApplePaySession.STATUS_FAILURE,se):await O()},terminateSession:h})}catch(B){h(B)}},O=async()=>{var R;try{const B=this.cart.lineItems.some(ie=>!!ie.sellingPlanAllocation),F=await xr({cartId:this.cart.id,totalAmount:this.cart.totalAmount,paymentMethod:{walletPaymentMethod:{applePayWalletContent:{billingAddress:E,data:m.paymentData.data,header:m.paymentData.header,lastDigits:m.paymentMethod.displayName.split(" ")[1],signature:m.paymentData.signature,version:m.paymentData.version}}},canUsePaymentMethodForFreeOrder:!1,billingAddress:E,cartClient:this.apiClient,instrumentName:this.name,hasSellingPlan:B,abortSignal:(R=this.abortController)==null?void 0:R.signal});if(st({result:F,instrument:this.name}),await this.decelerateOrAbort(F))return;const L=this.paymentSheetActionGenerator.mapMutationResultToPaymentSheetAction(F,{shippingRequired:this.isShippingRequired,shippingCountryCode:S==null?void 0:S.country});await er({paymentSheetAction:L,onProceed:async ie=>{ie&&ie.length>0?await this.completePayment(ApplePaySession.STATUS_FAILURE,ie):await oe()},terminateSession:h})}catch(B){h(B)}},oe=async()=>{try{const R=await La({cartId:this.cart.id,token:m.transactionIdentifier,cartClient:this.apiClient,instrumentName:this.name});st({result:R,instrument:this.name});const B=this.paymentSheetActionGenerator.mapCompletionResultToPaymentSheetAction(R,{shippingRequired:this.isShippingRequired,shippingCountryCode:S==null?void 0:S.country});await p(B)}catch(R){h(R)}};await z()}),c(this,"terminateSession",async d=>{var h;try{this.button.disabled=!1,Y(k.SheetLoad,this.name),d&&b.notify(d),y.sheetFailed(this.name,d);try{b.leaveBreadcrumb("ApplePay Session abort",{},"log"),this.session.abort(),(h=this.abortController)==null||h.abort("[Apple Pay] Payment sheet terminated")}catch(p){console.debug("[DEBUG] InvalidAccessError occurred during Apple Pay terminateSession")}if(d){const p=this.i18n.translate("brand.apple_pay");N(p,this.i18n)}}finally{await this.removePersonalData()}}),c(this,"getMostRecentNonNullCart",(d,h)=>d?(this.cart=d,d):h),c(this,"handleNullCart",d=>{if(!d)throw new Error("[".concat(this.name,"] no cart returned on mutation, aborting"))}),c(this,"completeShippingContactSelection",(d,h)=>{try{const p=this.getMostRecentNonNullCart(d,this.cart),{newTotal:m,newLineItems:_}=this.getNewTotalAndLineItems(p),w=pn(p.deliveryGroups,this.i18n);b.leaveBreadcrumb("ApplePay Session completeShippingContactSelection",{errors:h,newTotal:m,newLineItems:_,newShippingMethods:w},"log"),this.session.completeShippingContactSelection({errors:h!=null?h:[],newTotal:m,newLineItems:_,newShippingMethods:w})}catch(p){this.terminateSession(p)}}),c(this,"completeShippingMethodSelection",d=>{try{this.handleNullCart(d),this.cart=d;const{newTotal:h,newLineItems:p}=this.getNewTotalAndLineItems(d);b.leaveBreadcrumb("ApplePay Session completeShippingContactSelection",{newTotal:h,newLineItems:p},"log"),this.session.completeShippingMethodSelection({newTotal:h,newLineItems:p})}catch(h){this.terminateSession(h)}}),c(this,"sessionIsCancelled",()=>this.cancelled),this.accessToken=e,this.buyerCountry=n,this.button=a,this.cart=null,this.apiClient=l,this.dataSource=s,this.i18n=i,this.merchantName=o,this.cancelled=!1,this.buyerCurrency=r,this.customerAccountEmail=u.customerAccountEmail,this.session=new ApplePaySession(Mn,$c({shippingRequired:this.isShippingRequired,walletParams:u,buyerCurrency:r})),this.session.onvalidatemerchant=this.onvalidatemerchant,this.session.onshippingcontactselected=this.onshippingcontactselected,this.session.onshippingmethodselected=this.onshippingmethodselected,this.session.onpaymentmethodselected=this.onpaymentmethodselected,this.session.oncancel=this.oncancel,this.session.onpaymentauthorized=this.onpaymentauthorized,this.paymentSheetActionGenerator=new Fc(i)}begin(){b.leaveBreadcrumb("ApplePay Session begin",{},"log"),this.abortController=Re("AbortController"),this.session.begin()}handleShippingMethodNotFoundInCart(e){var n,r;const a=pn((r=(n=this.cart)==null?void 0:n.deliveryGroups)!=null?r:[],this.i18n);b.leaveBreadcrumb("ApplePay Session selected shipping method(s) not found in current cart, updating payment sheet",{attemptedDeliveryOptionHandles:e,currentShippingMethods:a},"log"),this.completeShippingMethodSelection(this.cart)}async applyPaymentSheetActions(e,n,r){var a;const i=this.paymentSheetActionGenerator.mapMutationResultToPaymentSheetAction(e,r);(a=i.effects)==null||a.forEach(o=>o()),!this.sessionIsCancelled()&&await er({paymentSheetAction:i,onProceed:n,terminateSession:this.terminateSession})}async getMerchantSession(e){const n=await new Bt({accessToken:this.accessToken,country:this.buyerCountry,locale:this.i18n.locale,apiVersion:"unstable"}).applePaySessionCreate(e);return JSON.parse(n)}abortPaymentSheetAndShowError(){b.leaveBreadcrumb("ApplePay Session abort",{},"log"),this.session.abort(),this.button.disabled=!1,this.emitPaymentCompleteTelemetry(ApplePaySession.STATUS_FAILURE)}abortPaymentSheetAndShowOutOfStockModal(){b.leaveBreadcrumb("ApplePay Session abort",{},"log"),this.session.abort(),this.button.disabled=!1,me(this.i18n),this.emitPaymentCompleteTelemetry(ApplePaySession.STATUS_FAILURE)}emitPaymentCompleteTelemetry(e){y.authorizationComplete({instrument:this.name,measurement:Y(k.AuthorizationLatency,this.name),result:e===ApplePaySession.STATUS_SUCCESS?"success":"failed"})}async completePayment(e,n=void 0){b.leaveBreadcrumb("ApplePay Session completePayment",{status:e,errors:n},"log"),this.session.completePayment({status:e,errors:n}),this.emitPaymentCompleteTelemetry(e)}getNewTotalAndLineItems(e){return{newTotal:Mc(this.merchantName,e.totalAmount),newLineItems:Uc({subtotal:Na(e),discountAllocations:zc(e),deliveryGroups:e.deliveryGroups,duties:e.totalDutyAmount,taxes:e.totalTaxAmount,i18n:this.i18n,formattedRecurringTotals:Hc(this.cart.lineItems,this.i18n)})}}async decelerateOrAbort(e){return e.abortReason==="out_of_stock"?(this.abortPaymentSheetAndShowOutOfStockModal(),!0):this.decelerateIfNeeded(e)}async decelerateIfNeeded(e){if(!e.data)return!1;const{checkoutUrl:n,totalAmount:r}=e.data;let a;if(e.decelerationReason&&gn.includes(e.decelerationReason)?a=e.decelerationReason:Dr({currentCartTotal:r,initialBuyerCurrency:this.buyerCurrency})&&(a="currency_changed"),a){try{await this.terminateSession()}finally{M({checkoutUrl:n,instrumentName:this.name,reason:a})}return!0}return!1}get isShippingRequired(){return this.button.isShippingRequired}async removePersonalData(){this.isPersonalDataCleared||!this.cart||(await this.apiClient.cartRemovePersonalData({cartId:this.cart.id,instrumentName:this.name}),this.isPersonalDataCleared=!0)}onSheetLoaded(){if(this.isSheetLoaded)return;this.isSheetLoaded=!0;const e=Y(k.SheetLoad,this.name);e&&(H({event:U.SheetLoaded,ttl:e}),y.sheetLoaded({instrument:this.name,measurement:e}))}}function Mr({translate:t,logoElement:e,setAriaHidden:n}){const r=document.createElement("div");return t("buy_with_button_content",{wallet:"LOGO_PLACEHOLDER"}).split(new RegExp("(LOGO_PLACEHOLDER)")).filter(Boolean).forEach(a=>{let i;a==="LOGO_PLACEHOLDER"?i=e:(i=document.createElement("span"),i.innerText=a,n&&i.setAttribute("aria-hidden","true")),r.appendChild(i)}),r}const hr=new Map;function Ke({instrumentName:t,logoType:e="LIGHT",logoContent:n}){const r=hr.get(t)||{};r[e]||(r[e]=n,hr.set(t,r))}function dt({instrumentName:t,logoType:e="LIGHT"}){var n;const r=(n=hr.get(t))==null?void 0:n[e];if(!r)throw new Error("".concat(t," ").concat(e," logo not loaded"));return r}class Wc extends fe{constructor(){super(...arguments),c(this,"name",f.ApplePay),c(this,"button",null)}static get observedAttributes(){return["disabled"]}get merchantName(){return this.walletParams.merchantName}connectedCallback(){this.cleanupOnFailure(this.render.bind(this),this.name)}attributeChangedCallback(e,n,r){super.attributeChangedCallback(e,n,r,this.button)}async handleClick(e){if(e.preventDefault(),this.disabled||!this.button||this.button.hasAttribute("aria-disabled"))return;this.disabled=!0;const[n,r,a]=await Promise.all([this.i18n,this.dataSource,this.apiClient]);Je(k.SheetLoad,this.name);let i;try{if(i=new qc({accessToken:this.accessToken,buyerCountry:this.buyerCountry,buyerCurrency:this.buyerCurrency,button:this,i18n:n,merchantName:this.merchantName,dataSource:r,apiClient:a,walletParams:this.walletParams}),!i)throw new Error("missing ApplePaySession");i.begin(),y.sheetClicked({instrument:this.name,result:"success"})}catch(o){b.notify(o),y.sheetClicked({instrument:this.name,result:"failed"}),i==null||i.terminateSession()}}async render(){var e;const n=this.getOrCreateShadowRoot(),{translate:r}=await this.i18n,a=dt({instrumentName:f.ApplePay}),i=new DOMParser().parseFromString(a,"image/svg+xml").documentElement;if(!this.button){const o=this.isCTA?r("buy_with_button_content",{wallet:r("brand.apple_pay")}):r("brand.apple_pay");if(this.button=$t({label:o}),this.button.onclick=s=>this.handleClick(s),this.button.classList.add("apple-pay-button",ci[this.buttonTheme],Te.BUTTON),this.isCTA){const s=Mr({translate:r,logoElement:i,setAriaHidden:!0});s.classList.add("apple-pay--content"),this.button.appendChild(s)}else this.button.appendChild(i);this.initializeShadowStyles(n,Dc),n.appendChild(this.button),this.ensureLightDOMIsNotEmpty(),(e=this.onRendered)==null||e.call(this)}this.disabled&&(this.button.setAttribute("disabled",""),this.button.setAttribute("aria-disabled","true"))}}function Ur(t){return function(e,n,r){const a=r.value;return r.value=function(...i){const o=a.apply(this,i);return o.eligible||console.debug("[DEBUG] ".concat(t," could not be rendered. Reason: ").concat(o.reason)),o},r}}var jc=Object.defineProperty,Yc=Object.getOwnPropertyDescriptor,xa=(t,e,n,r)=>{for(var a=Yc(e,n),i=t.length-1,o;i>=0;i--)(o=t[i])&&(a=o(e,n,a)||a);return a&&jc(e,n,a),a};class Br extends be{constructor(e){super(e),c(this,"companyFieldRequired"),this.companyFieldRequired=!!this.walletParams.companyRequired}static walletName(){return"apple_pay"}getWebComponentName(){return"shopify-apple-pay-button"}getWebComponentClass(){return Wc}getInstrumentName(){return f.ApplePay}applePayInIframe(){return typeof window>"u"||window.self!==window.top}getLoadEligibility(){return this.applePayInIframe()?{eligible:!1,reason:"cannot be loaded in an iframe"}:{eligible:!0}}getPartnerSDKEligibility(){var e,n,r,a;if(typeof window>"u"||window.ApplePaySession===void 0)return{eligible:!1,reason:"window or ApplePaySession is undefined"};try{if(!((n=(e=window.ApplePaySession).supportsVersion)!=null&&n.call(e,Mn)))return{eligible:!1,reason:"SDK does not meet minimum version requirement"};if(!((a=(r=window.ApplePaySession).canMakePayments)!=null&&a.call(r)))return{eligible:!1,reason:"failed SDK eligibility check"}}catch(i){return{eligible:!1,reason:"error in SDK eligibility checks"}}return{eligible:!0}}async loadWalletSDK(){await this.loadAppleLogo()}async loadAppleLogo(){const e=await import("./apple-pay-logo-BZB6IYWC.js");if(e)Ke({instrumentName:f.ApplePay,logoContent:e.default});else throw new Error("Apple Pay logo is empty")}}xa([Ur("Apple Pay")],Br.prototype,"getLoadEligibility"),xa([Ur("Apple Pay")],Br.prototype,"getPartnerSDKEligibility");const Kc=".buy-with-prime-button{width:100%;height:100%;padding:12px 10px 8px;display:flex;align-items:center;justify-content:center;gap:5px;opacity:1;border-width:0px}.buy-with-prime-button:hover:enabled{background-color:#0684eb!important}.buy-with-prime-button:focus{box-shadow:0 0 0 2px #1a98ff,inset 0 0 0 2px rgb(var(--color-background))!important;outline:none!important}.buy-with-prime-button:active,.buy-with-prime-button:hover:enabled:active{background-color:#0066cd!important;box-shadow:none!important}.buy-with-prime-button>svg{min-width:85px}";class Dt extends Error{constructor(){super(...arguments),c(this,"name","MissingAccessTokenError")}}class Fr extends Error{constructor(){super(...arguments),c(this,"name","FetchingWalletsPlatformConfigError")}}class $r extends Error{constructor(){super(...arguments),c(this,"name","EmptyLineItemsWalletsPlatformConfigError")}}async function zr({configOptionsEndpoint:t,checkoutUrl:e,sourceId:n,accessToken:r,instrument:a}){try{const i=JSON.stringify({checkout_url:e,checkout_cancel_url:window.location.href,cart_id:n}),o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json","X-Shopify-Storefront-Access-Token":r},body:i});if(o.ok)return o.json();const s=await o.json();throw s.error?new Error(s.error):new Error("Could not fetch wallets platform configs: [".concat(o.status,"] ").concat(o.statusText))}catch(i){const{message:o}=i!=null?i:{};switch(o){case"empty_line_items":throw new $r("[Fetch Pay Config][".concat(a,"] No eligible line items provided in the config request."));default:throw new Fr("[Fetch Pay Config][".concat(a,"] ").concat(o))}}}const Qc={[P.Checkout]:"Checkout",[P.CartAjax]:"Cart",[P.CartPage]:"Cart",[P.ProductPage]:"Product",[P.Unknown]:"Product"},Me=class wu extends fe{constructor(){super(...arguments),c(this,"name",f.BuyWithPrime),c(this,"button",null),c(this,"apiClientId","")}connectedCallback(){this.cleanupOnFailure(this.render.bind(this),this.name),this.handleOnSendCheckoutAction=this.handleOnSendCheckoutAction.bind(this)}async triggerCheckout(){this.handleClick({showDisabledStyles:!1})}async createButton(){const{translate:e}=await this.i18n,n=$t({label:e("brand.buy_with_prime")});return n.type="button",n.className="buy-with-prime-button",this.applyEnabledStyles(n),n}applyEnabledStyles(e){const n=dt({instrumentName:f.BuyWithPrime});e.innerHTML=n,e.disabled=!1,dn(e,{"background-color":"#1A98FF",color:"#FFFFFF",cursor:"pointer"})}applyDisabledStyles(e){const n=dt({instrumentName:f.BuyWithPrime,logoType:"DARK"});e.innerHTML=n,e.disabled=!0,dn(e,{"background-color":"#C0E3FF",color:"#7292AC",cursor:"not-allowed"})}async handleClick(e={showDisabledStyles:!0}){var n,r,a;if(!this.button)return;const[i,o]=await Promise.all([this.i18n,this.dataSource]);let s=[];try{e.showDisabledStyles&&this.applyDisabledStyles(this.button);let l=this.walletParams;if(this.apiClientId=this.apiClientId||l.appId||"",!l.createCheckoutSessionConfig){const{cart:d,decelerationReason:h,customValidationError:p,unrecoverableError:m}=await ge({element:this,instrumentName:this.name,dataSource:o});if(m){y.sheetClicked({instrument:this.name,result:"failed",webPixelMetaData:this.apiClientId?{apiClientId:this.apiClientId,pageType:this.pageType}:void 0}),ke(i,m);return}if(p){Ne(i,p.message);return}if(h){M({checkoutUrl:(n=d==null?void 0:d.checkoutUrl)!=null?n:"",instrumentName:this.name,reason:h});return}if(!this.accessToken)throw new Dt("[Fetch Pay Config] could not fetch pay configs since required accessToken is missing.");l={...await zr({configOptionsEndpoint:wu.OPTIMUS_CONFIG_REST_API_URL,checkoutUrl:(r=d==null?void 0:d.checkoutUrl)!=null?r:"",sourceId:(a=Pr(d))!=null?a:"",accessToken:this.accessToken,instrument:this.name})}}l.placement=this.getPlacement(),s=this.extractSKUs(l),delete l.checkoutSupportsSplitCart,delete l.appId;const u=l;this.pciEnabled&&(u.onSendCheckoutAction=this.handleOnSendCheckoutAction),window.amazon.buywithprime.initCheckout({amazonPayOptions:u}),y.sheetClicked({instrument:this.name,result:"success",webPixelMetaData:this.apiClientId?{apiClientId:this.apiClientId,skus:s,pageType:this.pageType}:void 0})}catch(l){wu.EXPECTED_ERRORS.some(h=>l instanceof h)||b.notify(l),y.sheetClicked({instrument:this.name,result:"failed",webPixelMetaData:this.apiClientId?{apiClientId:this.apiClientId,skus:s,pageType:this.pageType}:void 0});const{name:u}=l!=null?l:{name:"unknown"},{translate:d}=i;yt(d("error_dialogs.wallet.title",{wallet:d("brand.buy_with_prime")}),this.getModalContent(u,d))}finally{this.applyEnabledStyles(this.button)}}getModalContent(e,n){return e==="EmptyLineItemsWalletsPlatformConfigError"?n("error_dialogs.wallet.eligibility_error",{wallet:n("brand.buy_with_prime")}):n("error_dialogs.wallet.generic_error",{wallet:n("brand.buy_with_prime")})}extractSKUs(e){var n;return(n=e.createCheckoutSessionConfig)!=null&&n.payloadJSON?JSON.parse(e.createCheckoutSessionConfig.payloadJSON).cartDetails.lineItems.map(r=>r&&r.sku).filter(r=>r):[]}getPlacement(){return this.pageType&&Qc[this.pageType]||"Other"}async render(){var e;const n=this.getOrCreateShadowRoot();this.button||(this.button=await this.createButton(),this.button.onclick=()=>this.handleClick(),this.button.classList.add("buy-with-prime-button"),this.getPlacement().includes("Checkout")||this.button.classList.add(Te.BUTTON),await this.initializeShadowStyles(n,Kc),n.appendChild(this.button),(e=this.onRendered)==null||e.call(this))}buildBwpEventData(e){const n={};return e.createCheckoutSessionConfig&&(n.skus=this.extractSKUs(e),n.apiClientId=this.apiClientId),n}async handleOnSendCheckoutAction(e){const{eventName:n,redirect:r,redirectUrl:a,requestPayload:i}=e;if(r&&a){this.dispatchWalletEvent({eventName:"wallet-redirect",detail:{redirectUrl:a,requestPayload:i}});return}switch(n){case"initCheckout":{const o=this.walletParams;this.dispatchWalletEvent({eventName:"wallet-clicked",detail:{bwpEventData:this.buildBwpEventData(o)}});break}case"initChange":this.dispatchWalletEvent({eventName:"wallet-clicked"});break;case"cancelCheckout":this.dispatchWalletEvent({eventName:"wallet-cancel"});break}}};c(Me,"OPTIMUS_CONFIG_REST_API_URL","".concat(window.location.origin,"/wallets/config/optimus")),c(Me,"EXPECTED_ERRORS",[Fr,Dt,$r]);let Rt=Me;async function Ma({fn:t,maxRetries:e=3,delay:n=1e3,retryPolicyFn:r=()=>!0,onFinishedFn:a=()=>{}}){const i=()=>new Promise(l=>{setTimeout(l,n)});let o=0;const s=async()=>{try{return await t()}catch(l){if(o++,o<e&&r(l))return await i(),s();throw l}};try{return await s()}finally{a(o)}}async function Jc(t){return new Promise((e,n)=>{if(document.querySelector('script[src="'.concat(t,'"]'))&&window.amazon){e();return}const r=document.createElement("script");r.src=t,r.onload=()=>{window.amazon?e():n(new Error("Amazon SDK not present after loading"))},r.onerror=a=>{const i=a instanceof Event?a.type:String(a);n(new re("Error loading Amazon SDK: ".concat(i)))},document.head.append(r)})}async function Ua({sdkUrl:t=_r,maxRetries:e=3}){return Ma({fn:()=>Jc(t),maxRetries:e,delay:200,retryPolicyFn:()=>!0,onFinishedFn:n=>{y.amazonPaySDKLoadRetries({retries:n})}})}const Ue=class vu extends be{static walletName(){return"buy_with_prime"}constructor(e){super(e)}getWebComponentName(){return"shopify-buy-with-prime-button"}getInstrumentName(){return f.BuyWithPrime}getWebComponentClass(){return Rt}async loadWalletSDK(){await Ua({sdkUrl:vu.SDK_URL,maxRetries:vu.MAX_RETRIES}),await this.loadLogos()}async loadLogos(){await Promise.all([this.loadPrimeLogo(),this.loadPrimeLogoDark()])}async loadPrimeLogo(){const e=await import("./prime-logo-DoVHKBSO.js");if(e)Ke({instrumentName:f.BuyWithPrime,logoType:"LIGHT",logoContent:e.default});else throw new Error("Buy with Prime logo is empty")}async loadPrimeLogoDark(){const e=await import("./prime-logo-dark-DJIA6J0w.js");if(e)Ke({instrumentName:f.BuyWithPrime,logoType:"DARK",logoContent:e.default});else throw new Error("Buy with Prime logo dark is empty")}};c(Ue,"MAX_RETRIES",3),c(Ue,"SDK_URL",_r);let pr=Ue;const Xc="shopify-amazon-pay-button div[role=button][slot=amazon-pay-slot][data-testid=amazon-pay-button]{border-radius:0!important;width:100%!important}",Zc=':host([page-type="cart_page"]) .accelerated-checkout-button{overflow:hidden;display:flex;justify-content:center}',fn="amazon-pay-slot";var ut,ht,Ve,W,ee,Ie;const Be=class _u extends fe{constructor(){super(...arguments),c(this,"name",f.AmazonPay),c(this,"resizeObserver",null),$(this,ut,0),$(this,ht,0),$(this,Ve,!1),$(this,W,null),$(this,ee,null),$(this,Ie,null),c(this,"sdkButton",null)}connectedCallback(){this.resizeObserver=Re("ResizeObserver",()=>this.onResize()),this.cleanupOnFailure(this.render.bind(this),this.name)}async disconnectedCallback(){var e;this.innerHTML="",this.clearShadowRoot(),this.teardownAmazonButtonContainer(),(e=this.resizeObserver)==null||e.disconnect()}async triggerCheckout(){if(!this.sdkButton)throw y.log({body:"Attempted to trigger checkout on uninitialized Amazon Pay SDK button",attributes:{pageType:this.pageType,containerID:A(this,ee)}}),new Error("Attempted to trigger checkout on uninitialized Amazon Pay SDK button");this.handleClick(this.sdkButton)}async handleClick(e){var n,r;const[a,i]=await Promise.all([this.i18n,this.dataSource]);try{const{cart:o,decelerationReason:s,customValidationError:l,unrecoverableError:u}=await ge({element:this,instrumentName:this.name,dataSource:i});if(u){y.sheetClicked({instrument:this.name,result:"failed"}),ke(a,u);return}if(l){Ne(a,l.message);return}if(s){M({checkoutUrl:(n=o==null?void 0:o.checkoutUrl)!=null?n:"",instrumentName:this.name,reason:s});return}const{totalAmount:{amount:d},checkoutUrl:h}=o;let p=this.walletParams;if(!p.createCheckoutSessionConfig){if(!this.accessToken)throw new Dt("[Fetch Pay Config] could not fetch pay configs since required accessToken is missing.");p={...await zr({configOptionsEndpoint:_u.CV2_CONFIG_REST_API_URL,checkoutUrl:h,sourceId:(r=Pr(o))!=null?r:"",accessToken:this.accessToken,instrument:this.name}),placement:this.pageType===P.ProductPage?"Product":"Cart"}}delete p.appId,e.initCheckout({...p,...Number(d)!==0&&{estimatedOrderAmount:{amount:d,currencyCode:this.buyerCurrency}}}),y.sheetClicked({instrument:this.name,result:"success"})}catch(o){_u.EXPECTED_ERRORS.some(l=>o instanceof l)||b.notify(o),y.sheetClicked({instrument:this.name,result:"failed"});const s=a.translate("brand.amazon_pay");N(s,a)}}createContainer({containerID:e,height:n}){const r=document.createElement("div");return r.id=e,r.style.display="flex",r.style.minWidth="140px",n!=null&&(r.style.height="".concat(n,"px")),r.slot=fn,r.setAttribute("data-testid","amazon-pay-button"),r}async onResize(){const e=Pt(A(this,W))||0,n=vt(A(this,W))||0,r=A(this,ut)!==e||A(this,ht)!==n;!A(this,Ve)&&r&&(this.teardownAmazonButtonContainer(),await this.cleanupOnFailure(()=>this.renderFromAmazonSDK(),this.name))}async render(){var e;const n=this.getOrCreateShadowRoot();try{const r=document.createElement("slot");r.name=fn;const a=document.createElement("style");a.textContent=Xc,this.appendChild(a),await this.initializeShadowStyles(n,Zc);const i=await this.containerInstanceNumber;x(this,ee,"AcceleratedCheckoutAmazonPayButtonContainer".concat(this.pageType).concat(i)),x(this,W,document.createElement("div")),A(this,W).classList.add(Te.BUTTON),A(this,W).appendChild(r),n.appendChild(A(this,W)),(e=this.resizeObserver)==null||e.observe(A(this,W)),await this.renderFromAmazonSDK()}catch(r){b.notify(r)}}async renderFromAmazonSDK(){var e;x(this,Ve,!0);const n=vt(A(this,W))||0;x(this,Ie,this.createContainer({containerID:A(this,ee),height:n})),this.appendChild(A(this,Ie));const r=Pt(A(this,W))||0,a={buttonBorderRadius:"".concat(r,"px")};let i=!1;try{if(this.sdkButton=window.amazon.Pay.renderButton("#".concat(A(this,ee)),{...this.walletParams,style:a,placement:this.pageType===P.ProductPage?"Product":"Cart"}),this.sdkButton){const o=this.sdkButton;this.sdkButton.onClick(()=>this.handleClick(o))}else y.log({body:"Amazon Pay Button rendered null SDK button",attributes:{pageType:this.pageType,containerID:A(this,ee)}});x(this,ht,n),x(this,ut,r),y.renderSucceeded(this.name),i=!0}catch(o){y.renderFailed(this.name);const s="Amazon Pay Button not able to render into container #".concat(A(this,ee),".");console.error(s),y.log({body:s,attributes:{pageType:this.pageType,containerID:A(this,ee),error:o==null?void 0:o.toString()}})}finally{x(this,Ve,!1)}i&&((e=this.onRendered)==null||e.call(this))}teardownAmazonButtonContainer(){var e;(e=A(this,Ie))==null||e.remove(),x(this,Ie,null)}};ut=new WeakMap,ht=new WeakMap,Ve=new WeakMap,W=new WeakMap,ee=new WeakMap,Ie=new WeakMap,c(Be,"CV2_CONFIG_REST_API_URL","".concat(window.location.origin,"/wallets/config/amazon_pay")),c(Be,"EXPECTED_ERRORS",[Fr,Dt,$r]);let Tt=Be;const Fe=class Cu extends be{static walletName(){return"amazon_pay"}constructor(e){super(e)}getWebComponentName(){return"shopify-amazon-pay-button"}getInstrumentName(){return f.AmazonPay}getWebComponentClass(){return Tt}async loadWalletSDK(){await Ua({sdkUrl:Cu.SDK_URL,maxRetries:Cu.MAX_RETRIES})}getLoadEligibility(e){return e.getRootNode()instanceof ShadowRoot?{eligible:!1,reason:"Cannot load Amazon Pay within Shadow DOM"}:{eligible:!0}}};c(Fe,"MAX_RETRIES",3),c(Fe,"SDK_URL",_r);let mr=Fe;var tr={Purple:{D50:"#4524DB",P40:"#5433EB"},Grayscale:{L0:"#FFFFFF"}},ed={16:16},bn="gravity-font-faces",td="\n@font-face {\n  font-family: 'SuisseIntl';\n  src: url('https://cdn.shopify.com/shop-assets/static_uploads/shoplift/SuisseIntl-Book.otf')\n    format('opentype');\n  font-style: normal;\n  font-weight: 450;\n  font-display: fallback;\n}\n\n@font-face {\n  font-family: 'SuisseIntl';\n  src: url('https://cdn.shopify.com/shop-assets/static_uploads/shoplift/SuisseIntl-Medium.otf')\n    format('opentype');\n  font-style: normal;\n  font-weight: 500;\n  font-display: fallback;\n}\n\n@font-face {\n  font-family: 'SuisseIntl';\n  src: url('https://cdn.shopify.com/shop-assets/static_uploads/shoplift/SuisseIntl-SemiBold.otf')\n    format('opentype');\n  font-style: normal;\n  font-weight: 600;\n  font-display: fallback;\n}",rd=class extends HTMLElement{static get observedAttributes(){return["disabled"]}connectedCallback(){this._loadFonts(),this._updateButton()}attributeChangedCallback(){this._updateButton()}_loadFonts(){if(document.querySelector('style[data-description="'.concat(bn,'"], style[data-description="shop-js-font-faces"]')))return;let t=document.createElement("style");t.dataset.description=bn,t.appendChild(document.createTextNode(td)),document.head.appendChild(t)}_updateButton(){var t;let e=this._getOrCreateShadowRoot();e.innerHTML=ad,this.hasAttribute("disabled")&&((t=e.querySelector("button"))==null||t.setAttribute("disabled",""))}_getOrCreateShadowRoot(){return this.shadowRoot||this.attachShadow({mode:"open"}),this.shadowRoot}};function nd(){window.customElements&&(customElements.get("gravity-button")||customElements.define("gravity-button",rd))}var ad="\n<style>\n  * {\n    box-sizing: border-box;\n    border-width: 0;\n    border-style: solid;\n    border-color: currentColor;\n    --focused-box-shadow: 0 0 0 3px #DBD1FF, 0 0 #0000;\n  }\n\n  button {\n    text-transform: none;\n    -webkit-appearance: button;\n    background-color: transparent;\n    background-image: none;\n    font-family: SuisseIntl;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    font-size: 100%;\n    font-weight: inherit;\n    line-height: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    margin: 0;\n    padding: 0;\n    cursor: pointer;\n  }\n\n  button:disabled {\n    cursor: default;\n    opacity: 0.5;\n  }\n\n  .gravity-button {\n    position: relative;\n    display: flex;\n    min-height: var(--gravity-button-min-height);\n    height: var(--gravity-button-height);\n    width: var(--gravity-button-width, 260px);\n    justify-content: center;\n    color: ".concat(tr.Grayscale.L0,";\n    align-items: center;\n    overflow: visible;\n    padding: var(--gravity-button-padding, ").concat(ed[16],"px 10px);\n    border-radius: var(--gravity-button-border-radius);\n    border: none;\n    background-color: ").concat(tr.Purple.P40,";\n    transition: all;\n  }\n\n  .gravity-button:not([disabled]):focus-visible {\n    box-shadow: var(--focused-base-box-shadow, var(--focused-box-shadow));\n    outline: var(--focused-base-outline, 2px solid transparent);\n    outline-offset: var(--focused-base-outline-offset, 2px);\n  }\n\n  .gravity-button:not([disabled]):hover {\n    background-color: ").concat(tr.Purple.D50,';\n  }\n</style>\n\n<button class="gravity-button">\n  <slot id="button-content"></slot>\n</button>\n');const id=".button-content{display:flex;align-items:center;justify-content:center;width:100%;height:100%;gap:3px;white-space:nowrap;container-type:inline-size;container-name:bc}.ShopPromise>.separator{margin:0 8px}@container bc (width <= 338px){.ShopPromise{display:none!important}}gravity-button{display:block}shop-pay-iframe-content{height:100%}.credit-card-iframe-container{position:relative;height:100%;left:-34px;width:0px;transition:width .35s cubic-bezier(.33,0,0,1);visibility:hidden;opacity:0}shop-pay-iframe-content,.credit-card-iframe-container>iframe.credit-card-iframe{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}.credit-card-iframe-visible{opacity:1;visibility:visible}.credit-card-iframe-show-digits{width:38px}.credit-card-iframe{position:absolute;top:0;left:0;object-position:left top;object-fit:none;height:100%;width:80px}svg #pay-square{will-change:transform}.shop-pay-square-removing{overflow:visible}.shop-pay-square-removing #pay-square{transform-origin:81.85% 100%;animation:fadeOutScale .3s cubic-bezier(.33,0,0,1) forwards}.shop-pay-square-removing.animation-complete #pay-square{animation:none;opacity:0;transform:translateY(25%) scale(.55)}@keyframes fadeOutScale{0%{transform:scale(1)}30%{transform:translateY(-15%) scale(1)}95%{opacity:1}to{opacity:0;transform:translateY(25%) scale(.55)}}",od='.accelerated-checkout-button{height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);min-height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 44px),55px);border-radius:var(--shopify-accelerated-checkout-button-border-radius, 0px);box-shadow:var(--shopify-accelerated-checkout-button-box-shadow)}:host([page-type="cart_page"]) .accelerated-checkout-button{height:100%;width:100%;border-radius:var(--shopify-accelerated-checkout-button-border-radius, 4px);box-shadow:var(--shopify-accelerated-checkout-button-box-shadow)}:host([page-type="product"]) .accelerated-checkout-button{min-width:150px}@media (forced-colors: active){.accelerated-checkout-button{border:1px solid transparent!important}:host([page-type="cart_page"]) .accelerated-checkout-button{border:1px solid transparent!important}}.accelerated-checkout-button{--gravity-button-height: clamp(25px, var(--shopify-accelerated-checkout-button-block-size, 44px), 55px);--gravity-button-min-height: clamp(25px, var(--shopify-accelerated-checkout-button-block-size, 44px), 55px);--gravity-button-border-radius: var(--shopify-accelerated-checkout-button-border-radius, 0px);--gravity-button-padding: 0px 10px;--gravity-button-width: 100%}:host([page-type="cart_page"]) .accelerated-checkout-button{--gravity-button-height: 100%;--gravity-button-min-height: 100%;--gravity-button-border-radius: var(--shopify-accelerated-checkout-button-border-radius, 4px);--gravity-button-width: 100%}';class Ba extends Error{constructor(){const e="DeliveryEstimatesResponseIsNullError";super("deliveryEstimates is null or undefined"),this.name=e}}class rr extends Error{constructor(){const e="DeliveryPromiseAPIClientError";super("window.Shopify.ShopPromise.deliveryPromiseAPIClient is undefined"),this.name=e}}const sd=15,ld=200;class cd{constructor(e){c(this,"storefrontAPIToken"),this.storefrontAPIToken=e}async fetchDeliveryPromise({variant:e,postalCode:n,defaultPostalCode:r,sellingPlanId:a}){const i=await Ma({fn:async()=>this.fetchDeliveryOptions(e,n,r,a),maxRetries:sd,delay:ld,retryPolicyFn:o=>o instanceof rr}).catch(o=>{if(o instanceof rr)throw o;return null});if(!i)throw new Ba;return i}async fetchDeliveryOptions(e,n,r,a){var i,o;const s={variantId:e,sellingPlanId:a,postalCode:n||r,isPostalCodeOverride:!!n},l=(o=(i=window==null?void 0:window.Shopify)==null?void 0:i.ShopPromise)==null?void 0:o.deliveryPromiseAPIClient;if(!l)throw new rr;return l(s,this.storefrontAPIToken)}}function dd(t,e){switch(t){case 0:return(e==null?void 0:e.translate("shop_promise_delivery.same_day"))||null;case 1:return(e==null?void 0:e.translate("shop_promise_delivery.next_day"))||null;case 2:return(e==null?void 0:e.translate("shop_promise_delivery.two_day"))||null;default:return null}}const ud="delivery_promise_default_address",hd="delivery_promise_postal_code";class pd extends ft{constructor(){super(...arguments),c(this,"lastFetchedProductVariantId"),c(this,"lastFetchSellingPlan"),c(this,"fetching",!1),c(this,"i18n",null),c(this,"daysToDelivery"),c(this,"formObserver",null)}async connectedCallback(){this.formObserver=new Or(this,({variantId:e,sellingPlanId:n})=>{this.fetchDeliveryPromise(e,n)}),this.formObserver.setupMutationObservers()}setTranslations(e){this.i18n=e,this.render()}get postalCode(){try{return JSON.parse(ct(hd)||"null")||null}catch(e){return null}}get defaultDeliveryAddress(){try{return JSON.parse(ct(ud)||"null")}catch(e){return null}}async fetchDeliveryPromise(e,n){var r,a,i,o;if(!(!e||!this.accessToken)&&!(e===this.lastFetchedProductVariantId&&n===this.lastFetchSellingPlan))try{this.lastFetchedProductVariantId=e,this.lastFetchSellingPlan=n;const s=((r=this.defaultDeliveryAddress)==null?void 0:r.zip)||"",l=await new cd(this.accessToken).fetchDeliveryPromise({variant:e,postalCode:this.postalCode||"",defaultPostalCode:s,sellingPlanId:n||""}),u=Lr(this),d=(i=(a=l==null?void 0:l.selectedShippingOption)==null?void 0:a.brandedPromise)==null?void 0:i.handle;if((u==null?void 0:u.sellingPlanId)!==n||(u==null?void 0:u.variantId)!==e||d!=="shop_promise")return;this.daysToDelivery=(o=l==null?void 0:l.selectedShippingOption)==null?void 0:o.maxCalendarDaysToDelivery,this.render()}catch(s){s instanceof Ba&&(this.daysToDelivery=null)}}render(){const e=dd(this.daysToDelivery,this.i18n);this.classList.add("ShopPromise"),this.innerHTML=e?'<span class="separator">|</span><span>'.concat(e,"</span>"):""}}function md({flow:t="default"}){const e=new URLSearchParams({target_origin:window.location.origin,flow:t});return"".concat(window.location.origin,"/services/login_with_shop/authorize?").concat(e.toString())}function Fa(t,e,n){const r=n!=null?n:window.location.origin;if(t.origin!==r||!e)return!1;try{return t.source===e.contentWindow}catch(a){return!1}}class yd extends ft{constructor(){super(...arguments),c(this,"_userRecognized"),c(this,"iframe",null),c(this,"iframeContainer",null),c(this,"iframeCreationStart",null),c(this,"iframeOrigin",null),c(this,"payLogo",null),c(this,"iframeState","hidden"),c(this,"pendingAnimation",!1),c(this,"handleMessage",e=>{var n;if(Fa(e,this.iframe,e.origin)&&((n=e.data)==null?void 0:n.type)==="loaded"){if(this.iframeOrigin!==e.origin&&(this.iframeOrigin=e.origin),e.data.type==="loaded"&&this.iframeCreationStart){const r=ne()-this.iframeCreationStart,a={instrument:f.ShopPay,pageType:At()};C.histogram({name:I.ShopPayPersonalizedIframeLoadTime,value:r,attributes:a,unit:"ms"}),C.histogram({name:I.ShopPayPersonalizedIframeLoadTimeFromPageLoad,value:window.performance.now(),attributes:a,unit:"ms"})}if(e.data.type==="loaded"){if(this._userRecognized=e.data.recognized===!0,this.iframeCreationStart){const r=ne()-this.iframeCreationStart,a={instrument:f.ShopPay,pageType:At()};C.histogram({name:I.ShopPayPersonalizedIframeLoadTime,value:r,attributes:a,unit:"ms"}),C.histogram({name:I.ShopPayPersonalizedIframeLoadTimeFromPageLoad,value:window.performance.now(),attributes:a,unit:"ms"})}this._userRecognized?this.handleIframeLoaded():(this.cleanup(),this.dispatchEvent(new CustomEvent("user-not-recognized")))}}})}get userRecognized(){return this._userRecognized}async createIframe(){if(this.iframeContainer)return this.iframeContainer;const e=ne();return this.iframeCreationStart=e,this.iframeContainer=document.createElement("div"),this.iframeContainer.classList.add("credit-card-iframe-container"),this.iframeContainer.setAttribute("data-test-id","shop-pay-iframe-container"),this.iframe=document.createElement("iframe"),this.iframe.src=md({flow:"shop_pay_button"}),this.iframe.setAttribute("scrolling","no"),this.iframe.setAttribute("frameborder","no"),this.iframe.setAttribute("loading","lazy"),this.iframe.setAttribute("allowtransparency","true"),this.iframe.setAttribute("allowfullscreen","false"),this.iframe.classList.add("credit-card-iframe"),this.iframeContainer.appendChild(this.iframe),this.iframeContainer}async connectedCallback(){const e=await this.createIframe();this.appendChild(e),this.setupMessageListener()}disconnectedCallback(){this.cleanup()}setPayLogo(e){this.payLogo=e}setDisabled(e){this.disabled=e,e&&this.iframeState!=="animation-complete"&&this.hideIframe()}animationCompleted(){return this.iframeState==="animation-complete"}triggerAnimation(e){const n=this.style.display!=="none",r=["animating","animation-complete"];this.disabled||r.includes(this.iframeState)||!n||(this.iframeState==="ready"?this.showIframe(e):this.pendingAnimation=!0)}hideAnimation(){this.pendingAnimation=!1,this.iframeState==="animating"&&this.hideIframe()}setupMessageListener(){window.addEventListener("message",this.handleMessage)}handleIframeLoaded(){this.iframeState==="hidden"&&(this.iframeState="ready");const e=this.style.display!=="none";this.pendingAnimation&&!this.disabled&&this.iframeState==="ready"&&e&&(this.pendingAnimation=!1,this.showIframe())}animateIn(){this.disabled||!this.iframeContainer||this.iframeState!=="ready"||!this.payLogo||(this.iframeState="animating",this.payLogo.classList.add("shop-pay-square-removing"),this.iframeContainer.classList.add("credit-card-iframe-visible"),this.sendMessageToIframe(),this.payLogo.addEventListener("animationend",()=>{var e,n;(e=this.iframeContainer)==null||e.classList.add("credit-card-iframe-show-digits"),this.iframeState="animation-complete",(n=this.payLogo)==null||n.classList.add("animation-complete")},{once:!0}))}showIframe(e){if(!e){this.animateIn();return}const n=setTimeout(()=>{this.animateIn()},500);return()=>clearTimeout(n)}hideIframe(){var e,n,r,a;this.iframeState!=="hidden"&&this.iframeState!=="animation-complete"&&(this.iframeState="hidden",(e=this.payLogo)==null||e.classList.remove("shop-pay-square-removing"),(n=this.payLogo)==null||n.classList.remove("animation-complete"),(r=this.iframeContainer)==null||r.classList.remove("credit-card-iframe-visible"),(a=this.iframeContainer)==null||a.classList.remove("credit-card-iframe-show-digits"))}cleanup(){var e;window.removeEventListener("message",this.handleMessage),(e=this.iframeContainer)==null||e.remove(),this.iframeContainer=null,this.iframe=null,this.iframeOrigin=null,this.payLogo=null,this.iframeState="hidden",this.pendingAnimation=!1}sendMessageToIframe(){var e;if(!((e=this.iframe)!=null&&e.contentWindow)||!this.iframeOrigin)return;const n={timestamp:ne(),type:"animate-in"};this.iframe.contentWindow.postMessage(n,this.iframeOrigin)}}function gd(t){const e=t.querySelector(".button-content span");if(!e)return 0;const n=window.getComputedStyle(e);return parseFloat(n.fontSize)}function fd({element:t,isCTA:e,onFormStateChange:n,personalized:r,shopPromiseEligible:a,shopPromiseProductPageContent:i}){if(!r)return{cleanup:()=>{},shouldRender:()=>!1};let o={disabled:!1,variantId:void 0};const s=new Or(t,({disabled:l,variantId:u})=>{o={disabled:l,variantId:u},n==null||n()});return s.setupMutationObservers(),{cleanup:()=>{var l;(l=s.addToCartMutationObserver)==null||l.disconnect()},shouldRender:()=>{var l;if(o.disabled&&o.variantId!==void 0||a&&i||((l=t.offsetWidth)!=null?l:0)<170)return!1;if(e){const u=gd(t);if(u<10||u>18)return!1}return!0}}}class bd extends fe{constructor(){super(...arguments),c(this,"name",f.ShopPay),c(this,"button",null),c(this,"shopPromiseProductPageContent",null),c(this,"shopPromiseEligible",!1),c(this,"payLogo",null),c(this,"iframeContent",null),c(this,"iframeRenderEligibility",null),c(this,"buttonContentDiv",null),c(this,"intersectionObserver",null),c(this,"inView",!1),c(this,"impressionTracked",!1),c(this,"impressionAbortController",null)}static get observedAttributes(){return["disabled"]}connectedCallback(){this.shopPromiseEligible=ma(),this.setupIntersectionObserver(),this.cleanupOnFailure(async()=>{nd(),await this.render()},this.name)}disconnectedCallback(){var e,n;(e=this.intersectionObserver)==null||e.disconnect(),this.intersectionObserver=null,(n=this.iframeRenderEligibility)==null||n.cleanup(),this.iframeRenderEligibility=null,this.impressionAbortController&&!this.impressionAbortController.signal.aborted&&this.impressionAbortController.abort()}attributeChangedCallback(e,n,r){super.attributeChangedCallback(e,n,r,this.button),e==="disabled"&&this.onDisabledChanged(r)}setupIntersectionObserver(){var e,n;if("IntersectionObserver"in window){const r=((n=(e=this.button)==null?void 0:e.offsetHeight)!=null?n:46)*-1;this.intersectionObserver=new IntersectionObserver(a=>{a.forEach(i=>{var o;i.isIntersecting&&!this.inView&&(this.inView=!0,this.onButtonInView(!0),(o=this.intersectionObserver)==null||o.unobserve(this))})},{threshold:.1,rootMargin:"".concat(r,"px")}),this.intersectionObserver.observe(this)}else this.inView=!0,this.onButtonInView()}onButtonInView(e){var n;(n=this.iframeRenderEligibility)!=null&&n.shouldRender()?(this.iframeContent?this.iframeContent.triggerAnimation(e):this.createAndAttachIframe(),this.waitForIframeAndTrackImpression()):this.trackButtonImpression()}waitForIframeAndTrackImpression(){if(this.iframeContent&&this.iframeContent.userRecognized!==void 0){this.trackButtonImpression();return}this.impressionAbortController&&!this.impressionAbortController.signal.aborted&&this.impressionAbortController.abort();const e=Re("AbortController");if(this.impressionAbortController=e,!e){this.trackButtonImpression();return}const n=e.signal,r=a=>{var i,o;const s=(i=this.iframeContent)==null?void 0:i.querySelector("iframe");Fa(a,s)&&a.data.type==="loaded"&&(this.trackButtonImpression(),(o=this.impressionAbortController)==null||o.abort(),this.impressionAbortController=null)};window.addEventListener("message",r,{signal:n})}trackButtonImpression(){var e;if(this.impressionTracked)return;const n=!!((e=this.iframeContent)!=null&&e.userRecognized);H({event:U.ShopPayButtonInView,instrumentId:this.name,eventSubtype:n?"personalized":"not_personalized"}),this.impressionTracked=!0}onDisabledChanged(e){var n;(n=this.iframeContent)==null||n.setDisabled(e!==null)}async addButtonContent(){if(!this.button)return;let e;const n=await this.i18n,r=dt({instrumentName:f.ShopPay}),a=new DOMParser().parseFromString(r,"image/svg+xml").documentElement;this.payLogo=a,this.isCTA?e=Mr({translate:n.translate,logoElement:a,setAriaHidden:!1}):(e=document.createElement("div"),e.appendChild(a)),e.classList.add("button-content"),this.shopPromiseEligible&&!this.shopPromiseProductPageContent&&(this.shopPromiseProductPageContent=document.createElement("shop-promise-product-page-content"),this.shopPromiseProductPageContent.setTranslations(n),this.shopPromiseProductPageContent.setAttribute("access-token",this.accessToken||""),e.appendChild(this.shopPromiseProductPageContent));const i=this.getAttribute("is-shop-pay-personalization-eligible")==="true",o=()=>{var s,l;if(!this.iframeContent){(s=this.iframeRenderEligibility)!=null&&s.shouldRender()&&this.createAndAttachIframe();return}const u=((l=this.iframeRenderEligibility)==null?void 0:l.shouldRender())||!1;this.iframeContent.animationCompleted()||(this.iframeContent.style.display=u?"":"none",u?u&&this.inView&&this.iframeContent.triggerAnimation():this.iframeContent.hideAnimation())};this.iframeRenderEligibility=fd({element:this.button,isCTA:this.isCTA,onFormStateChange:o,personalized:i,shopPromiseEligible:this.shopPromiseEligible,shopPromiseProductPageContent:this.shopPromiseProductPageContent}),this.buttonContentDiv=e,this.button.appendChild(e)}createAndAttachIframe(){!this.buttonContentDiv||this.iframeContent||(this.iframeContent=document.createElement("shop-pay-iframe-content"),this.iframeContent.setPayLogo(this.payLogo),this.iframeContent.setDisabled(this.disabled),this.iframeContent.addEventListener("user-not-recognized",()=>{this.iframeContent=null}),this.buttonContentDiv.appendChild(this.iframeContent),this.inView&&this.iframeContent.triggerAnimation())}handleIframeRenderLogic(){if(!this.iframeRenderEligibility)return;const e=this.iframeRenderEligibility.shouldRender();if(!this.iframeContent&&e){this.createAndAttachIframe();return}this.iframeContent&&(this.iframeContent.style.display=e?"":"none",e&&this.inView?this.iframeContent.triggerAnimation():e||this.iframeContent.hideAnimation())}getShopPayBehavior(){return this.pageType===P.ProductPage?"force_shop_pay_product":this.pageType===P.CartPage?"force_shop_pay_cart":"force_shop_pay"}async handleClick(e){var n,r;if(e.preventDefault(),this.disabled||!this.button||this.button.getAttribute("disabled"))return;this.disabled=!0;const[a,i]=await Promise.all([this.i18n,this.dataSource]);try{const{decelerationReason:o,cart:s,customValidationError:l,unrecoverableError:u}=await ge({element:this,instrumentName:f.ShopPay,dataSource:i});if(u){y.sheetClicked({instrument:this.name,result:"failed"}),ke(a,u);return}if(y.sheetClicked({instrument:this.name,result:"success"}),l){Ne(a,l.message);return}if(o){M({checkoutUrl:(n=s==null?void 0:s.checkoutUrl)!=null?n:"",instrumentName:this.name,reason:o});return}pe((r=s==null?void 0:s.checkoutUrl)!=null?r:"",this.getShopPayBehavior())}catch(o){b.notify(new wd("An error occurred when attempting to create a cart using the Shop Pay Button. Details: ".concat(o),{cause:o})),y.sheetClicked({instrument:this.name,result:"failed"}),gt(a)}}async render(){const e=this.getOrCreateShadowRoot();if(e.innerHTML)return;this.button||(this.button=document.createElement("gravity-button"));const n=document.createElement("style");n.innerHTML=[od,id].join("\n"),e.appendChild(n),await this.addButtonContent(),this.disabled&&this.button.setAttribute("disabled",""),this.button.classList.add(Te.BUTTON),this.button.onclick=r=>this.handleClick(r),e.appendChild(this.button),this.ensureLightDOMIsNotEmpty(),this.handleIframeRenderLogic(),this.onRendered()}}ae("shop-promise-product-page-content",pd),ae("shop-pay-iframe-content",yd);class wd extends Error{constructor(){super(...arguments),c(this,"name","ShopPayButtonError")}}var _d=Object.defineProperty,Ed=Object.getOwnPropertyDescriptor,Ad=(t,e,n,r)=>{for(var a=Ed(e,n),i=t.length-1,o;i>=0;i--)(o=t[i])&&(a=o(e,n,a)||a);return a&&_d(e,n,a),a};class $a extends be{static walletName(){return"shop_pay"}constructor(e){super(e)}getWebComponentName(){return"shop-pay-wallet-button"}getWebComponentClass(){return bd}getInstrumentName(){return f.ShopPay}async createWebComponent(e){var n;const r=await super.createWebComponent(e),{walletContainer:a}=e,i=a.isShopPayPersonalizationEnabled,o=(n=this.walletParams)==null?void 0:n.personalized;return i&&o&&r.setAttribute("is-shop-pay-personalization-eligible","true"),r}async loadWalletSDK(){await this.loadLogo()}getLoadEligibility(){return{eligible:!0}}async loadLogo(){const e=await import("./shop-pay-logo-nzlo6e3W.js");if(e)Ke({instrumentName:f.ShopPay,logoType:"LIGHT",logoContent:e.default});else throw new Error("Shop wordmark logo is empty")}}Ad([Ur("Shop Pay")],$a.prototype,"getLoadEligibility");const Cd=".button{display:flex;justify-content:center;align-items:center;width:100%;border:none}.button:hover:not(:disabled){filter:brightness(92%);cursor:pointer}.button:disabled{opacity:.5;cursor:not-allowed}.button:focus-visible{box-shadow:inset 0 0 0 2px #1773b0,inset 0 0 0 3px #f8f8f8!important;outline:none}.logo-only>svg{width:50px}.dark{background:#000;color:#fff}.light{background:#fff;color:#000;outline:1px solid #3c4043}@media (forced-colors: active){.light{outline:none}}@media (prefers-contrast: high),(forced-colors: active){.button:focus-visible{outline:.2rem solid;outline-offset:.1rem}}.content{display:flex;justify-content:center;align-items:center;white-space:pre}";function nr({cart:t,i18n:e}){const n=Na(t),r={label:e.translate("order_summary.subtotal"),type:"SUBTOTAL",price:ve(n.amount)},a=Ra({deliveryGroups:t.deliveryGroups,i18n:e}).map(s=>Sd({lineItem:s,type:"LINE_ITEM"})),i=Id(t,e),o=[r,...a,...i];if(t.totalDutyAmount){const s={label:e.translate("order_summary.duties"),type:"LINE_ITEM",price:ve(t.totalDutyAmount.amount)};o.push(s)}if(t.totalTaxAmount){const s={label:e.translate("order_summary.taxes"),type:"TAX",price:ve(t.totalTaxAmount.amount)};o.push(s)}return{totalPrice:t.totalAmount.amount,currencyCode:t.totalAmount.currencyCode,totalPriceStatus:"ESTIMATED",totalPriceLabel:e.translate("order_summary.total"),displayItems:o}}function Sd({lineItem:t,type:e,status:n="FINAL"}){return{label:t.label,type:e,price:t.amount,status:n}}function Id(t,e){return[...t.lineItems.flatMap(n=>n.discountAllocations),...t.discountAllocations].flatMap(n=>{var r;const a=(r=n.title)!=null?r:n.code;return{label:a!=null?a:e.translate("order_summary.discount"),type:"LINE_ITEM",price:"-".concat(ve(Number(n.discountedAmount.amount)))}})}const za=["PAYMENT_AUTHORIZATION"],Pd=za.concat(["SHIPPING_ADDRESS","SHIPPING_OPTION"]),vd=["PAN_ONLY"];function Dd({walletParams:t,isShippingRequired:e,hasSellingPlan:n,i18n:r,buyerCurrency:a,authJwt:i}){const o={totalPrice:"0.01",currencyCode:a,totalPriceStatus:"ESTIMATED",totalPriceLabel:r.translate("order_summary.total"),displayItems:[{label:r.translate("order_summary.subtotal"),type:"SUBTOTAL",price:"0.01"}]},s={...t.paymentData,shippingAddressRequired:e,shippingOptionRequired:e,transactionInfo:o};return s.merchantInfo.authJwt=i,e||delete s.shippingAddressParameters,s.callbackIntents=e?Pd:za,n&&Rd(s),s}function Rd(t){t.allowedPaymentMethods.forEach(e=>{e.parameters.allowedAuthMethods=e.parameters.allowedAuthMethods.filter(n=>vd.includes(n))})}class Td extends Ta{constructor(e){super(),c(this,"brandName"),c(this,"completionResult"),this.i18n=e,this.brandName=this.i18n.translate("brand.google_pay"),this.completionResult=void 0}mapMutationResultToPaymentSheetAction(e){const n=this.mapCustomValidationFunctionErrorToAction(e.errors);if(n)return n;const r=this.getErrorActions(),a=e.errors,i=this.getUnhandledErrors(a,r);i.forEach(s=>{b.notify(new ze("[".concat(f.GooglePay,"] mutation result error not handled: ").concat(s),{groupingHash:"UnhandledActionError:".concat(f.GooglePay,":").concat(s)}))});const{firstPaymentSheetAction:o}=this.getMergedPaymentSheetAction({result:e,errorActions:r});return o||(i.length>0?{action:"abort"}:{action:"update",errors:[]})}mapCompletionResultToPaymentSheetAction(e){if(!e.data)throw new Error("[".concat(f.GooglePay,"] completion returned null result"));switch(this.completionResult=e,e.data.__typename){case"SubmitSuccess":return{action:"complete",redirectUrl:e.data.redirectUrl};case"SubmitAlreadyAccepted":case"SubmitThrottled":case"SubmitFailed":{const n=this.mapCustomValidationFunctionErrorToAction(e.errors);if(n)return n;const r=this.getErrorActions(),a=e.errors;this.getUnhandledErrors(a,r).forEach(o=>{b.notify(new ze("[".concat(f.GooglePay,"] completion result error not handled: ").concat(o),{groupingHash:"UnhandledActionError:".concat(f.GooglePay,":").concat(o)}))});const{prioritizedPaymentSheetAction:i}=this.getMergedPaymentSheetAction({result:e,errorActions:r});return i||(N(this.brandName,this.i18n),{action:"abort"})}default:throw new Error("[".concat(f.GooglePay,"] unknown completion result type: ").concat(e.data.__typename))}}getErrorActions(){return[{errors:[g.CaptchaCompletionRequired],generateAction:()=>this.decelerateOrAbort({reason:"captcha_required",instrumentName:f.GooglePay,errorOrWarning:g.CaptchaCompletionRequired,result:this.completionResult})},{errors:[g.InvalidLanguage],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.GooglePay,"] mutation provided invalid language, aborting")))},()=>N(this.brandName,this.i18n)]})},{errors:[g.InvalidCountry],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.GooglePay,"] mutation provided invalid country, aborting")))},()=>N(this.brandName,this.i18n)]})},{errors:[g.MissingCartId],generateAction:()=>({action:"abort",effects:[()=>{b.notify(new Error("[".concat(f.GooglePay,"] mutation provided invalid cart ID, aborting")))},()=>N(this.brandName,this.i18n)]})},{errors:[g.RedirectToCheckoutRequired],generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n)]})},{errors:Cr,generateAction:()=>this.decelerateOrAbort({reason:"dynamic_tax",instrumentName:f.GooglePay,errorOrWarning:g.NewTaxMustBeAccepted,result:this.completionResult})},{errors:[g.MerchandiseExpectedPriceMismatch,g.DeliveryLineChanged,g.DeliveryLocalPickupLineChanged,...la],generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n)]})},{errors:[g.UnsupportedGooglePayPaymentMethod],generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n),()=>{b.notify(new ze("[".concat(f.GooglePay,"] payment method is not supported"),{groupingHash:"UnhandledActionError:".concat(f.GooglePay,":payment_method_not_supported")}))}]})},{errors:[g.PaymentsMethodRequired,g.PaymentsMethodUnavailable],generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n)]})},{errors:[g.CustomValidation],generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n)]})},{errors:[g.MerchandiseOutOfStock],generateAction:()=>({action:"abort",effects:[()=>me(this.i18n)]})},{errors:ca,generateAction:()=>({action:"abort",effects:[()=>N(this.brandName,this.i18n)]})},{errors:da,generateAction:()=>this.decelerateOrAbort({reason:"not_enough_stock",instrumentName:f.GooglePay,errorOrWarning:g.MerchandiseNotEnoughStock,result:this.completionResult})},{errors:sa,generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_UNSERVICEABLE",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.shipping_address")}]})},{errors:oa,generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.postal_code")}]})},{errors:[g.NoDeliveryGroupSelected],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_OPTION_INVALID",intent:"SHIPPING_OPTION",message:this.i18n.translate("errors.missing.shipping_option")}]})},{errors:[g.BuyerIdentityEmailDomainInvalid,g.BuyerIdentityEmailNotExpectedPattern,g.BuyerIdentityEmailInvalid],generateAction:()=>({action:"show_error",errors:[{reason:"OTHER_ERROR",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.email")}]})},{errors:[g.BuyerIdentityEmailRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.email")}]})},{errors:[g.DeliveryAddress2Required,g.DeliveryAddress2AddressFieldRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.address2")}]})},{errors:[g.DeliveryAddress2Invalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.address2")}]})},{errors:[g.DeliveryCityAddressFieldRequired,g.DeliveryCityRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.city")}]})},{errors:[g.DeliveryCityInvalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.city")}]})},{errors:[g.DeliveryZoneRequiredForCountry],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.zone")}]})},{errors:[g.DeliveryZoneNotFound],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.zone")}]})},{errors:[g.DeliveryPostalCodeRequired,g.DeliveryPostalCodeAddressFieldRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.postal_code")}]})},{errors:[g.DeliveryPostalCodeInvalid,g.DeliveryInvalidPostalCodeForZone,g.DeliveryInvalidPostalCodeForCountry,g.DeliveryZipInvalidForCountry],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.postal_code")}]})},{errors:[g.DeliveryPhoneNumberRequired],generateAction:()=>({action:"show_error",errors:[{reason:"OTHER_ERROR",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.phone_number")}]})},{errors:[g.DeliveryPhoneNumberInvalid,g.BuyerIdentityInvalidPhone,g.DeliveryPhoneDoesNotMatchExpectedPattern],generateAction:()=>({action:"show_error",errors:[{reason:"OTHER_ERROR",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.phone_number")}]})},{errors:[g.BuyerIdentityInvalidCountry,g.DeliveryCountryInvalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.country")}]})},{errors:[g.DeliveryFirstNameRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.first_name")}]})},{errors:[g.DeliveryFirstNameInvalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.first_name")}]})},{errors:[g.DeliveryLastNameRequired],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.last_name")}]})},{errors:[g.DeliveryLastNameInvalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.last_name")}]})},{errors:[g.DeliveryAddress1Required],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.missing.address1")}]})},{errors:[g.DeliveryAddress1Invalid],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.invalid.address1")}]})},{errors:[g.DeliveryLastNameContainsEmojis],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.emojis.last_name")}]})},{errors:[g.DeliveryCityContainsEmojis],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.emojis.city")}]})},{errors:[g.DeliveryAddress1ContainsEmojis],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.emojis.address1")}]})},{errors:[g.DeliveryAddress2ContainsEmojis],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.emojis.address2")}]})},{errors:[g.DeliveryPostalCodeContainsEmojis],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.emojis.postal_code")}]})},{errors:[g.DeliveryAddress1TooLong],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.too_long.address1")}]})},{errors:[g.DeliveryAddress2TooLong],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.too_long.address2")}]})},{errors:[g.DeliveryFirstNameTooLong],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.too_long.first_name")}]})},{errors:[g.DeliveryLastNameTooLong],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.too_long.last_name")}]})},{errors:[g.DeliveryCityTooLong],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.too_long.city")}]})},{errors:[g.DeliveryFirstNameContainsUrl],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.url.first_name")}]})},{errors:[g.DeliveryLastNameContainsUrl],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.url.last_name")}]})},{errors:[g.DeliveryAddress1ContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.html_tags.address1")}]})},{errors:[g.DeliveryAddress2ContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.html_tags.address2")}]})},{errors:[g.DeliveryCityContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.html_tags.city")}]})},{errors:[g.DeliveryFirstNameContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.html_tags.first_name")}]})},{errors:[g.DeliveryLastNameContainsHtmlTags],generateAction:()=>({action:"show_error",errors:[{reason:"SHIPPING_ADDRESS_INVALID",intent:"SHIPPING_ADDRESS",message:this.i18n.translate("errors.html_tags.last_name")}]})},{errors:[g.InvalidPaymentGooglePayBillingAddress],generateAction:()=>({action:"show_error",errors:[{reason:"PAYMENT_DATA_INVALID",intent:"PAYMENT_METHOD",message:this.i18n.translate("errors.invalid.billing_address")}]})}]}mapCustomValidationFunctionErrorToAction(e){const n=vr(e);return n?(b.notify(new Error("[".concat(f.GooglePay,"] custom validation function error: ").concat(JSON.stringify(e)))),Rr(this.i18n.translate("error_dialogs.wallet.title",{wallet:this.brandName}),n.message),{action:"abort"}):null}}function Nd({deliveryGroups:t,i18n:e}){var n;const r=Nc(va({deliveryGroups:t}),e),a=r.map(({handle:o,title:s,description:l})=>({id:o,label:s!=null?s:"",description:l})),i=(n=r.find(o=>{var s;return o.handle.includes(((s=t[0].selectedDeliveryOption)==null?void 0:s.handle)||"")}))==null?void 0:n.handle;return{shippingOptions:a,defaultSelectedOptionId:i}}function ar(t){let e=t.administrativeArea,n=t.locality;t.countryCode==="AE"&&(n=t.administrativeArea,t.administrativeArea in wn&&(e=wn[t.administrativeArea])),t.countryCode==="MX"&&e&&(e=kd(e));let r,a;if(t.name){const[i,...o]=(t.name||"").split(" ");o.length===0?a=i:a=o.join(" "),r=i}return{firstName:r,lastName:a,address1:t.address1,address2:t.address2,city:n,province:e,country:t.countryCode,phone:t.phoneNumber,zip:t.postalCode}}const wn={"\u0623\u0628\u0648 \u0638\u0628\u064A":"AZ","\u0625\u0645\u0627\u0631\u0629 \u0627\u0644\u0634\u0627\u0631\u0642\u0629\u0651":"SH",\u0627\u0644\u0641\u062C\u064A\u0631\u0629:"FU",\u0639\u062C\u0645\u0627\u0646:"AJ","\u0631\u0623\u0633 \u0627\u0644\u062E\u064A\u0645\u0629":"RK","\u0625\u0645\u0627\u0631\u0629 \u0631\u0623\u0633 \u0627\u0644\u062E\u064A\u0645\u0629":"RK","\u0623\u0645 \u0627\u0644\u0642\u064A\u0648\u064A\u0646":"UQ","\u0627\u0645 \u0627\u0644\u0642\u064A\u0648\u064A\u0646":"UQ",\u062F\u0628\u064A:"DU","\u0625\u0645\u0627\u0631\u0629 \u062F\u0628\u064A\u0651":"DU"},kd=t=>t==="M\xE9x."?"MEX":t==="Q.R."?"Q ROO":t.replace(/\./g,"").toUpperCase();class ir extends Error{constructor(){super(...arguments),c(this,"name","GooglePayError")}}function Ha(t){const e=t.auth;if(!(Array.isArray(e)&&e.length>0))return{result:"error",reason:"invalidAuthParams"};const n=ne();for(const r of e)if(n<r.expiresAt*1e3)return{result:"success",jwt:r.jwt};return{result:"error",reason:"authJwtExpired"}}const Ld=27e3,Od="Google Pay Timeout";var pt,te,qe;class xd{constructor({walletParams:e,isShippingRequired:n,hasSellingPlan:r,dataSource:a,button:i,i18n:o,cartClient:s,buyerCurrency:l,abortController:u}){c(this,"name",f.GooglePay),c(this,"walletParams"),c(this,"cart",null),c(this,"isShippingRequired"),c(this,"hasSellingPlan"),c(this,"dataSource"),c(this,"button"),c(this,"paymentsClient"),c(this,"i18n"),c(this,"actionGenerator"),c(this,"cartClient"),c(this,"initialBuyerCurrency"),c(this,"terminalCartCreationError"),$(this,pt),$(this,te),$(this,qe),c(this,"onPaymentDataChanged",async p=>{var m,_;const{callbackTrigger:w,shippingAddress:E,shippingOptionData:S}=p,{translate:z}=this.i18n;switch(w){case"INITIALIZE":{if(!this.cart)try{const{cart:oe,customValidationError:R,decelerationReason:B,unrecoverableError:F}=await ge({element:this.button,instrumentName:f.GooglePay,dataSource:this.dataSource});if(R||F)return this.terminalCartCreationError=R?R.message:Md(this.i18n,F),this.showPaymentDataRequestError({error:{reason:"OTHER_ERROR",message:this.terminalCartCreationError,intent:"SHIPPING_ADDRESS"}});if(B)return M({checkoutUrl:(m=oe==null?void 0:oe.checkoutUrl)!=null?m:"",instrumentName:this.name,reason:B}),this.showPaymentDataRequestError({error:{intent:"SHIPPING_ADDRESS",reason:"OTHER_ERROR",message:""}});this.cart=oe,y.sheetClicked({instrument:this.name,result:"success"})}catch(oe){return y.sheetClicked({instrument:this.name,result:"failed"}),this.showPaymentDataRequestError({intent:"SHIPPING_ADDRESS"})}let O=!1;if(E)try{return await this.handleShippingAddressChange(this.cart.id,E)}catch(oe){O=!0}return{newTransactionInfo:nr({cart:this.cart,i18n:this.i18n}),...O?this.showPaymentDataRequestError():{}}}case"SHIPPING_ADDRESS":return this.terminalCartCreationError?this.showPaymentDataRequestError({error:{reason:"OTHER_ERROR",message:this.terminalCartCreationError,intent:"SHIPPING_ADDRESS"}}):this.cart?E?this.handleShippingAddressChange(this.cart.id,E):{newTransactionInfo:nr({cart:this.cart,i18n:this.i18n})}:this.showPaymentDataRequestError({intent:w});case"SHIPPING_OPTION":{if(!this.cart)return this.showPaymentDataRequestError({intent:w});if(!S||!S.id)return{error:{reason:"SHIPPING_OPTION_INVALID",message:z("shipping_methods.choose_delivery_strategy"),intent:"SHIPPING_OPTION"}};const O=await this.handleDeliveryOptionChange(S.id.split(","));return O.data?(this.cart=O.data,O.abortReason==="out_of_stock"?this.showPaymentDataRequestError({error:{intent:"SHIPPING_OPTION",reason:"OTHER_ERROR",message:this.i18n.translate("error_dialogs.product.out_of_stock")}}):O.decelerationReason?(M({checkoutUrl:(_=this.cart.checkoutUrl)!=null?_:"",instrumentName:this.name,reason:O.decelerationReason}),this.showPaymentDataRequestError({error:{intent:"SHIPPING_OPTION",reason:"OTHER_ERROR",message:""}})):this.handlePaymentDataRequestUpdate({result:O,intent:w})):this.showPaymentDataRequestError({intent:w})}default:return this.logException("Payment data changed callback returned unexpected intent: ".concat(w),{severity:"warning"}),this.showPaymentDataRequestError({intent:"SHIPPING_ADDRESS"})}}),c(this,"onPaymentAuthorized",async p=>{var m,_,w;y.authorizationAttempt(this.name),Je(k.AuthorizationLatency,this.name);const E=(m=p.paymentMethodData.info)==null?void 0:m.billingAddress,S=p.shippingAddress,{signature:z,signedMessage:O,protocolVersion:oe}=JSON.parse(p.paymentMethodData.tokenizationData.token);if(!E)return this.handlePaymentAuthorizationError({error:{reason:"PAYMENT_DATA_INVALID",intent:"PAYMENT_AUTHORIZATION",message:this.i18n.translate("errors.invalid.billing_address")}});if(this.isShippingRequired&&!S)return this.handlePaymentAuthorizationError({error:{reason:"SHIPPING_ADDRESS_INVALID",intent:"PAYMENT_AUTHORIZATION",message:this.i18n.translate("errors.invalid.shipping_address")}});const R=async()=>{var F,L;const ie=(F=A(this,pt))!=null?F:p.email,se=this.isShippingRequired?{validateAddress:!0,streetAddress:ar(S)}:{};return await $e({cartClient:this.cartClient,cartId:this.cart.id,instrumentName:this.name,emailAddress:ie,abortSignal:(L=A(this,te))==null?void 0:L.signal,...se})},B=async()=>{var F;const L=ar(E);return await xr({cartId:this.cart.id,totalAmount:this.cart.totalAmount,paymentMethod:{walletPaymentMethod:{googlePayWalletContent:{billingAddress:L,signature:z,signedMessage:O,protocolVersion:oe}}},canUsePaymentMethodForFreeOrder:!1,billingAddress:L,cartClient:this.cartClient,instrumentName:this.name,hasSellingPlan:this.hasSellingPlan,abortSignal:(F=A(this,te))==null?void 0:F.signal})};try{const F=setTimeout(()=>{var Iu;(Iu=A(this,te))==null||Iu.abort("[Google Pay] Timeout")},Ld),L=await R(),ie=this.handleIntermediatePaymentAuthorizationResult(L);if(ie)return ie;const se=await B(),Eu=this.handleIntermediatePaymentAuthorizationResult(se);if(Eu)return Eu;if(clearTimeout(F),(w=(_=A(this,te))==null?void 0:_.signal)!=null&&w.aborted)throw new ir("Abort Controller was not aborted");const Au=await La({cartId:this.cart.id,token:z,cartClient:this.cartClient,instrumentName:this.name});st({result:Au,instrument:this.name});const Ru=this.actionGenerator.mapCompletionResultToPaymentSheetAction(Au);return this.handlePaymentAuthorizationResult(Ru)}catch(F){const{message:L}=F;return L!=null&&L.includes(Od)?this.handlePaymentAuthorizationError({status:"timeout"}):(this.logException(L),this.handlePaymentAuthorizationError())}}),c(this,"handlePaymentAuthorizationResult",async p=>{var m,_;switch((m=p.effects)==null||m.forEach(w=>w()),p.action){case"complete":return this.emitAuthorizationCompleteMetric("success"),pe(p.redirectUrl),{transactionState:"SUCCESS"};case"show_error":return this.handlePaymentAuthorizationError({error:(_=p.errors)==null?void 0:_[0]});case"decelerate":return M({checkoutUrl:p.redirectUrl,instrumentName:this.name,reason:p.reason}),this.emitAuthorizationCompleteMetric("failed"),{transactionState:"SUCCESS"};case"abort":default:return this.emitAuthorizationCompleteMetric("failed"),{transactionState:"SUCCESS"}}}),this.walletParams=e,this.isShippingRequired=n,this.hasSellingPlan=r,this.dataSource=a,this.button=i,this.i18n=o,this.cartClient=s,this.initialBuyerCurrency=l,x(this,pt,e.customerAccountEmail),x(this,te,u);const d=Ha(this.walletParams);if(d.result==="error")throw b.leaveBreadcrumb("Google Pay Invalid AuthJwt detected on click",{reason:d.reason,walletParams:this.walletParams},"error"),new ir(d.reason);x(this,qe,d.jwt);const h={environment:this.walletParams.environment,merchantInfo:{...this.walletParams.paymentData.merchantInfo,authJwt:A(this,qe)},paymentDataCallbacks:{onPaymentDataChanged:this.isShippingRequired?this.onPaymentDataChanged:void 0,onPaymentAuthorized:this.onPaymentAuthorized}};this.paymentsClient=new window.google.payments.api.PaymentsClient(h),this.actionGenerator=new Td(this.i18n)}async handleClick(){if(!this.isShippingRequired)throw new re("Google Pay checkout started for digital cart");const e=Dd({walletParams:this.walletParams,isShippingRequired:this.isShippingRequired,hasSellingPlan:this.hasSellingPlan,i18n:this.i18n,buyerCurrency:this.initialBuyerCurrency,authJwt:A(this,qe)});await this.paymentsClient.loadPaymentData(e)}showPaymentDataRequestError({error:e,intent:n="SHIPPING_ADDRESS"}={}){return e?{error:e}:{error:{intent:n,reason:"OTHER_ERROR",message:this.i18n.translate("error_dialogs.wallet.generic_error",{wallet:this.i18n.translate("brand.google_pay")})}}}handlePaymentAuthorizationError({error:e,status:n="failed"}={}){return this.emitAuthorizationCompleteMetric(n),{transactionState:"ERROR",...this.showPaymentDataRequestError({error:e,intent:"PAYMENT_AUTHORIZATION"})}}handlePaymentDataRequestUpdate({result:e,intent:n}){var r,a;const i=this.actionGenerator.mapMutationResultToPaymentSheetAction(e);switch(i.action){case"update":{const o=i,{data:s,decelerationReason:l}=e;return s?l?(M({checkoutUrl:s.checkoutUrl,instrumentName:this.name,reason:l}),this.showPaymentDataRequestError({error:{reason:"OTHER_ERROR",message:"",intent:"OFFER"}})):this.isShippingRequired&&s.deliveryGroups.length===0?this.showPaymentDataRequestError({error:{reason:"SHIPPING_ADDRESS_INVALID",message:this.i18n.translate("errors.address_unserviceable",{shopName:this.walletParams.paymentData.merchantInfo.merchantName}),intent:"SHIPPING_ADDRESS"}}):{newTransactionInfo:nr({cart:s,i18n:this.i18n}),newShippingOptionParameters:this.isShippingRequired?Nd({deliveryGroups:s.deliveryGroups,i18n:this.i18n}):void 0,error:(r=o.errors)==null?void 0:r[0]}:this.showPaymentDataRequestError({intent:n})}case"show_error":return{error:(a=i.errors)==null?void 0:a[0]};case"abort":return this.showPaymentDataRequestError({intent:n});default:return this.logException("Payment sheet update returned unexpected action (see request tab for details)",{metadata:{request:{action:i.action,intent:n}}}),this.showPaymentDataRequestError({intent:n})}}handleIntermediatePaymentAuthorizationResult(e){var n,r,a,i;if(st({result:e,instrument:this.name}),e.abortReason==="out_of_stock")return me(this.i18n),{transactionState:"SUCCESS"};if(e.decelerationReason)return M({checkoutUrl:(r=(n=e.data)==null?void 0:n.checkoutUrl)!=null?r:"",instrumentName:this.name,reason:e.decelerationReason}),{transactionState:"SUCCESS"};const o=this.actionGenerator.mapMutationResultToPaymentSheetAction(e);if(((a=e.errors)==null?void 0:a.length)>0){const s=o;if(s.action==="show_error")return this.handlePaymentAuthorizationError({error:(i=s.errors)==null?void 0:i[0]})}}handleCurrencyChange(e,n){Dr({currentCartTotal:n,initialBuyerCurrency:this.initialBuyerCurrency})&&M({checkoutUrl:e,instrumentName:this.name,reason:"currency_changed"})}async handleShippingAddressChange(e,n){var r,a;let i=await $e({cartClient:this.cartClient,cartId:e,streetAddress:n?ar(n):{},instrumentName:f.GooglePay,abortSignal:(r=A(this,te))==null?void 0:r.signal});if(!i.data)return this.showPaymentDataRequestError({intent:"SHIPPING_ADDRESS"});if(this.cart=i.data,i.abortReason==="out_of_stock")return this.showPaymentDataRequestError({error:{intent:"SHIPPING_ADDRESS",reason:"OTHER_ERROR",message:this.i18n.translate("error_dialogs.product.out_of_stock")}});if(i.decelerationReason)return M({checkoutUrl:(a=this.cart.checkoutUrl)!=null?a:"",instrumentName:this.name,reason:i.decelerationReason}),this.showPaymentDataRequestError({error:{intent:"SHIPPING_ADDRESS",reason:"OTHER_ERROR",message:""}});const o=this.cart.deliveryGroups.map(({selectedDeliveryOption:s,deliveryOptions:l})=>{var u;return s!=null&&s.handle&&s.deliveryMethodType!=="LOCAL"?s.handle:(u=l.filter(({deliveryMethodType:d})=>d==="SHIPPING")[0])==null?void 0:u.handle}).filter(Boolean);if(o.length>0){if(i=await this.handleDeliveryOptionChange(o),!i.data)return this.showPaymentDataRequestError({intent:"SHIPPING_ADDRESS"});this.cart=i.data}return this.handleCurrencyChange(this.cart.checkoutUrl,this.cart.totalAmount),this.handlePaymentDataRequestUpdate({result:i,intent:"SHIPPING_ADDRESS"})}async handleDeliveryOptionChange(e){var n;const r=Oa({deliveryGroups:this.cart.deliveryGroups,instrumentName:f.GooglePay,selectedDeliveryOptionHandles:e});return await ka({cartClient:this.cartClient,cartId:this.cart.id,instrumentName:f.GooglePay,selectedDeliveryOptions:r,abortSignal:(n=A(this,te))==null?void 0:n.signal})}logException(e,{severity:n,metadata:r}={}){b.notify(new ir(e),{severity:n!=null?n:"error",metadata:r})}emitAuthorizationCompleteMetric(e){y.authorizationComplete({instrument:this.name,measurement:Y(k.AuthorizationLatency,this.name),result:e})}}pt=new WeakMap,te=new WeakMap,qe=new WeakMap;function Md(t,e){switch(e){case q.VariantRequiresSellingPlan:case q.SellingPlanNotApplicable:case q.InvalidQuantity:case q.MerchandiseIdInvalid:return t.translate("error_dialogs.checkout.generic_error");default:return t.translate("error_dialogs.checkout.generic_error")}}const Ud={LIGHT:"light",DARK:"dark"};class Bd extends fe{constructor(){super(...arguments),c(this,"name",f.GooglePay),c(this,"paymentsClient",null),c(this,"button",null),c(this,"handleClick",async()=>{if(this.disabled)return;this.disabled=!0;const e=Re("AbortController");try{await this.createPaymentsClient(e),await this.paymentsClient.handleClick()}catch(n){if(this.disabled=!1,n.statusCode==="CANCELED"){e==null||e.abort("[Google Pay] Payment sheet cancelled"),y.sheetCancelled(f.GooglePay);return}e==null||e.abort("[Google Pay] Payment sheet failure"),b.notify(n),y.sheetClicked({instrument:this.name,result:"failed"});const r=await this.i18n,a=r.translate("brand.google_pay");N(a,r)}})}static get observedAttributes(){return["disabled"]}attributeChangedCallback(e,n,r){super.attributeChangedCallback(e,n,r,this.button)}async connectedCallback(){this.cleanupOnFailure(this.render.bind(this),this.name)}async createPaymentsClient(e){this.paymentsClient=new xd({walletParams:this.walletParams,isShippingRequired:this.isShippingRequired,hasSellingPlan:this.hasSellingPlan,dataSource:await this.dataSource,button:this,cartClient:await this.apiClient,i18n:await this.i18n,buyerCurrency:this.buyerCurrency,abortController:e})}async render(){const e=this.getOrCreateShadowRoot();if(this.button)return;await this.initializeShadowStyles(e,Cd);const{translate:n}=await this.i18n,r=this.isCTA?n("buy_with_button_content",{wallet:n("brand.google_pay")}):n("brand.google_pay"),a=$t({label:r});this.disabled&&a.setAttribute("disabled",""),a.classList.add("button",Te.BUTTON,Ud[this.buttonTheme]),a.onclick=this.handleClick,await this.renderButtonContent(a,n),this.button=a,e.appendChild(a),this.ensureLightDOMIsNotEmpty(),this.onRendered()}async renderButtonContent(e,n){const r=dt({instrumentName:f.GooglePay,logoType:this.buttonTheme}),a=new DOMParser().parseFromString(r,"image/svg+xml").documentElement;if(e.style.font=window.getComputedStyle(this).font,this.isCTA){const i=Mr({translate:n,logoElement:a,setAriaHidden:!0});i.classList.add("content"),e.appendChild(i)}else e.classList.add("logo-only"),e.appendChild(a)}}const Se=class Gt extends be{static walletName(){return"google_pay"}constructor(e){super(e)}getWebComponentName(){return"shopify-google-pay-button"}getInstrumentName(){return f.GooglePay}getWebComponentClass(){return Bd}async loadWalletSDK(){if(Gt.googlePaySDKPromise)return Gt.googlePaySDKPromise;const e=document.createElement("script");e.setAttribute("src",this.walletParams.sdkUrl);const n=new Promise((r,a)=>{e.onload=()=>r(),e.onerror=i=>{document.body.removeChild(e),a(i)},document.body.appendChild(e)});return Gt.googlePaySDKPromise=Promise.all([n,this.loadLogos()]),Gt.googlePaySDKPromise}getLoadEligibility(){const e=Ha(this.walletParams);switch(e.result){case"success":return{eligible:!0};case"error":return{eligible:!1,reason:e.reason}}}getPartnerSDKEligibility(){return{eligible:!0}}async loadLogos(){return Promise.all([this.loadGooglePayLogo(),this.loadGooglePayLogoDark()])}async loadGooglePayLogo(){const e=await import("./google-pay-logo-BgkSiJGq.js");if(e)Ke({instrumentName:f.GooglePay,logoType:"LIGHT",logoContent:e.default});else throw new Error("Google Pay logo is empty")}async loadGooglePayLogoDark(){const e=await import("./google-pay-logo-dark-DmTdfep2.js");if(e)Ke({instrumentName:f.GooglePay,logoType:"DARK",logoContent:e.default});else throw new Error("Google Pay logo dark is empty")}};c(Se,"googlePaySDKPromise");let yr=Se;const Fd=[Ac,dr,Br,yr,pr,mr,$a],$d=new Map(Fd.map(t=>[t.walletName(),e=>new t(e)]));function lt(t){if(!t)return null;const e=$d.get(t.name);return e?e(t):(console.debug("Can't build wallet with name ".concat(t.name)),null)}function zd(t){return t.map(e=>lt(e)).filter(e=>e!=null)}const Ga="*{box-sizing:border-box}.wallet-button-fade-in{animation:animation-fade-in .3s cubic-bezier(.1,.79,1,1)}@keyframes animation-fade-in{0%{opacity:0}to{opacity:1}}button[aria-disabled=true]{opacity:.5;cursor:not-allowed}";class Hd{constructor(e,n,r){this.element=e,this.apiClient=n,this.instrumentName=r}async getInitialCart(e){const n=Lr(this.element);if(!n)throw new Error("[".concat(this.instrumentName,"] unable to find product form"));const r=ct(hi),{data:a,errors:i,abortReason:o,decelerationReason:s}=await this.apiClient.createCart({...n,discountCodes:r?[r]:[],instrumentName:e});return{cart:a,errors:i,abortReason:o,decelerationReason:s}}}function Gd({styleId:t,styles:e,cssFileName:n}){var r,a;(a=(r=window.Shopify)==null?void 0:r.PaymentButton)!=null&&a.isStorefrontPortableWallets&&(Vd({styleId:t})||qd({styleId:t,styles:e}))}function Vd({styleId:t}){return!!document.querySelector("style#".concat(t))}function qd({styleId:t,styles:e}){const n=document.createElement("style");n.id=t,n.innerHTML=e,document.head.appendChild(n)}const Q={buttonBlockSize:"--shopify-accelerated-checkout-button-block-size",buttonBorderRadius:"--shopify-accelerated-checkout-button-border-radius",buttonBoxShadow:"--shopify-accelerated-checkout-button-box-shadow",inlineAlignment:"--shopify-accelerated-checkout-inline-alignment"};function _n(t){let e=0,n=0,r=0;const a=t.split(/\s+/);for(let i=0;i<a.length;i++){const o=a[i];o!==">"&&(o.includes("#")&&(e+=(o.match(/#/g)||[]).length),o.includes(".")&&(n+=(o.match(/\./g)||[]).length),o.includes("[")&&(n+=(o.match(/\[/g)||[]).length),o.includes(":")&&(n+=(o.match(/:[^:]/g)||[]).length),/^[A-Za-z]/.test(o)&&(r+=1))}return[e,n,r]}function Wd(t,e){for(let n=0;n<3;n++)if(t[n]!==e[n])return e[n]-t[n];return 0}function En(t){return[...t].sort((e,n)=>{const r=_n(e.selector),a=_n(n.selector);return Wd(r,a)})}const jd=/\.cart__dynamic-checkout-buttons|\.dynamic-checkout-buttons\s*(>\s*)?li(?![a-zA-Z0-9_.:#-])/,Yd=/\.cart__dynamic-checkout-buttons|\.dynamic-checkout-buttons\s*\[role="?button"?\](?![:\w-])/,Kd=/\.additional-checkout-buttons\s*(?:div\s*)?\[role="?button"?\](?![:\w-])/,Va=/\[data-shopify-buttoncontainer\](?![:\w-])/,qa=/.dynamic-checkout-buttons .shopify-payment-button__button/,Qd=/(?!.*\.shopify-cleanslate)\.shopify-payment-button__button(?:--branded)?(?![\w-:.#>])/,Jd=/\.(shopify-payment-button|shopify-payment-button__button|shopify-payment-button__button--branded)\s*\[role="?button"?\](?![:\w-])/,Xd=/(?:only\s+)?(?:screen\s+and\s+)?\((?:min|max)-(?:width|height):\s*\d+px\)/,Zd=[Yd,Va,jd,Kd,qa],eu=[Qd,Jd,qa],tu=.001;function Wa(t){const e=t.pageType,n=[...document.styleSheets].filter(ru),r=[],a=[];n.forEach(R=>{try{const B=[...R.cssRules].filter(L=>L instanceof CSSMediaRule),F=[...R.cssRules].filter(L=>L instanceof CSSStyleRule);a.push(...B.filter(L=>Xd.test(L.conditionText)).flatMap(L=>[...L.cssRules].filter(ie=>ie instanceof CSSStyleRule).flatMap(ie=>ie.selectorText.split(",").map(se=>se.trim()).map(se=>({selector:se,conditionText:L.conditionText,...Pn(ie)}))))),r.push(...F.flatMap(L=>L.selectorText.split(",").map(ie=>ie.trim()).map(ie=>({selector:ie,...Pn(L)}))))}catch(B){Ct()("shopify-support-debug")&&(console.debug("[shopify-support-debug] stylesheet origin: ".concat(R.href)),console.debug(B))}});const i=e===P.ProductPage?eu:Zd,o=new Set;function s(R,B){const F=B.selector.match(R);return F?(o.add(F[0]),!0):!1}const l=r.filter(R=>i.some(B=>s(B,R))).reverse(),u=a.filter(R=>i.some(B=>s(B,R))).reverse(),d=Ct()("shopify-support-debug");d&&console.table(Array.from(o));const h=En(l),p=En(u),m={},_=An(e)?document.querySelector(".cart__blocks .button"):document.querySelector(".product-form__buttons .button");_&&(m.boxShadow=getComputedStyle(_,":before").boxShadow);for(const R of h){const B=["height","minHeight","borderRadius","marginTop"];An(e)&&R.selector.match(Va)&&B.push("justifyContent");for(const F of B)m[F]=iu({rule:R,property:F,currentPropertyValue:m[F]})}const{height:w,borderRadius:E,minHeight:S,marginTop:z,justifyContent:O,boxShadow:oe}=m;return d&&console.table([{property:"height",value:w},{property:"borderRadius",value:E},{property:"minHeight",value:S},{property:"marginTop",value:z},{property:"justifyContent",value:O},{property:"boxShadow",value:oe}]),e===P.ProductPage?In({styles:{height:w,minHeight:S,borderRadius:E,marginTop:z,boxShadow:oe},mediaRules:p,element:t}):In({styles:{borderRadius:E,boxShadow:oe,justifyContent:O},mediaRules:p,element:t})}function ja(t,e){if(!e)return;su(e,t);const n="global-".concat(t.tagName.toLowerCase(),"-styles");if(document.head.querySelector("style#".concat(n))==null){const r=document.createElement("style");r.id=n,r.innerHTML=e,document.head.appendChild(r)}}function ru(t){const e=t.ownerNode instanceof HTMLLinkElement&&(t.ownerNode.getAttribute("crossorigin")==="anonymous"||t.ownerNode.getAttribute("crossorigin")==="");return t.href==null||t.href.startsWith(window.location.origin)||e}function An(t){return t===P.CartPage||t===P.CartAjax}function Cn({height:t,minHeight:e}){let n=t,r=e;return(n!=null&&n.includes("var(".concat(Q.buttonBlockSize))||n==="auto")&&(n=null),(r!=null&&r.includes("var(".concat(Q.buttonBlockSize))||r==="auto")&&(r=null),n===r?n:r&&n?"max(".concat(n,",").concat(r,")"):n||r}function Sn({existingProperties:t,mediaCondition:e,selector:n,styles:r}){const a={...r};for(const i of t)delete a[i];for(const[i,o]of Object.entries(a))o!=null&&o.startsWith("var(".concat(i))&&delete a[i];for(const[i,o]of Object.entries(a))(o==null||o==="")&&delete a[i];return Object.keys(a).length===0?"":nu({mediaCondition:e,selector:n,styles:a})}function nu({mediaCondition:t,selector:e,styles:n}){let r="".concat(t?"@media ".concat(t," { "):"").concat(e," {");for(const[a,i]of Object.entries(n))i&&(r+="\n  ".concat(a,": ").concat(i,";"));return r+="\n}",t&&(r+="}"),r+="\n",r}function In({styles:t,element:e,mediaRules:n}){const{height:r,minHeight:a,borderRadius:i,marginTop:o,justifyContent:s,boxShadow:l}=t,u=e.tagName.toLowerCase(),d=getComputedStyle(e),h=new Set;for(const m of Object.values(Q))d.getPropertyValue(m)&&h.add(m);let p=Sn({existingProperties:h,selector:u,styles:{[Q.buttonBlockSize]:Cn({height:r,minHeight:a}),[Q.buttonBorderRadius]:i,[Q.buttonBoxShadow]:l,[Q.inlineAlignment]:s,"margin-top":o,display:o?"block":void 0}});return n.forEach(m=>{(m.height||m.minHeight||m.borderRadius||m.boxShadow||m.justifyContent||m.marginTop)&&(p+=Sn({existingProperties:h,mediaCondition:m.conditionText,selector:u,styles:{[Q.buttonBlockSize]:Cn({height:m.height,minHeight:m.minHeight}),[Q.buttonBorderRadius]:m.borderRadius,[Q.buttonBoxShadow]:m.boxShadow,[Q.inlineAlignment]:m.justifyContent,"margin-top":m.marginTop,display:m.marginTop?"block":void 0}}))}),p.trim()}function Pn(t){return[["height"],["minHeight","min-height"],["borderRadius","border-radius"],["marginTop","margin-top"],["justifyContent","justify-content"]].reduce((e,[n,r])=>(e[n]=au({rule:t,property:r!=null?r:n}),e),{})}function au({rule:t,property:e}){const n=t.style.getPropertyValue(e);return t.style.getPropertyPriority(e)?"".concat(n," !important"):n||null}function iu({rule:t,property:e,currentPropertyValue:n}){const r=t[e];return!(n!=null&&n.includes("!important"))&&r!=null&&r.includes("!important")?r:n!=null?n:r}function ou(t){const e=/(--[\w-]+)\s*:/g;let n;const r={};for(;(n=e.exec(t))!==null;){const l=n[1];r[l]=(r[l]||0)+1}const a=/@media\s+([^{]+)/g,i=(t.match(a)||[]).length,o=/margin-top\s*:/g,s=(t.match(o)||[]).length;return{properties:r,mediaQueryCount:i,marginTopCount:s}}function su(t,e){var n,r,a;const i=(a=(r=(n=window.ShopifyAnalytics)==null?void 0:n.lib)==null?void 0:r.trekkie)==null?void 0:a.defaultAttributes,o=i==null?void 0:i.shopId,s=i==null?void 0:i.themeId;if(o&&s&&(y.log({body:"Style backwards compatibility used",attributes:{shopId:"".concat(o),themeId:"".concat(s)}}),Math.random()<tu)){const l=ou(t);for(const[u,d]of Object.entries(l.properties))y.styleBackwardsCompatibilityRules({rule:u,count:d,pageType:e.pageType});l.mediaQueryCount>0&&y.styleBackwardsCompatibilityRules({rule:"@media",count:l.mediaQueryCount,pageType:e.pageType}),l.marginTopCount>0&&y.styleBackwardsCompatibilityRules({rule:"margin-top",count:l.marginTopCount,pageType:e.pageType}),y.log({body:"Style backwards compatibility details",attributes:{shopId:"".concat(o),themeId:"".concat(s),pageType:e.pageType,stylesString:t.length>2e3?t.substring(0,2e3):t}})}}const vn=2e3;class Dn extends Error{constructor(){super(...arguments),c(this,"name","AcceleratedCheckoutError")}}var We;const Nt=class Pu extends Nr{constructor(){super(...arguments),c(this,"instanceNumber",++Pu.instanceCount),c(this,"pageType",P.ProductPage),c(this,"formObserver"),c(this,"dataSource"),c(this,"wrapper"),c(this,"shopPromiseEligible",!1),c(this,"didInitiateRender",!1),$(this,We)}static get observedAttributes(){return["access-token","disabled","has-selling-plan","requires-shipping"]}async connectedCallback(){var e,n;super.connectedCallback();try{if((n=(e=this.wrapper)==null?void 0:e.children)!=null&&n.length||this.didInitiateRender)return;if(!this.validateRenderingSurface()){this.clearUI();return}this.instanceNumber===1&&(kr(this),y.initStarted(),Gd({styleId:"shopify-accelerated-checkout",styles:uc,cssFileName:"AcceleratedCheckout.css"}),this.extractStyles()),this.shopPromiseEligible=ma();const r=Ye(k.ButtonDisplay,"dc:".concat(this.instanceNumber));this.i18n=Ft(),this.setupFormObserver(),this.didInitiateRender=!0;const a=await this.loadWallet(),i=a.getInstrumentName();this.apiClient=new Bt({accessToken:this.accessToken,country:this.buyerCountry,locale:this.i18n.locale,withCarrierRates:!0,cartPrepareMigrationEnabled:this.isFlagEnabled(Aa)}),this.dataSource=new Hd(this,this.apiClient,i);const o=await this.getWebComponentInstance({wallet:a,instrumentName:i,dataSource:this.dataSource,i18n:this.i18n,apiClient:this.apiClient,buttonDisplayBenchmark:r});this.render(o,i),y.initCompleted(),this.instanceNumber===1&&this.triggerLoadedEvent()}catch(r){if(this.clearUI(),y.initCompleted(r),r.name===mt)throw r;b.notify(new Dn("An error occurred while attempting to render AcceleratedCheckout: ".concat(r),{cause:r}))}}async attributeChangedCallback(e,n,r){n!==r&&(Tr(this,e,r),e==="has-selling-plan"&&r===""?this.showBuyerConsent(this.apiClient,this.i18n):e==="has-selling-plan"&&r===null&&this.hideBuyerConsent())}async getWebComponentInstance({wallet:e,instrumentName:n,dataSource:r,i18n:a,apiClient:i,buttonDisplayBenchmark:o}){if(this.hidePaymentButton){y.log({body:"Payment button hidden for instrument ".concat(n),attributes:{instrument:n,pageType:this.pageType,containerInstanceNumber:this.instanceNumber,hidePaymentButton:this.hidePaymentButton,isRedirectedFromAmazon:this.isRedirectedFromAmazon,isBuyWithPrimeEligible:this.isBuyWithPrimeEligible}}),y.amazonAdExperimentPaymentButtonHidden({instrument:n});return}const s=await e.createWebComponent({walletContainer:this,dataSource:r,i18n:a,apiClient:i,classNames:lu(n),callToAction:"true",pageType:this.pageType,containerInstanceNumber:this.instanceNumber,onRendered:()=>y.instrumentLoaded({instrumentOrComponentName:n,result:"success",measurement:o()})});if(!s)throw new Dn("No web component instance found for instrument ".concat(n));return s}async render(e,n){this.clearUI();const r=this.getOrCreateShadowRoot(),a=document.createElement("style");a.innerHTML=Ga,r.appendChild(a),this.wrapper=document.createElement("div"),this.wrapper.className="wallet-button-fade-in wallet-button-wrapper";const i=document.createElement("slot");i.name="button",this.wrapper.appendChild(i);const o=document.createElement("slot");o.name="promise",this.wrapper.appendChild(o);const s=document.createElement("slot");s.name="more-options",this.wrapper.appendChild(s),r==null||r.appendChild(this.wrapper),this.hasSellingPlan?this.showBuyerConsent(this.apiClient,this.i18n):this.hideBuyerConsent();const l=new DocumentFragment;e&&l.appendChild(e);const u=this.getShopPromiseSlot();u&&l.appendChild(u);const d=this.getMorePaymentOptionsLink(n);d&&l.appendChild(d),this.appendChild(l)}getMorePaymentOptionsLink(e){var n,r;if(e===f.BuyItNow)return null;const a=document.createElement("more-payment-options-link");return zt(a,{"buyer-country":(n=this.buyerCountry)!=null?n:"","access-token":(r=this.accessToken)!=null?r:"","recommended-instrument":e,slot:"more-options"}),a.disabled=this.disabled,a.setDataSource(this.dataSource),a.setI18n(this.i18n),a.setClassNames(or.MORE_PAYMENT_OPTION_BUTTON),a}getShopPromiseSlot(){if(!this.shopPromiseEligible)return null;const e=document.createElement("div");return e.setAttribute("data-shopify","shop-promise-payment-button-slot"),e.style.height="100%",e.slot="promise",e}validateRenderingSurface(){return Ht({page:"product",element:this})!=null}async loadWallet(){const e=this.getRecommendedWallet(),n=dc(e,this.fallbackWallet,"AcceleratedCheckout");return await du(this,{...n,instanceNumber:this.instanceNumber})}getRecommendedWallet(){return this.amazonWallet&&this.isRedirectedFromAmazon?(y.log({body:"Recommended wallet overridden to AmazonPay",attributes:{pageType:this.pageType,containerInstanceNumber:this.instanceNumber,isRedirectedFromAmazon:this.isRedirectedFromAmazon,isBuyWithPrimeEligible:this.isBuyWithPrimeEligible,recommendedWallet:this.recommendedWallet,amazonWallet:this.amazonWallet}}),y.amazonAdExperimentRecommendedWalletOverriden({instrument:f.AmazonPay}),this.amazonWallet):this.recommendedWallet}setupFormObserver(){this.formObserver||(this.formObserver=new Or(this,({disabled:e,hasSellingPlan:n})=>{this.disabled=e,this.hasSellingPlan=n,this.isShippingRequired=cu(this,this.variantParams)}),this.formObserver.setupMutationObservers())}getOrCreateShadowRoot(){return A(this,We)||x(this,We,this.attachShadow({mode:"closed"})),A(this,We)}extractStyles(){if(this.styleExtractorDisabled){Ct()("shopify-support-debug")&&console.debug("[shopify-support-debug] styling backward compatibility disabled"),y.styleBackwardsCompatibility({usedBackwardsCompatibility:!1});return}const e=Ye(k.StyleExtract,"dc:".concat(this.instanceNumber)),n=Wa(this);n&&ja(this,n);const r=e();r&&y.styleBackwardsCompatibilityExecutionTime({measurement:r,pageType:this.pageType,stylesWritten:!!n}),y.styleBackwardsCompatibility({usedBackwardsCompatibility:!!n})}};We=new WeakMap,c(Nt,"instanceCount",0);let gr=Nt;function lu(t){return t===f.BuyItNow?or.UNBRANDED_BUTTON:or.BRANDED_BUTTON}function cu(t,e){var n;const r=Lr(t);return!!((n=e.find(a=>a.id===Number(r==null?void 0:r.variantId)))!=null&&n.requiresShipping)}async function du(t,{recommendedWallet:e,fallbackWallet:n,instanceNumber:r}){const a=e?lt(e):null,i=lt(n),o=async s=>{const l=Ea({walletInstrument:s,instanceNumber:r}),u="timeout",d=await Promise.race([l,new Promise(h=>setTimeout(()=>h(u),vn))]);if(d===u)y.instrumentSDKLoaded({instrument:s.getInstrumentName(),measurement:vn,result:"timeout"});else if(d instanceof be)return d;return null};if(St(t,a)){const s=await o(a);if(s)return s}if(St(t,i)){const s=await o(i);if(s)return s}return lt(zn)}ae("shopify-accelerated-checkout",gr),ae("more-payment-options-link",tc,{isChildCustomElement:!0});const uu=t=>{if(typeof PerformanceObserver>"u")return null;const e=new PerformanceObserver(n=>{n.getEntriesByType("resource").some(r=>r.name.includes(rt.CartUpdate)||r.name.includes(rt.CartChange)||r.name.includes(rt.CartAdd)||r.name.includes(rt.CartClear))&&t()});return e.observe({entryTypes:["resource"]}),e},hu=".wallet-cart-button__skeleton{animation:acceleratedCheckoutLoadingSkeleton var(--shopify-accelerated-checkout-skeleton-animation-duration, 4s) var(--shopify-accelerated-checkout-skeleton-animation-timing-function, ease) infinite;animation-delay:-.168s;background-color:var(--shopify-accelerated-checkout-skeleton-background-color, #dedede)}.wallet-button-wrapper{container-type:inline-size;container-name:wrapper;width:100%}.wallet-cart-grid{margin:0 -5px -5px;padding:0;display:flex;flex-direction:row;justify-content:var(--shopify-accelerated-checkout-inline-alignment, start)}.wallet-cart-button-container{position:relative;margin:0 5px 5px}.wallet-cart-button-container,.wallet-cart-button{width:auto;flex:1 1 0px;min-width:100px;height:clamp(25px,var(--shopify-accelerated-checkout-button-inline-size, 42px),55px);border-radius:var(--shopify-accelerated-checkout-button-border-radius, 4px);list-style-type:none!important;text-align:center}.wallet-cart-grid.wallet-cart-grid--vertical,.additional-checkout-buttons--vertical .wallet-cart-grid{justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button-container,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button-container:first-child,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}.wallet-cart-grid.wallet-cart-grid--horizontal,.additional-checkout-buttons--horizontal .wallet-cart-grid{justify-content:stretch}.wallet-cart-grid.wallet-cart-grid--horizontal .wallet-cart-button-container,.additional-checkout-buttons--horizontal .wallet-cart-grid .wallet-cart-button-container,.wallet-cart-grid.wallet-cart-grid--horizontal .wallet-cart-button,.additional-checkout-buttons--horizontal .wallet-cart-grid .wallet-cart-button{min-width:0}@container wrapper (width >= 100px) and (width <= 500px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 210px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 320px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 430px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 540px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button-container:first-child{margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}@media screen and (max-width: 750px){.wallet-cart-grid{justify-content:stretch}.wallet-cart-button-container,.wallet-cart-button{min-width:0}}@supports (not (container-type: inline-size)) or (not (selector(:has(*)))){.wallet-cart-grid{justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-button-container:first-child{margin:0}.wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}.screen-reader-text{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}",pu=".wallet-cart-button__skeleton{animation:acceleratedCheckoutLoadingSkeleton var(--shopify-accelerated-checkout-skeleton-animation-duration, 4s) var(--shopify-accelerated-checkout-skeleton-animation-timing-function, ease) infinite;animation-delay:-.168s;background-color:var(--shopify-accelerated-checkout-skeleton-background-color, #dedede)}.wallet-button-wrapper{container-type:inline-size;container-name:wrapper;width:100%}.wallet-cart-grid{margin:0 -5px -5px;padding:0;display:flex;flex-direction:row;justify-content:var(--shopify-accelerated-checkout-inline-alignment, start)}.wallet-cart-button-container{position:relative;margin:0 5px 5px}.wallet-cart-button-container,.wallet-cart-button{width:auto;flex:1 1 0px;min-width:100px;height:clamp(25px,var(--shopify-accelerated-checkout-button-inline-size, 42px),55px);border-radius:var(--shopify-accelerated-checkout-button-border-radius, 4px);list-style-type:none!important;text-align:center}.wallet-cart-grid.wallet-cart-grid--vertical,.additional-checkout-buttons--vertical .wallet-cart-grid{justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button-container,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button-container:first-child,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid.wallet-cart-grid--vertical .wallet-cart-button,.additional-checkout-buttons--vertical .wallet-cart-grid .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}.wallet-cart-grid.wallet-cart-grid--horizontal,.additional-checkout-buttons--horizontal .wallet-cart-grid{justify-content:stretch}.wallet-cart-grid.wallet-cart-grid--horizontal .wallet-cart-button-container,.additional-checkout-buttons--horizontal .wallet-cart-grid .wallet-cart-button-container,.wallet-cart-grid.wallet-cart-grid--horizontal .wallet-cart-button,.additional-checkout-buttons--horizontal .wallet-cart-grid .wallet-cart-button{min-width:0}@container wrapper (width >= 100px) and (width <= 500px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(1)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(2))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 210px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(2)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(3))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 320px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(3)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(4))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 430px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(4)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(5))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}@container wrapper (width <= 540px){.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(5)).wallet-cart-grid:not(:has(>.wallet-cart-button-container:nth-of-type(6))) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)){justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-grid:not(.wallet-cart-grid--horizontal):has(>.wallet-cart-button-container:nth-of-type(6)) .wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}@media screen and (max-width: 750px){.wallet-cart-grid{justify-content:stretch}.wallet-cart-button-container,.wallet-cart-button{min-width:0}}@supports (not (container-type: inline-size)) or (not (selector(:has(*)))){.wallet-cart-grid{justify-content:flex-start;flex-direction:column;margin:0}.wallet-cart-button-container{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px);margin:var(--shopify-accelerated-checkout-row-gap, 8px) 0 0 0}.wallet-cart-button-container:first-child{margin:8px 0 0}.wallet-cart-button{width:100%;height:clamp(25px,var(--shopify-accelerated-checkout-button-block-size, 54px),55px)}}.screen-reader-text{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}";function mu(t){const e=Ht({page:"cart",element:t});return Ca({type:"attributes",form:e})}class yu{constructor(e,n,r){this.element=e,this.cartToken=n,this.apiClient=r}async getInitialCart(e){const n="gid://shopify/Cart/".concat(this.cartToken),r=mu(this.element);let a,i=[];if(r.length){const o=await this.apiClient.updateCartAttributes({cartId:n,attributes:r,instrumentName:e,startingCheckout:!0});a=o.data,i=o.errors}else a=await this.apiClient.fetchCart({id:n,instrumentName:e,startingCheckout:!0});return{cart:a,errors:i}}}var je;const kt=class Du extends Nr{constructor(){super(),c(this,"instanceNumber",++Du.instanceCount),c(this,"pageType",P.CartPage),c(this,"cartObserver",null),c(this,"dataSource",null),c(this,"wrapper"),c(this,"didInitiateRender",!1),c(this,"amazonAdExperimentCheckoutButtonListeners",[]),$(this,je)}static get observedAttributes(){return["access-token","has-selling-plan","requires-shipping"]}createDataSource(){if(!this.cartToken){y.cartTokenMissing({reason:"createDataSource"});return}this.i18n=Ft(),this.apiClient=new Bt({accessToken:this.accessToken,country:this.buyerCountry,locale:this.i18n.locale,withCarrierRates:!0,cartPrepareMigrationEnabled:this.isFlagEnabled(Aa)}),this.dataSource=new yu(this,this.cartToken,this.apiClient)}async createWebComponents({shouldRefetchCartAttributes:e}){if(!this.walletConfigs)throw new J({code:"invalid-wallet-configs",message:"[AcceleratedCheckoutCart] walletConfigs does not exist"});const n=rc(this.walletConfigs,"AcceleratedCheckoutCart");if(!(n instanceof Array))throw new J({code:"invalid-wallet-configs",message:"[AcceleratedCheckoutCart] invalid walletConfigs found"});const r=k.ButtonDisplay,a=o=>"".concat(o,":").concat(this.instanceNumber),i=await gu(this,{walletConfigs:n,startButtonDisplayBenchmark:o=>Je(r,a(o))});return this.createDataSource(),this.dataSource?(e&&await this.updateAttributes(),await Promise.all(i.map(async o=>{const s=o.getInstrumentName();return await o.createWebComponent({walletContainer:this,dataSource:this.dataSource,i18n:this.i18n,apiClient:this.apiClient,pageType:this.pageType,containerInstanceNumber:this.instanceNumber,slot:"button-".concat(o.getInstrumentName()),onRendered:(()=>{let l=!1;return()=>{l||(l=!0,y.instrumentLoaded({instrumentOrComponentName:s,result:"success",measurement:Y(r,a(s))}))}})()})}))):[]}async cartChangedCallback(){try{if(!this.dataSource&&this.cartToken){await this.render({shouldRefetchCartAttributes:!0}),y.initCompleted();return}else if(!this.cartToken){y.cartTokenMissing({reason:"cartChangedCallback"});return}await this.updateAttributes()}catch(e){this.clearUI(),b.notify(e)}}async updateAttributes(){var e,n;if(!this.cartToken||!this.apiClient)return;const r=await this.apiClient.fetchLimitedCart({id:"gid://shopify/Cart/".concat(this.cartToken),instrumentName:f.Unknown});this.hasSellingPlan=(e=r.sellingPlan)!=null?e:!1,this.isShippingRequired=(n=r.shippingRequired)!=null?n:!1}async connectedCallback(){super.connectedCallback();try{if(this.didInitiateRender)return;if(this.instanceNumber===1&&kr(this),y.initStarted(),this.cartObserver=uu(()=>this.cartChangedCallback()),!this.cartObserver){y.log({body:"Cart observer unavailable - PerformanceObserver not supported",attributes:{userAgent:navigator.userAgent}}),this.clearUI();return}if(!this.cartToken){y.cartTokenMissing({reason:"connectedCallback"});return}await this.render({shouldRefetchCartAttributes:!1}),y.initCompleted()}catch(e){this.clearUI(),y.initCompleted(e),b.notify(e)}}disconnectedCallback(){var e;(e=this.cartObserver)==null||e.disconnect(),this.isAmazonAdExperimentEnabled()&&this.amazonAdExperimentRemoveCheckoutButtonListeners()}async attributeChangedCallback(e,n,r){n!==r&&(Tr(this,e,r),e==="has-selling-plan"&&r===""?this.showBuyerConsent(this.apiClient,this.i18n):e==="has-selling-plan"&&r===null&&this.hideBuyerConsent())}get cartToken(){return ct("cart")}async render({shouldRefetchCartAttributes:e}){var n;if(this.hasLegacyCartCookie()){y.legacyCartCookie(),this.clearUI();return}this.didInitiateRender=!0;const r=Ye(k.ButtonDisplay,"cart:".concat(this.instanceNumber)),a=await this.createWebComponents({shouldRefetchCartAttributes:e});if(this.clearUI(),a.length===0)return;try{if(this.isAmazonAdExperimentEnabled()){const d=a.find(p=>p.name===f.BuyWithPrime),h=a.find(p=>p.name===f.AmazonPay);y.log({body:"available wallets for amazon ad experiment",attributes:this.getTelemetryAttributes({buyWithPrimeCheckoutButton:!!d,amazonPayCheckoutButton:!!h})}),d?this.accelerateCheckoutButtonsForAmazonAdExperiment(d):h?this.accelerateCheckoutButtonsForAmazonAdExperiment(h):y.amazonAdExperimentAcceleratedCheckoutButtonSetup({instrument:f.Unknown,result:"failed"})}}catch(d){y.log({body:"failed to accelerate checkout buttons for amazon ad experiment",attributes:this.getTelemetryAttributes({error:d instanceof Error?d.message:String(d)})}),b.notify(d,{context:"failed to accelerate checkout buttons for amazon ad experiment"})}const i=new DocumentFragment,o=this.getOrCreateShadowRoot(),s=document.createElement("style");s.innerHTML=[Ga,hu].join("\n"),o.appendChild(s),this.extractStyles(),this.wrapper=document.createElement("div"),this.wrapper.className="wallet-button-fade-in wallet-button-wrapper";const l=document.createElement("h2");l.className="screen-reader-text",l.textContent=((n=this.i18n)==null?void 0:n.translate("express_checkout"))||"",this.wrapper.appendChild(l);const u=document.createElement("div");u.className="wallet-cart-grid ".concat(Te.CONTAINER),Object.entries(fu).forEach(([d,h])=>{document.querySelector(d)&&u.classList.add(h)}),a.forEach(d=>{const h=document.createElement("div"),p=document.createElement("div"),m=document.createElement("slot");m.name="button-".concat(d.name),h.className="wallet-cart-button-container",p.className="wallet-cart-button",h.setAttribute("data-testid","grid-cell"),h.appendChild(p),p.appendChild(m),u.appendChild(h),i.appendChild(d)}),this.wrapper.appendChild(u),this.hasSellingPlan?this.showBuyerConsent(this.apiClient,this.i18n):this.hideBuyerConsent(),o.appendChild(this.wrapper),this.appendChild(i),y.instrumentLoaded({instrumentOrComponentName:"AcceleratedCheckoutCart",result:"success",measurement:r()})}getTelemetryAttributes(e={}){return{isAmazonAdExperimentFlagEnabled:this.isFlagEnabled(Oe),amazonReferralSessionStorageValue:sessionStorage.getItem("shopify_amazon_referral"),cartToken:this.cartToken,cartInstance:this.instanceNumber,hasDataSource:!!this.dataSource,...e}}isAmazonAdExperimentEnabled(){return this.isFlagEnabled(Oe)&&sessionStorage.getItem("shopify_amazon_referral")==="mShop"}accelerateCheckoutButtonsForAmazonAdExperiment(e){try{const n=document.getElementsByName("checkout");if(n.length<=0)throw y.log({body:"attempted to accelerate checkout buttons, but none were found",attributes:this.getTelemetryAttributes({walletName:e.name})}),new Error("No checkout buttons found");n.forEach(r=>{const a=async i=>{if(i.preventDefault(),!this.dataSource){this.amazonAdExperimentFallbackToDefaultCheckout(r,e.name);return}try{const{cart:o}=await this.dataSource.getInitialCart(e.name),s=Pr(o);if(!o||!this.accessToken||!s){this.amazonAdExperimentFallbackToDefaultCheckout(r,e.name,"checkout button was clicked but was missing required data to validate ".concat(e.name," eligibility:\n                cart: ").concat((o==null?void 0:o.id)||"missing",",\n                accessToken: ").concat(this.accessToken||"missing",",\n                checkoutToken: ").concat(s||"missing"));return}try{await zr({configOptionsEndpoint:e.name===f.BuyWithPrime?Rt.OPTIMUS_CONFIG_REST_API_URL:Tt.CV2_CONFIG_REST_API_URL,checkoutUrl:o.checkoutUrl,sourceId:s,accessToken:this.accessToken,instrument:e.name});try{e.triggerCheckout(),y.amazonAdExperimentAcceleratedCheckoutButtonClicked({instrument:e.name,result:"success"}),y.log({body:"checkout button was clicked and accelerated",attributes:this.getTelemetryAttributes({walletName:e.name})})}catch(l){this.amazonAdExperimentFallbackToDefaultCheckout(r,e.name,"checkout button was clicked but failed to accelerate",l)}}catch(l){this.amazonAdExperimentFallbackToDefaultCheckout(r,e.name,"checkout button was clicked but failed to fetch wallets platform config, falling back to guest checkout:\n                ".concat(l instanceof Error?"".concat(l.name,": ").concat(l.message):String(l)))}}catch(o){this.amazonAdExperimentFallbackToDefaultCheckout(r,e.name,"checkout button was clicked but was missing required data to validate line item eligibility, falling back to guest checkout",o)}};r.addEventListener("click",a,!0),this.amazonAdExperimentCheckoutButtonListeners.push({button:r,listener:a})}),y.amazonAdExperimentAcceleratedCheckoutButtonSetup({instrument:e.name,result:"success"})}catch(n){y.amazonAdExperimentAcceleratedCheckoutButtonSetup({instrument:e.name,result:"failed"})}}amazonAdExperimentFallbackToDefaultCheckout(e,n,r,a){y.amazonAdExperimentAcceleratedCheckoutButtonClicked({instrument:n,result:"failed"}),r&&(y.log({body:r,attributes:this.getTelemetryAttributes({walletName:n})}),a&&b.notify(a,{context:r}));const i=this.amazonAdExperimentCheckoutButtonListeners.find(o=>o.button===e);if(i){e.removeEventListener("click",i.listener,!0),this.amazonAdExperimentCheckoutButtonListeners=this.amazonAdExperimentCheckoutButtonListeners.filter(s=>s.button!==e);const o=new Event("click",{bubbles:!0,cancelable:!0});typeof e.click=="function"?e.click():e.dispatchEvent(o)}y.amazonAdExperimentAcceleratedCheckoutButtonClicked({instrument:f.BuyItNow,result:"success"})}amazonAdExperimentRemoveCheckoutButtonListeners(){try{this.amazonAdExperimentCheckoutButtonListeners.forEach(({button:e,listener:n})=>{e.removeEventListener("click",n,!0)}),this.amazonAdExperimentCheckoutButtonListeners=[]}catch(e){b.notify(e,{context:"failed to remove checkout button event listeners"})}}getOrCreateShadowRoot(){return A(this,je)||x(this,je,this.attachShadow({mode:"closed"})),A(this,je)}hasLegacyCartCookie(){var e;return this.cartToken?(e=this.cartToken)==null?void 0:e.match(/^[a-z0-9]+$/):!1}extractStyles(){if(this.styleExtractorDisabled){Ct()("shopify-support-debug")&&console.debug("[shopify-support-debug] styling backward compatibility disabled"),y.styleBackwardsCompatibility({usedBackwardsCompatibility:!1});return}const e=Ye(k.StyleExtract,"cart:".concat(this.instanceNumber)),n=Wa(this);if(n){ja(this,n);const a=this.getOrCreateShadowRoot(),i=document.createElement("style");i.innerHTML=pu,a.appendChild(i)}const r=e();r&&y.styleBackwardsCompatibilityExecutionTime({measurement:r,pageType:this.pageType,stylesWritten:!!n}),y.styleBackwardsCompatibility({usedBackwardsCompatibility:!!n})}};je=new WeakMap,c(kt,"instanceCount",0);let fr=kt;async function gu(t,{walletConfigs:e,startButtonDisplayBenchmark:n=()=>{}}){const r=ac(t,zd(e));return(await Promise.all(r.map(a=>(n(a.getInstrumentName()),Ea({walletInstrument:a,instanceNumber:t.instanceNumber}))))).filter(a=>a!=null)}const fu={".additional-checkout-buttons--vertical shopify-accelerated-checkout-cart":"wallet-cart-grid--vertical",".additional-checkout-buttons--horizontal shopify-accelerated-checkout-cart":"wallet-cart-grid--horizontal"};ae("shopify-accelerated-checkout-cart",fr);class bu{getInitialCart(){throw new Error('[NoOpDataSource] Unexpected call to "getCart"')}}const Lt=class Nu extends Nr{constructor(){super(...arguments),c(this,"instanceNumber",++Nu.instanceCount),c(this,"pageType",P.Checkout)}get walletConfig(){return this.getAttribute("wallet-config")}async connectedCallback(){var e;super.connectedCallback();try{if(this.children.length)return;this.instanceNumber===1&&(kr(this),y.initStarted());const n=Ye(k.ButtonDisplay,"dc:".concat(this.instanceNumber)),r=nc(this.walletConfig,"WalletButton");if(!r)throw new mi("[WalletButton] No config provided.");const a=lt(r);if(!a)return;if(!await this.eligibleToShow(a)){this.pciEnabled&&this.dispatchEvent(new CustomEvent("wallet-rendered",{detail:{status:"failure",message:"ineligible"}}));return}if(this.onlySdk){y.initCompleted(),this.pciEnabled&&this.dispatchEvent(new CustomEvent("wallet-rendered",{detail:{status:"success"}}));return}const i=a.getInstrumentName();this.i18n=Ft(),this.apiClient=new Bt({accessToken:this.accessToken,country:this.buyerCountry,locale:this.i18n.locale});const o=await this.createWebComponent(a,this.i18n,this.apiClient,i,n);this.innerHTML="",this.appendChild(o),y.initCompleted(),this.instanceNumber===1&&this.triggerLoadedEvent()}catch(n){this.innerHTML="",this.pciEnabled&&this.dispatchEvent(new CustomEvent("wallet-rendered",{detail:{status:"failure",message:(e=n.message)!=null?e:"Error loading wallet SDK"}})),y.initCompleted(n),b.notify(n)}}async createWebComponent(e,n,r,a,i){const o=await e.createWebComponent({walletContainer:this,dataSource:new bu,i18n:n,apiClient:r,containerInstanceNumber:this.instanceNumber,pageType:this.pageType});return o.onRendered=()=>{this.pciEnabled&&this.dispatchEvent(new CustomEvent("wallet-rendered",{detail:{status:"success"}})),y.instrumentLoaded({instrumentOrComponentName:a,result:"success",measurement:i()})},o}async eligibleToShow(e){return!(!St(this,e)||(await e.loadWalletSDK(),!e.getPartnerSDKEligibility().eligible))}};c(Lt,"instanceCount",0);let br=Lt;ae("shopify-wallet-button",br),si(b,{metadata:{custom:{"notify-origin":"window-onerror"}}}),li(b,{metadata:{custom:{"notify-origin":"unhandled-promise-rejection-handler"}}}),y.startExporter();
