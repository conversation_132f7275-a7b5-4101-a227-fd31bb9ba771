class Wheel{constructor(subscribeModule){this.pathname=window.location.origin+location.pathname,this.list=[],this.$p=document.querySelector(subscribeModule),this.main=this.$p.querySelector(".tray-main"),this.temp=this.$p.querySelector(".tray-part"),this.box=this.$p.querySelector(".tray"),this.rotate=0,this.isRunning=!1,this.perAngle=0,this.rotates=0,this.mathDeg=30,this.isShow=!1,this.subscripSuccessData="",this.shopCurrencySymbol="$",this.api="https://api.ulike.com",this.subscribeActFrom={resourceId:"",sourceCode:"",activityItem:"",currentData:"",colorItem:[],languageMessData:"",formActive:{theme:0}},this.subscribeObj={name:"",userEmail:"",phoneNumber:"",activityId:"",utmSource:"",extend:{utm_source:""},isMarketing:!1},this.getInitData(),this.color=[{color:"linear-gradient(180deg, #F0EBFF 0.13%, rgba(255, 255, 255, 1.00) 72.68%)"},{color:"linear-gradient(235deg, rgba(224, 0, 77, 1.00) 23.06%, #FF7DAA 92.89%)"},{color:"linear-gradient(180deg, #F0EBFF 0.13%, rgba(255, 255, 255, 1.00) 72.68%)"},{color:"linear-gradient(260deg, rgba(224, 0, 77, 1.00) -1.58%, #FF7DAA 74.91%)"},{color:"linear-gradient(180deg, #F0EBFF 0.13%, rgba(255, 255, 255, 1.00) 72.68%)"},{color:"linear-gradient(109deg, #FF7DAA 15.96%, rgba(224, 0, 77, 1.00) 89.42%)"}]}getInitData(){this.subscripSuccessData=localStorage.getItem("subscript-success-data");let _subscribeActFrom=this.getCookie("subscript-success-activeFrom");!this.getCookie("subscript-success")||this.subscripSuccessData==null||_subscribeActFrom==""?this.getActiveInitConfig():this.subscripSuccessData&&(this.subscripSuccessData=JSON.parse(this.subscripSuccessData),this.$p.classList.add("dispatch-cont"),this.updateSuccessDom(this.subscripSuccessData),this.fadeIn(document.querySelector(".teaser-app"))),this.init()}getBenefitInfoList(){this.sendApiRequest("/promotion/queryBenefitInfoList",{activityId:this.subscribeObj.activityId,activityType:"SUBSCRIBE_ACTIVITY",includeCount:!1,language:"en",pageNo:1,pageSize:100}).then(res=>{res.code==0&&res.data&&res.data.length>0?(res.data.forEach(item=>{this.$p.querySelector(".subscribe-module-slide-list").innerHTML+=`
                  <div class="subscribe-module-slide-item">${item.userEmail} won <span>${item.benefitName}</span></div>`}),this.fadeIn(this.$p.querySelector(".subscribe-module-slide")),this.verticalCarousel()):this.$p.querySelector(".subscribe-module-slide").style.display="none"}).catch(err=>{console.log(err)})}getActiveInitConfig(){let time=parseInt(new Date().getTime()/1e3);this.sendApiRequest("/promotion/queryTargetInfoList",{targetStatus:"PUBLISH",afterStartTime:time,beforeEndTime:time,targetType:"ACTIVITY",resourceType:"SUBSCRIBE_ACTIVITY"}).then(res=>{if(res.code==0&&res.data.length>0){const currentData=this.getCurrentActivity(res.data);console.log(currentData),this.formatActiveData(currentData),delete currentData.languageMessageDtoList,delete currentData.targetPluginDtoList,this.subscribeActFrom.currentData=currentData,this.subscribeActFrom.resourceId=currentData.resourceId,this.subscribeObj.activityId=this.subscribeActFrom.resourceId,this.list=this.subscribeActFrom.currentData.resourceObject.activityItemDtoList,this.perAngle=360/this.list.length,this.getBenefitInfoList(),this.pageControl(this.subscribeActFrom.formActive),this.render(),this.domOnUpdate()}}).catch(err=>{console.log(err),reject(err)})}getCurrentActivity(data){const firstVisit=this.getCookie("subscript-First-Visit");if(firstVisit){let result=data.find(item=>item.resourceId===firstVisit);if(result)return result}let currentData=data[0];if(data.length>1){let actData=[];for(let index=0;index<data.length;index++){const ths=data[index];let actIndex=-1,_formActive=this.reverseTransform(ths.targetPluginDtoList),hideContent=!1;_formActive.urlViewArr.forEach(item=>{this.pathname.indexOf(item.url)>-1&&this.pathname.length==item.url.length&&(hideContent=!0)}),(_formActive.radioView==0||_formActive.radioView==1&&hideContent||_formActive.radioView==2&&!hideContent)&&(actIndex=index),actData.push({createTime:ths.createTime,radioView:_formActive.radioView,actIndex})}actData.sort((a,b)=>parseInt(a.createTime)-parseInt(b.createTime));let result=actData.find(item=>item.radioView==="1"&&item.actIndex!==-1);if(result||(result=actData.find(item=>item.radioView==="0")),result)return data[result.actIndex];console.error("\u6CA1\u6709\u7B26\u5408\u6761\u4EF6\u7684\u5BF9\u8C61"),this.fadeOut(document.querySelector(".teaser-app"))}return currentData}formatActiveData(data){let languageMessageDtoList={},_activityItem=data.resourceObject.activityItemDtoList;this.subscribeActFrom.formActive=this.reverseTransform(data.targetPluginDtoList);let activityItem=[];for(let i=0;i<_activityItem.length;i++){let _obj=_activityItem[i];activityItem.push({location:i,title:_obj.itemName,url:_obj.extend.prizeUrl||"",itemType:_obj.itemType,bgColor:this.subscribeActFrom.formActive.turntable_prize_arr[i].bg_color,bg2Color:this.subscribeActFrom.formActive.turntable_prize_arr[i].bg2_color,fontSize:this.subscribeActFrom.formActive.turntable_prize_arr[i].font_size,mfontSize:this.subscribeActFrom.formActive.turntable_prize_arr[i].m_font_size,fontColor:this.subscribeActFrom.formActive.turntable_prize_arr[i].font_color,price:_obj.extend.disProductPrice}),this.subscribeActFrom.colorItem.push([this.subscribeActFrom.formActive.turntable_prize_arr[i].bg_color,this.subscribeActFrom.formActive.turntable_prize_arr[i].bg2_color])}this.subscribeActFrom.activityItem=activityItem;for(let index=0;index<data.languageMessageDtoList.length;index++){let _ths=data.languageMessageDtoList[index];languageMessageDtoList[_ths.key]=_ths.value}this.subscribeActFrom.languageMessData=languageMessageDtoList}pageControl(formActive){let hideContent=!1;formActive.urlViewArr.forEach(item=>{this.pathname.indexOf(item.url)>-1&&this.pathname.length==item.url.length&&(hideContent=!0)}),(formActive.radioView==0||formActive.radioView==1&&hideContent||formActive.radioView==2&&!hideContent)&&this.showSubscriptionPopup(formActive),formActive.teaser=="0"&&this.fadeIn(document.querySelector(".teaser-app"))}reverseTransform(transformedData){function restore(entry){const{name,type,value}=entry;if(type==="array"){let arr=value.map(item=>{let simplifiedObject={};return item.value.forEach(innerItem=>{simplifiedObject[innerItem.name]=innerItem.value}),simplifiedObject});return{name,value:arr}}else return type==="object"?value.reduce((acc,item)=>{const restored=restore(item);return acc[restored.name]=restored.value,acc},{}):{name,value}}return transformedData.map(entry=>{const restored=restore(entry),result={};return restored!==void 0&&(result[restored.name]=restored.value),result}).reduce((acc,item)=>Object.assign(acc,item),{})}showSubscriptionPopup(formActive){let visTime="visitor-times"+this.subscribeActFrom.resourceId,visitorDay="visitor-day"+this.subscribeActFrom.resourceId,today=this.getTodayDate();setTimeout(()=>{if(console.log(formActive),formActive.text_device=="0"||!isMobile()&&formActive.text_device=="1"||isMobile()&&formActive.text_device=="2"){let num=this.getCookie(visTime)?parseInt(this.getCookie(visTime))+1:1;if(formActive.viewObj=="1"&&parseInt(formActive.text_viewNum)>=num)this.setCookie(visTime,num),ulikeOpenSubscribe();else if(formActive.viewObj=="2"&&!this.getCookie(visitorDay))this.setCookie(visitorDay,!0,formActive.text_viewDay,!0),ulikeOpenSubscribe();else if(this.shouldDisplay()){ulikeOpenSubscribe();const displayData=JSON.parse(localStorage.getItem("subscript-displayData"))||{date:today,count:0}}else console.log("\u4E0D\u663E\u793A")}},Number(formActive.text_in_show)*1e3)}showSubscript(){const today=this.getTodayDate();sessionStorage.setItem("subscript-sessionDisplayed",today);const displayData=JSON.parse(localStorage.getItem("subscript-displayData"))||{date:today,count:0};displayData.date!==today&&(displayData.date=today,displayData.count=0),displayData.count++,localStorage.setItem("subscript-displayData",JSON.stringify(displayData)),this.open()}shouldDisplay(){let today=this.getTodayDate();const sessionDisplayed=sessionStorage.getItem("subscript-sessionDisplayed"),displayData=JSON.parse(localStorage.getItem("subscript-displayData"))||{date:today,count:0};if(sessionDisplayed===today)return!1;let maxCount=Number(this.subscribeActFrom.formActive.text_viewDayNum);return!(displayData.date===today&&displayData.count>=maxCount)}getTodayDate(){return new Date().toISOString().split("T")[0]}init(){this.main.addEventListener("transitionend",()=>this.end()),this.getCookie("userTagClick")&&this.addSubscribeFeedback(),this.$p.addEventListener("click",e=>{let target=e.target;if(target.closest(".subscribe-module-btn")){let utm_source=new URLSearchParams(window.location.search).get("utm_source");if(this.subscribeObj.utmSource=utm_source,commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u70B9\u51FB\u63D0\u4EA4","subscribe","turntable"),this.subscribeObj.userEmail=this.$p.querySelector(".subscribe-module-input").value.trim(),!this.subscribeObj.userEmail){this.$p.querySelector(".subscribe-module-from").classList.add("report-cont"),this.$p.querySelector(".subscribe-module-inp-msg .text").innerHTML="Please enter your email to participate",this.fadeIn(this.$p.querySelector(".subscribe-module-inp-msg")),commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8-\u63D0\u793A\uFF1A\u586B\u5199\u90AE\u7BB1\u4E0D\u6B63\u786E","subscribe","1"),commonGtmEvent("\u9519\u8BEF\u4FE1\u606F-\u90AE\u7BB1\u4E3A\u7A7A","subscribe","1");return}if(!this.validateEmail(this.subscribeObj.userEmail)){this.$p.querySelector(".subscribe-module-from").classList.add("report-cont"),this.$p.querySelector(".subscribe-module-inp-msg .text").innerHTML=this.subscribeActFrom.languageMessData.participate_email_err,this.fadeIn(this.$p.querySelector(".subscribe-module-inp-msg")),commonGtmEvent("\u63D0\u793A\uFF1A\u586B\u5199\u90AE\u7BB1\u4E0D\u6B63\u786E","subscribe",this.subscribeObj.userEmail),commonGtmEvent("\u9519\u8BEF\u4FE1\u606F:"+this.subscribeObj.userEmail,"subscribe","1");return}this.getDispatch();return}const subscribeTagBtn=target.closest(".subscribe-module-button");if(subscribeTagBtn){const dataTag=$(subscribeTagBtn).attr("data-tag");this.setCookie("userTagClick",!0,2,!0),dataTag&&subscriptionApiNew(this.subscribeObj.userEmail,dataTag,"ADD_CARE_TAG"),this.addSubscribeFeedback(),dataTag==="Safe"?commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\uFF0C\u9009\u62E9\u5B89\u5168","",""):dataTag==="Pain-free"?commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\uFF0C\u9009\u62E9\u65E0\u75DB","",""):dataTag==="Long-lasting"&&commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\uFF0C\u9009\u62E9\u6709\u6548","","")}target.closest(".subscribe-module-close")&&this.closeScratch(),target.closest(".subscribe-module-close-new")&&this.closeScratch(),target.closest(".subscribe-module-button.subscribe-check-email")&&(commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\uFF0C\u70B9\u51FB\u67E5\u770B\u90AE\u7BB1","",""),this.closeScratch()),target.closest(".subscribe-module-button-text")&&(subscriptionApiNew(this.subscribeObj.userEmail,"Unchecked","ADD_CARE_TAG"),commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\uFF0C\u9009\u62E9\u4EE5\u540E","",""),this.closeScratch()),target.closest(".bottom-close-btn")&&this.closeScratch(),target.closest(".dispatch-close")&&this.closeScratch();const dispatchProductBtn=target.closest(".dispatch-product-btn");if(dispatchProductBtn){let productId=dispatchProductBtn.getAttribute("pid"),code=dispatchProductBtn.getAttribute("code");this.btnShowLoading(this.$p.querySelector(".dispatch-product-btn")),commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u70B9\u51FB\u6DFB\u52A0\u8D2D\u7269\u8F66","subscribe","turntable"),addToCartPromise(productId,null,{},code).then(cartData=>{this.btnShowLoading(this.$p.querySelector(".dispatch-product-btn"),!1),this.closeScratch()}).catch(error=>{this.btnShowLoading(this.$p.querySelector(".dispatch-product-btn"),!1)});return}}),this.$p.querySelector(".subscribe-module-input").addEventListener("keyup",e=>{this.$p.querySelector(".subscribe-module-from").classList.remove("report-cont"),this.fadeOut(this.$p.querySelector(".subscribe-module-inp-msg"))}),window.ulikeOpenSubscribe=()=>{this.showSubscript()},window.openFloatingSubscribe=()=>{this.showSubscript()},window.openReferFriend=()=>{commonGtmEvent("\u8001\u5E26\u65B0\uFF0C\u5BFC\u6D41\uFF0C\u9996\u9875icon\u5F39\u7A97\u70B9\u51FB","","")}}addSubscribeFeedback(){this.$p.querySelector(".subscribe-module-area").innerHTML=`
      <div class="subscribe-module-feedback">
          <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 42 42" fill="none">
            <path d="M21 6.125C23.942 6.125 26.8179 6.9974 29.2641 8.63189C31.7103 10.2664 33.6169 12.5895 34.7427 15.3076C35.8686 18.0256 36.1631 21.0165 35.5892 23.902C35.0152 26.7874 33.5985 29.4379 31.5182 31.5182C29.4379 33.5985 26.7874 35.0152 23.902 35.5892C21.0165 36.1631 18.0256 35.8686 15.3076 34.7427C12.5895 33.6169 10.2664 31.7103 8.63189 29.2641C6.9974 26.8179 6.125 23.942 6.125 21C6.12963 17.0563 7.6983 13.2755 10.4869 10.4869C13.2755 7.6983 17.0563 6.12963 21 6.125ZM21 3.5C17.5388 3.5 14.1554 4.52636 11.2775 6.44928C8.39966 8.37221 6.15664 11.1053 4.83211 14.303C3.50757 17.5007 3.16102 21.0194 3.83626 24.4141C4.5115 27.8087 6.17821 30.927 8.62563 33.3744C11.0731 35.8218 14.1913 37.4885 17.5859 38.1637C20.9806 38.839 24.4993 38.4924 27.697 37.1679C30.8947 35.8434 33.6278 33.6003 35.5507 30.7225C37.4736 27.8446 38.5 24.4612 38.5 21C38.5 16.3587 36.6563 11.9075 33.3744 8.62563C30.0925 5.34374 25.6413 3.5 21 3.5Z" fill="#1ADB2D"/>
            <path d="M19.25 27.5625C19.0776 27.5633 18.9067 27.5297 18.7474 27.4636C18.5881 27.3975 18.4437 27.3002 18.3225 27.1775L13.0725 21.9275C12.8407 21.6787 12.7144 21.3496 12.7204 21.0096C12.7264 20.6696 12.8642 20.3451 13.1047 20.1047C13.3451 19.8642 13.6696 19.7264 14.0096 19.7204C14.3496 19.7144 14.6787 19.8407 14.9275 20.0725L19.25 24.395L28 15.645C28.2556 15.4997 28.5525 15.4444 28.8433 15.488C29.134 15.5315 29.4017 15.6715 29.6034 15.8853C29.8052 16.0992 29.9293 16.3745 29.9558 16.6673C29.9824 16.9601 29.9099 17.2533 29.75 17.5L20.125 27.125C19.9018 27.3796 19.5877 27.5367 19.25 27.5625Z" fill="#1ADB2D"/>
          </svg>
          <div class="subscribe-module-feedback-text">Thank you for your feedback</div>
          <div class="subscribe-module-button subscribe-check-email">Check email</div>
      </div>
    `}async getDispatch(){this.btnShowLoading(this.$p.querySelector(".subscribe-module-btn"));try{let resData=await this.sendApiRequest("/promotion/sendBenefit",this.subscribeObj);if(this.btnShowLoading(this.$p.querySelector(".subscribe-module-btn"),!1),resData.code===0){let data=resData.data;Object.assign(data,{winning_title:this.subscribeActFrom.languageMessData.winning_title,winning_sub_title:this.subscribeActFrom.languageMessData.winning_sub_title,winning_code_title:this.subscribeActFrom.languageMessData.winning_code_title,prizeUrl:resData.url}),commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u53C2\u4E0E\u9875\u6210\u529F\u8BA2\u9605","subscriptPlug","");let variantRes=await this.sendApiRequest("/promotion/queryVariant",{variantId:data.extend.participantId});if(variantRes.code===0){let variantData=variantRes.data,dIndex=this.list.findIndex(item=>item.itemName==data.benefitName);if(dIndex==-1)return;this.$p.querySelector(".subscribe-module-input").value="",this.$p.classList.add("hide-text"),setTimeout(()=>{this.start(dIndex)},800),Object.assign(data,{award_info:this.subscribeActFrom.formActive.award_info,userEmail:this.subscribeObj.userEmail,variantData});let itemsWithPrice=this.subscribeActFrom.activityItem.filter(item=>item.price);data.dis_price=itemsWithPrice[0].price,commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u8FDB\u5165\u9886\u5956\u6210\u529F\u5F39\u7A97","subscribe","turntable"),this.updateSuccessDom(data),this.setCookie("subscript-success",!0,2,!0),localStorage.setItem("subscript-success-data",JSON.stringify(data)),this.setCookie("subscript-isSubscribe",!0,30,!0),this.setCookie("subscript-success-activeFrom",this.subscribeActFrom.formActive.theme,2,!0)}}else resData.code===99998&&resData.message==="participate_email_repeat_err"?(commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u63D0\u793A\uFF1A\u6BCF\u4E2A\u90AE\u7BB1\u53EA\u80FD\u8F6C\u4E00\u6B21","subscribe","turntable"),this.$p.querySelector(".subscribe-module-from").classList.add("report-cont"),this.$p.querySelector(".subscribe-module-inp-msg .text").innerHTML=this.subscribeActFrom.languageMessData[resData.message],this.fadeIn(this.$p.querySelector(".subscribe-module-inp-msg"))):(this.$p.querySelector(".subscribe-module-from").classList.add("report-cont"),this.$p.querySelector(".subscribe-module-inp-msg .text").innerHTML="Please enter your email to participate",this.fadeIn(this.$p.querySelector(".subscribe-module-inp-msg")))}catch{this.btnShowLoading(this.$p.querySelector(".subscribe-module-btn"),!1)}}updateSuccessDom(data){var actName=data.benefitName;this.subscribeObj.userEmail=data.userEmail}getValueByKey(data,targetKey){const item=data.find(element=>element.key===targetKey);return item?item.value:null}sendApiRequest(url,data){const signal=new AbortController().signal;data.siteCode="US";let urlLink=this.api+url,settings={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(data),signal,mode:"cors",credentials:"include"};return new Promise((resolve,reject2)=>{fetch(urlLink,settings).then(response=>{if(!response.ok)throw new Error(`HTTP error! Status: ${response.status}`);return response.json()}).then(data2=>resolve(data2)).catch(error=>{console.error("Error during API call:",error),reject2(error)})})}btnShowLoading($el,isShow=!0){if(isShow){$el.classList.add("loading-btn-cus");const loadingSpan=document.createElement("span");loadingSpan.className="loading-span",$el.appendChild(loadingSpan)}else{$el.classList.remove("loading-btn-cus");const loadingSpan=$el.querySelector(".loading-span");loadingSpan&&loadingSpan.remove()}}domOnUpdate(){this.$p.querySelector(".subscribe-module-text-title").innerHTML=this.subscribeActFrom.languageMessData.participate_main_title,this.$p.querySelector(".subscribe-module-btn span").innerHTML=this.subscribeActFrom.languageMessData.btn_title,this.$p.querySelector(".subscribe-module-btn .btn-txt").setAttribute("text",this.subscribeActFrom.languageMessData.btn_title),this.$p.querySelector(".subscribe-module-text-tips").innerHTML=this.subscribeActFrom.languageMessData.participate_desc}render(){this.list.forEach((item,i)=>{let newNode=this.temp.cloneNode(!0);if(newNode.style.display="block",newNode.style.transform=`rotateZ(${this.perAngle*i+this.perAngle/2}deg)`,newNode.querySelector(".tray-bg").style.background=this.color[i].color,this.list.length>2){let p=this.perAngle/2,x=(100-Math.tan(p*Math.PI/180)*100)/2;newNode.style.clipPath=`polygon(0% 50%, 100% ${x}%, 100% ${100-x}%)`}newNode.querySelector(".tray-prize").innerHTML=item.itemType==="COUPON"?`<div class="tray-prize-cont yhq-cont">
              <img src="https://cdn.shopify.com/s/files/1/0656/9079/6273/files/yhq2.svg" alt="" class="yhq-img">
              <div class="tray-prize-text"><div class="span">${item.itemName}</div> OFF </div>
            </div>`:`<div class="tray-prize-cont">
              <div class="tray-prize-text">${item.itemName}</div>
              <img src="${item.extend.prizeUrl}" alt="" class="p-img">
            </div>`,this.main.appendChild(newNode)})}open(){this.isShow||(this.isShow=!0,commonGtmEvent("\u8001\u8BA2\u9605\u8F6C\u76D8-\u5C55\u793A\u6B21\u6570","subscriptPlug",""),this.$p.classList.contains("is-close")?(this.$p.classList.remove("is-close"),this.animateValue({start:150,end:0,duration:1e3,onUpdate:value=>{this.$p.style.setProperty("display","block"),this.$p.style.setProperty("--clip-path",`${150-value}%`),this.$p.style.setProperty("--origin-x",`${value}%`),this.$p.style.setProperty("--origin-y",`${value}%`)},onComplete:()=>{}})):this.fadeIn(this.$p,null,500))}isOpen(param){let isOpen=sessionStorage.getItem("isOpenScratchUlike");return param&&sessionStorage.setItem("isOpenScratchUlike",param),isOpen}closeScratch(){const targetRect=document.querySelector(".teaser-app").getBoundingClientRect(),deltaX=targetRect.left+20,deltaY=targetRect.top+20;this.$p.style.setProperty("--origin-x",`${deltaX}px`),this.$p.style.setProperty("--origin-y",`${deltaY}px`),this.animateValue({start:0,end:150,duration:600,onUpdate:value=>{this.$p.style.setProperty("--clip-path",`${150-value}%`),this.$p.style.setProperty("opacity",`${1-value/150}`)},onComplete:()=>{this.isShow=!1,this.$p.style.setProperty("opacity","1"),this.$p.classList.add("is-close")}})}start(num){if(this.isRunning)return;this.isRunning=!0,this.rotate+=360*7;let getPrizeIndex=num+1;this.mathDeg=Math.floor(Math.random()*31)+30,this.rotates=this.rotate+(180-this.perAngle)/2-this.perAngle*getPrizeIndex+this.mathDeg+60,this.main.style.transition="all 3s ease",this.main.style.transform=`rotateZ(${this.rotates}deg)`}end(){if(!this.isRunning)return;this.isRunning=!1;let d=this.rotates-this.mathDeg+10;this.main.style.transform="rotateZ("+d+"deg)",this.main.style.transition="all 3s ease",setTimeout(()=>{this.connectTest()},3e3),setTimeout(()=>{this.fadeOut(this.$p.querySelector(".subscribe-module-cont"),()=>{this.$p.classList.add("dispatch-cont"),this.$p.classList.remove("hide-text"),commonGtmEvent("\u8BA2\u9605\u6D3B\u52A8\uFF0C\u5206\u7FA4\u9875\u9762","",""),this.fadeIn(this.$p.querySelector(".subscribe-module-cont"),null,800)},300)},3500)}validateEmail(email){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)}animateValue({start,end,duration,onUpdate,onComplete}){const startTime=performance.now();function step(currentTime){const elapsedTime=currentTime-startTime,progress=Math.min(elapsedTime/duration,1),currentValue=start+(end-start)*progress;onUpdate(currentValue),progress<1?requestAnimationFrame(step):onComplete&&onComplete()}requestAnimationFrame(step)}verticalCarousel(delay=1200,speed=500){const ul=this.$p.querySelector(".subscribe-module-slide-list"),items=ul.querySelectorAll(".subscribe-module-slide-item"),itemHeight=26;function moveSlide(){ul.style.transform=`translateY(-${itemHeight}px)`,ul.style.transition=`transform ${speed}ms ease-in-out`,setTimeout(()=>{ul.appendChild(ul.firstElementChild),ul.style.transition="none",ul.style.transform="translateY(0)"},speed)}setInterval(moveSlide,delay)}connectTest(){moduleUlike.exports({particleCount:150,spread:70,origin:{y:.5},colors:["F5D290","FEEFCF","F0C37C"],disableForReducedMotion:!0})}fadeIn(element,callback=null,duration=400){window.getComputedStyle(element).display==="none"&&(element.style.display="block",element.style.opacity=0);let startTime=performance.now();function animate(currentTime){let progress=(currentTime-startTime)/duration;progress>=1?(element.style.opacity=1,callback&&callback()):(element.style.opacity=progress,requestAnimationFrame(animate))}requestAnimationFrame(animate)}fadeOut(element,callback=null,duration=400){let startTime=performance.now();function animate(currentTime){let progress=(currentTime-startTime)/duration;progress>=1?(element.style.opacity=0,element.style.display="none",callback&&callback()):(element.style.opacity=1-progress,requestAnimationFrame(animate))}requestAnimationFrame(animate)}getCookie(name){const cookieName=name+"=",cookies=document.cookie.split(";");for(let i=0;i<cookies.length;i++){let cookie=cookies[i].trim();if(cookie.indexOf(cookieName)===0)return cookie.substring(cookieName.length,cookie.length)}return""}setCookie(cookieName,cookieValue,daysToExpire,endOfDay=!1){var expires="";if(daysToExpire){var date=new Date;date.setTime(date.getTime()+daysToExpire*24*60*60*1e3),endOfDay&&date.setHours("00","00","00","000"),expires="; expires="+date.toUTCString()}document.cookie=cookieName+"="+cookieValue+expires+"; path=/"}}if(getUrlParameter("type")!="invitationUser"||getUrlParameter("inviteCode")==null){let cashbackType=getCookie("customer_type");if(cashbackType==="air")updateTeaserImage("https://cdn.shopify.com/s/files/1/0656/9079/6273/files/Group_457393629.png?v=1753947824","/pages/cashback-air");else if(cashbackType==="mask")updateTeaserImage("https://cdn.shopify.com/s/files/1/0656/9079/6273/files/Group_457393629_1.png?v=1753947824","/pages/cashback-mask");else{const wheel=new Wheel(".subscribe-module")}$(".referfriend-app .teaser-gif").attr("src","https://cdn.shopify.com/s/files/1/0656/9079/6273/files/icon2.gif?v=1747115566")}function updateTeaserImage(imageUrl,targetUrl){const $teaser=$(".teaser-app .teaser-image");$teaser.parent().hasClass("teaser-link")?($teaser.attr("src",imageUrl).removeAttr("onclick"),$teaser.parent(".teaser-link").attr("href",targetUrl)):$teaser.attr("src",imageUrl).removeAttr("onclick").wrap(`<a href="${targetUrl}" class="teaser-link"></a>`),$(".teaser-app").css("width","55px").fadeIn()}
//# sourceMappingURL=/cdn/shop/t/56/assets/wheel.js.map?v=77935617761077538901753955745
