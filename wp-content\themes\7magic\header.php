<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#ffffff">
    
    <?php wp_head(); ?>
    
    <!-- Preload Critical Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Custom Fonts from Ulike -->
    <link rel="preload" as="font" href="<?php echo get_template_directory_uri(); ?>/assets/fonts/Saans-Regular.otf" type="font/otf" crossorigin>
    <link rel="preload" as="font" href="<?php echo get_template_directory_uri(); ?>/assets/fonts/Saans-Medium.otf" type="font/otf" crossorigin>
    
    <!-- Critical CSS -->
    <style>
        @font-face {
            font-family: 'Saans-Regular';
            src: url('<?php echo get_template_directory_uri(); ?>/assets/fonts/Saans-Regular.otf') format('opentype');
            font-display: swap;
        }
        
        @font-face {
            font-family: 'Saans-Medium';
            src: url('<?php echo get_template_directory_uri(); ?>/assets/fonts/Saans-Medium.otf') format('opentype');
            font-display: swap;
        }
        
        /* Critical above-the-fold styles */
        body {
            margin: 0;
            font-family: 'Saans-Regular', Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        /* Header Styles */
        .site-header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-top {
            background: #000;
            color: #fff;
            text-align: center;
            padding: 0.5rem;
            font-size: 0.9rem;
        }
        
        .header-top a {
            color: #fff;
            text-decoration: none;
        }
        
        .main-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 2rem;
        }
        
        .site-logo img {
            max-height: 50px;
            width: auto;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 2rem;
        }
        
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-menu a:hover {
            color: #ff6b6b;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-icon,
        .cart-icon,
        .menu-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #333;
        }
        
        .menu-toggle {
            display: none;
        }
        
        /* Mobile Styles */
        @media (max-width: 768px) {
            .main-navigation {
                flex-wrap: wrap;
                padding: 1rem;
            }
            
            .nav-menu {
                display: none;
                width: 100%;
                flex-direction: column;
                gap: 1rem;
                margin-top: 1rem;
            }
            
            .nav-menu.active {
                display: flex;
            }
            
            .menu-toggle {
                display: block;
            }
            
            .header-actions {
                order: 2;
            }
            
            .site-logo {
                order: 1;
            }
            
            .menu-toggle {
                order: 3;
            }
        }
    </style>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    
    <header id="masthead" class="site-header">
        
        <!-- Top Banner (like Ulike's promotional banner) -->
        <div class="header-top">
            <div class="container">
                <span>🎉 14-Day Price Match • Free Shipping • 2-Year Warranty</span>
                <a href="#learn-more" style="margin-left: 1rem;">Learn More →</a>
            </div>
        </div>
        
        <!-- Main Navigation -->
        <nav class="main-navigation">
            <div class="site-logo">
                <?php if (has_custom_logo()) : ?>
                    <?php the_custom_logo(); ?>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home" style="display: flex; align-items: center; text-decoration: none;">
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/logo.svg" alt="<?php bloginfo('name'); ?>" style="height: 50px; margin-right: 10px;" />
                        <span style="font-size: 1.5rem; font-weight: 700; color: #333;"><?php bloginfo('name'); ?></span>
                    </a>
                <?php endif; ?>
            </div>
            
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'container'      => false,
                'fallback_cb'    => 'sevenmagic_fallback_menu',
            ));
            ?>
            
            <div class="header-actions">
                <button class="search-icon" aria-label="Search">🔍</button>
                <button class="cart-icon" aria-label="Cart">🛒</button>
                <button class="menu-toggle" aria-label="Menu" onclick="toggleMobileMenu()">☰</button>
            </div>
        </nav>
        
    </header>

    <div id="content" class="site-content">

<script>
// Mobile menu toggle
function toggleMobileMenu() {
    const menu = document.querySelector('.nav-menu');
    menu.classList.toggle('active');
}

// Fallback menu function
<?php if (!function_exists('sevenmagic_fallback_menu')) :
function sevenmagic_fallback_menu() {
    echo '<ul class="nav-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
    echo '<li><a href="#products">Products</a></li>';
    echo '<li><a href="#technology">Technology</a></li>';
    echo '<li><a href="#about">About</a></li>';
    echo '<li><a href="#contact">Contact</a></li>';
    echo '</ul>';
}
endif; ?>
</script>
