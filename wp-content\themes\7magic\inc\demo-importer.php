<?php
/**
 * Demo Content Importer
 * 
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Demo Import Menu
 */
function sevenmagic_add_demo_import_menu() {
    add_theme_page(
        '7Magic Demo Import',
        'Demo Import',
        'manage_options',
        'sevenmagic-demo-import',
        'sevenmagic_demo_import_page'
    );
}
add_action('admin_menu', 'sevenmagic_add_demo_import_menu');

/**
 * Demo Import Page
 */
function sevenmagic_demo_import_page() {
    ?>
    <div class="wrap">
        <h1>7Magic Demo Content Import</h1>
        
        <?php if (isset($_GET['imported']) && $_GET['imported'] == 'success') : ?>
            <div class="notice notice-success">
                <p><strong>Demo content imported successfully!</strong> Your site now has all the demo products, pages, and content.</p>
            </div>
        <?php endif; ?>
        
        <div class="card" style="max-width: 800px;">
            <h2>Import Demo Content</h2>
            <p>This will import all demo content including:</p>
            <ul>
                <li>✅ <strong>4 Demo Products</strong> - Sapphire Air 10, Air 3, Ulike X, ReGlow LED Mask</li>
                <li>✅ <strong>Demo Pages</strong> - About Us, How It Works, Contact, FAQs</li>
                <li>✅ <strong>Product Images</strong> - All original Ulike product photos</li>
                <li>✅ <strong>Homepage Setup</strong> - Configured homepage with hero section</li>
                <li>✅ <strong>Navigation Menu</strong> - Pre-built menu structure</li>
            </ul>
            
            <p><strong>Note:</strong> This will not overwrite existing content, only add new demo content.</p>
            
            <form method="post" action="">
                <?php wp_nonce_field('sevenmagic_import_demo', 'demo_nonce'); ?>
                <p>
                    <input type="submit" name="import_demo" class="button button-primary button-large" value="Import Demo Content" />
                </p>
            </form>
            
            <hr>
            
            <h3>What's Included</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 8px;">
                    <h4>Products</h4>
                    <ul style="margin: 0; font-size: 0.9rem;">
                        <li>Sapphire Air 10 ($299.99)</li>
                        <li>Sapphire Air 3 ($199.99)</li>
                        <li>Ulike X ($149.99)</li>
                        <li>ReGlow LED Mask ($399.99)</li>
                    </ul>
                </div>
                <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 8px;">
                    <h4>Pages</h4>
                    <ul style="margin: 0; font-size: 0.9rem;">
                        <li>About Us</li>
                        <li>How It Works</li>
                        <li>Contact Us</li>
                        <li>FAQs</li>
                    </ul>
                </div>
                <div style="border: 1px solid #ddd; padding: 1rem; border-radius: 8px;">
                    <h4>Features</h4>
                    <ul style="margin: 0; font-size: 0.9rem;">
                        <li>Amazon Integration</li>
                        <li>Product Meta Fields</li>
                        <li>Responsive Images</li>
                        <li>SEO Optimized</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card" style="max-width: 800px; margin-top: 2rem;">
            <h2>After Import</h2>
            <p>Once you import the demo content, you can:</p>
            <ol>
                <li><strong>Customize Products:</strong> Edit product details, prices, and Amazon URLs</li>
                <li><strong>Replace Images:</strong> Upload your own product images</li>
                <li><strong>Edit Content:</strong> Modify page content to match your brand</li>
                <li><strong>Add More Products:</strong> Create additional products using the same structure</li>
                <li><strong>Customize Design:</strong> Use Elementor to modify layouts</li>
            </ol>
            
            <h3>Quick Start Guide</h3>
            <ol>
                <li>Import demo content using the button above</li>
                <li>Go to <strong>Appearance → Customize</strong> to upload your logo</li>
                <li>Visit <strong>Products → All Products</strong> to edit product details</li>
                <li>Update Amazon URLs in each product's custom fields</li>
                <li>Customize colors in <strong>Appearance → Customize</strong></li>
            </ol>
        </div>
    </div>
    
    <style>
    .card {
        background: white;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .card h2 {
        margin-top: 0;
        color: #23282d;
    }
    .card ul, .card ol {
        padding-left: 20px;
    }
    .card li {
        margin-bottom: 5px;
    }
    </style>
    <?php
}

/**
 * Handle Demo Import
 */
function sevenmagic_handle_demo_import() {
    if (isset($_POST['import_demo']) && wp_verify_nonce($_POST['demo_nonce'], 'sevenmagic_import_demo')) {
        
        // Import demo content
        sevenmagic_create_demo_content();
        sevenmagic_create_demo_menu();
        
        // Redirect with success message
        wp_redirect(admin_url('themes.php?page=sevenmagic-demo-import&imported=success'));
        exit;
    }
}
add_action('admin_init', 'sevenmagic_handle_demo_import');

/**
 * Create Demo Navigation Menu
 */
function sevenmagic_create_demo_menu() {
    // Check if menu already exists
    $menu_name = 'Primary Menu';
    $menu_exists = wp_get_nav_menu_object($menu_name);
    
    if ($menu_exists) {
        return;
    }
    
    // Create menu
    $menu_id = wp_create_nav_menu($menu_name);
    
    if (is_wp_error($menu_id)) {
        return;
    }
    
    // Get pages
    $home_page = get_page_by_title('Home');
    $about_page = get_page_by_title('About Us');
    $how_it_works_page = get_page_by_title('How It Works');
    $contact_page = get_page_by_title('Contact Us');
    $faqs_page = get_page_by_title('FAQs');
    
    // Add menu items
    $menu_items = array(
        array(
            'menu-item-title' => 'Home',
            'menu-item-url' => home_url('/'),
            'menu-item-status' => 'publish',
            'menu-item-type' => 'custom'
        ),
        array(
            'menu-item-title' => 'Products',
            'menu-item-url' => home_url('/#products'),
            'menu-item-status' => 'publish',
            'menu-item-type' => 'custom'
        )
    );
    
    if ($about_page) {
        $menu_items[] = array(
            'menu-item-object-id' => $about_page->ID,
            'menu-item-object' => 'page',
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish'
        );
    }
    
    if ($how_it_works_page) {
        $menu_items[] = array(
            'menu-item-object-id' => $how_it_works_page->ID,
            'menu-item-object' => 'page',
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish'
        );
    }
    
    if ($contact_page) {
        $menu_items[] = array(
            'menu-item-object-id' => $contact_page->ID,
            'menu-item-object' => 'page',
            'menu-item-type' => 'post_type',
            'menu-item-status' => 'publish'
        );
    }
    
    // Add menu items
    foreach ($menu_items as $item) {
        wp_update_nav_menu_item($menu_id, 0, $item);
    }
    
    // Assign menu to location
    $locations = get_theme_mod('nav_menu_locations');
    $locations['primary'] = $menu_id;
    set_theme_mod('nav_menu_locations', $locations);
}

/**
 * Add Admin Notice for Demo Import
 */
function sevenmagic_demo_import_notice() {
    $screen = get_current_screen();
    
    // Only show on theme-related pages
    if (!in_array($screen->id, array('themes', 'appearance_page_sevenmagic-demo-import'))) {
        return;
    }
    
    // Don't show if demo content already imported
    if (get_option('sevenmagic_demo_created')) {
        return;
    }
    
    ?>
    <div class="notice notice-info is-dismissible">
        <p>
            <strong>7Magic Theme:</strong> 
            Want to see the theme in action? 
            <a href="<?php echo admin_url('themes.php?page=sevenmagic-demo-import'); ?>">Import demo content</a> 
            to get started with sample products and pages.
        </p>
    </div>
    <?php
}
add_action('admin_notices', 'sevenmagic_demo_import_notice');
