# 7Magic WordPress Theme - Installation Guide

## Quick Start

### 1. Upload Theme
Copy the entire `7magic` folder to your WordPress themes directory:
```
wp-content/themes/7magic/
```

### 2. Activate Theme
1. Login to WordPress Admin
2. Go to **Appearance → Themes**
3. Find "7Magic" theme
4. Click **Activate**

### 3. Basic Setup
1. **Set Homepage**: Go to **Settings → Reading** and set a static page as homepage
2. **Create Menu**: Go to **Appearance → Menus** and create a primary menu
3. **Add Logo**: Go to **Appearance → Customize → Site Identity** and upload your logo

## Recommended Plugins

### Essential
- **Elementor** - For advanced page building
- **Contact Form 7** - For contact forms
- **Yoast SEO** - For search engine optimization

### Optional
- **WP Rocket** - For caching and performance
- **Smush** - For image optimization
- **UpdraftPlus** - For backups

## Theme Configuration

### 1. Homepage Setup
The theme works best with a custom homepage. You can either:

**Option A: Use the built-in homepage**
- The theme automatically shows a beautiful homepage when you visit your site

**Option B: Create a custom page**
1. Create a new page called "Home"
2. Use Elementor or the block editor to design your layout
3. Go to **Settings → Reading** and set this page as your homepage

### 2. Navigation Menu
1. Go to **Appearance → Menus**
2. Create a new menu called "Primary Menu"
3. Add pages like:
   - Home
   - Products
   - About
   - Contact
4. Assign to "Primary Menu" location

### 3. Adding Products
1. Go to **Products → Add New** (custom post type)
2. Fill in:
   - Product title
   - Description
   - Featured image
   - Amazon URL (in custom fields)
   - Price
   - Features

### 4. Customizing Colors
Edit the main stylesheet or use the WordPress Customizer:
```css
:root {
  --primary-color: #ff6b6b;
  --secondary-color: #ff8e8e;
  --text-color: #333;
  --background-color: #fff;
}
```

## Troubleshooting

### Theme Not Showing Correctly
1. **Check PHP Version**: Ensure PHP 7.4 or higher
2. **Memory Limit**: Increase to at least 256MB
3. **File Permissions**: Ensure proper file permissions (644 for files, 755 for folders)

### Images Not Loading
1. **Check File Paths**: Ensure all image files are in the correct location
2. **Regenerate Thumbnails**: Use a plugin like "Regenerate Thumbnails"
3. **Check .htaccess**: Ensure no blocking rules

### Fonts Not Loading
1. **Check Font Files**: Ensure .otf files are in `/assets/fonts/`
2. **MIME Types**: Add to .htaccess if needed:
   ```
   AddType font/otf .otf
   AddType font/woff .woff
   AddType font/woff2 .woff2
   ```

### JavaScript Not Working
1. **jQuery Conflicts**: Check for plugin conflicts
2. **Console Errors**: Check browser console for errors
3. **File Loading**: Ensure all JS files are loading correctly

## Performance Optimization

### 1. Image Optimization
- Use WebP format when possible
- Compress images before upload
- Use lazy loading (built into theme)

### 2. Caching
- Install a caching plugin like WP Rocket
- Enable browser caching
- Use a CDN for static assets

### 3. Database Optimization
- Remove unused plugins
- Clean up spam comments
- Optimize database tables

## Security

### 1. Basic Security
- Keep WordPress updated
- Use strong passwords
- Install a security plugin like Wordfence

### 2. File Permissions
```
Folders: 755
Files: 644
wp-config.php: 600
```

### 3. Hide wp-admin
- Use a plugin to change login URL
- Limit login attempts
- Enable two-factor authentication

## Support

### Getting Help
1. **Check Documentation**: Read this file and README.md
2. **WordPress Codex**: Official WordPress documentation
3. **Community Forums**: WordPress.org support forums

### Common Issues
- **White Screen**: Usually a PHP error, check error logs
- **500 Error**: Server configuration issue
- **404 Errors**: Permalink issues, flush rewrite rules

### Debug Mode
Add to wp-config.php for debugging:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

## Advanced Configuration

### Child Theme (Recommended)
Create a child theme to preserve customizations:

1. Create folder: `wp-content/themes/7magic-child/`
2. Create `style.css`:
```css
/*
Theme Name: 7Magic Child
Template: 7magic
*/

@import url("../7magic/style.css");

/* Your custom styles here */
```

3. Create `functions.php`:
```php
<?php
function sevenmagic_child_enqueue_styles() {
    wp_enqueue_style('parent-style', get_template_directory_uri() . '/style.css');
}
add_action('wp_enqueue_scripts', 'sevenmagic_child_enqueue_styles');
```

### Custom Functions
Add custom functionality to child theme's functions.php:
```php
// Custom post types
// Custom shortcodes
// Custom widgets
// Theme modifications
```

## Backup & Maintenance

### Regular Backups
- **Files**: Backup entire WordPress installation
- **Database**: Export database regularly
- **Automated**: Use UpdraftPlus or similar

### Updates
- **WordPress Core**: Keep updated
- **Plugins**: Update regularly
- **Theme**: Check for updates

### Monitoring
- **Uptime**: Monitor site availability
- **Performance**: Check loading speeds
- **Security**: Scan for malware

---

**Need Help?** Contact the theme author or check the WordPress community forums for support.
