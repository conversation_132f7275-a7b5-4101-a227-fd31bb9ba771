<?php
/**
 * 7Magic Theme Functions
 *
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function sevenmagic_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Add support for wide and full alignment
    add_theme_support('align-wide');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', '7magic'),
        'footer'  => esc_html__('Footer Menu', '7magic'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'sevenmagic_setup');

/**
 * Enqueue Scripts and Styles
 */
function sevenmagic_scripts() {
    // Main theme stylesheet
    wp_enqueue_style('sevenmagic-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Ulike original CSS files
    wp_enqueue_style('ulike-theme', get_template_directory_uri() . '/assets/css/theme.min.css', array(), '1.0.0');
    wp_enqueue_style('ulike-common', get_template_directory_uri() . '/assets/css/common-ulike.min.css', array(), '1.0.0');
    wp_enqueue_style('ulike-custom', get_template_directory_uri() . '/assets/css/custom_css.css', array(), '1.0.0');
    wp_enqueue_style('swiper-css', get_template_directory_uri() . '/assets/css/swiper-bundle.min.css', array(), '1.0.0');
    
    // JavaScript files
    wp_enqueue_script('jquery');
    wp_enqueue_script('swiper-js', get_template_directory_uri() . '/assets/js/swiper-bundle.min.js', array('jquery'), '1.0.0', true);
    wp_enqueue_script('gsap', get_template_directory_uri() . '/assets/js/gsap.min.js', array(), '1.0.0', true);
    wp_enqueue_script('ulike-theme-js', get_template_directory_uri() . '/assets/js/theme.js', array('jquery'), '1.0.0', true);
    wp_enqueue_script('sevenmagic-custom', get_template_directory_uri() . '/assets/js/custom.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('sevenmagic-custom', 'sevenmagic_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('sevenmagic_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'sevenmagic_scripts');

/**
 * Register Widget Areas
 */
function sevenmagic_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', '7magic'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', '7magic'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => esc_html__('Footer Widget Area', '7magic'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add widgets to the footer area.', '7magic'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="footer-widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'sevenmagic_widgets_init');

/**
 * Custom Post Types for Products
 */
function sevenmagic_register_post_types() {
    // Products Post Type
    register_post_type('product', array(
        'labels' => array(
            'name'               => 'Products',
            'singular_name'      => 'Product',
            'menu_name'          => 'Products',
            'add_new'            => 'Add New Product',
            'add_new_item'       => 'Add New Product',
            'edit_item'          => 'Edit Product',
            'new_item'           => 'New Product',
            'view_item'          => 'View Product',
            'search_items'       => 'Search Products',
            'not_found'          => 'No products found',
            'not_found_in_trash' => 'No products found in trash',
        ),
        'public'       => true,
        'has_archive'  => true,
        'supports'     => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'menu_icon'    => 'dashicons-products',
        'rewrite'      => array('slug' => 'products'),
    ));
}
add_action('init', 'sevenmagic_register_post_types');

/**
 * Add Custom Meta Boxes for Products
 */
function sevenmagic_add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        'Product Details',
        'sevenmagic_product_details_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'sevenmagic_add_product_meta_boxes');

function sevenmagic_product_details_callback($post) {
    wp_nonce_field('sevenmagic_save_product_details', 'sevenmagic_product_nonce');
    
    $amazon_url = get_post_meta($post->ID, '_amazon_url', true);
    $price = get_post_meta($post->ID, '_price', true);
    $features = get_post_meta($post->ID, '_features', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="amazon_url">Amazon URL:</label></th>';
    echo '<td><input type="url" id="amazon_url" name="amazon_url" value="' . esc_attr($amazon_url) . '" style="width: 100%;" /></td></tr>';
    echo '<tr><th><label for="price">Price:</label></th>';
    echo '<td><input type="text" id="price" name="price" value="' . esc_attr($price) . '" /></td></tr>';
    echo '<tr><th><label for="features">Key Features:</label></th>';
    echo '<td><textarea id="features" name="features" rows="5" style="width: 100%;">' . esc_textarea($features) . '</textarea></td></tr>';
    echo '</table>';
}

function sevenmagic_save_product_details($post_id) {
    if (!isset($_POST['sevenmagic_product_nonce']) || !wp_verify_nonce($_POST['sevenmagic_product_nonce'], 'sevenmagic_save_product_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['amazon_url'])) {
        update_post_meta($post_id, '_amazon_url', sanitize_url($_POST['amazon_url']));
    }
    
    if (isset($_POST['price'])) {
        update_post_meta($post_id, '_price', sanitize_text_field($_POST['price']));
    }
    
    if (isset($_POST['features'])) {
        update_post_meta($post_id, '_features', sanitize_textarea_field($_POST['features']));
    }
}
add_action('save_post', 'sevenmagic_save_product_details');

/**
 * Elementor Compatibility
 */
function sevenmagic_elementor_support() {
    add_theme_support('elementor');
}
add_action('after_setup_theme', 'sevenmagic_elementor_support');

/**
 * Include Additional Files
 */
require_once get_template_directory() . '/inc/demo-importer.php';
require_once get_template_directory() . '/inc/customizer.php';

/**
 * Custom Shortcodes
 */
function sevenmagic_product_grid_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => 4,
        'category' => '',
    ), $atts);
    
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => intval($atts['limit']),
        'post_status' => 'publish',
    );
    
    if (!empty($atts['category'])) {
        $args['meta_query'] = array(
            array(
                'key' => '_product_category',
                'value' => $atts['category'],
                'compare' => 'LIKE',
            ),
        );
    }
    
    $products = new WP_Query($args);
    
    ob_start();
    
    if ($products->have_posts()) {
        echo '<div class="product-grid">';
        while ($products->have_posts()) {
            $products->the_post();
            $amazon_url = get_post_meta(get_the_ID(), '_amazon_url', true);
            $price = get_post_meta(get_the_ID(), '_price', true);
            
            echo '<div class="product-card fade-in">';
            if (has_post_thumbnail()) {
                echo '<img src="' . get_the_post_thumbnail_url(get_the_ID(), 'medium') . '" alt="' . get_the_title() . '" class="product-image">';
            }
            echo '<div class="product-info">';
            echo '<h3 class="product-title">' . get_the_title() . '</h3>';
            echo '<p class="product-description">' . get_the_excerpt() . '</p>';
            if ($price) {
                echo '<p class="product-price">$' . esc_html($price) . '</p>';
            }
            if ($amazon_url) {
                echo '<a href="' . esc_url($amazon_url) . '" target="_blank" class="amazon-buy-btn">Buy on Amazon</a>';
            }
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
        wp_reset_postdata();
    }
    
    return ob_get_clean();
}
add_shortcode('product_grid', 'sevenmagic_product_grid_shortcode');

/**
 * Customize Login Page
 */
function sevenmagic_login_logo() {
    echo '<style type="text/css">
        #login h1 a, .login h1 a {
            background-image: url(' . get_template_directory_uri() . '/assets/images/logo.png);
            height: 80px;
            width: 200px;
            background-size: contain;
            background-repeat: no-repeat;
            padding-bottom: 30px;
        }
    </style>';
}
add_action('login_enqueue_scripts', 'sevenmagic_login_logo');

/**
 * Remove WordPress version from head
 */
remove_action('wp_head', 'wp_generator');

/**
 * Optimize WordPress
 */
function sevenmagic_optimize_wp() {
    // Remove unnecessary scripts
    wp_deregister_script('wp-embed');
    
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'sevenmagic_optimize_wp');

/**
 * Add async/defer to scripts
 */
function sevenmagic_add_async_defer($tag, $handle, $src) {
    $async_scripts = array('gsap', 'swiper-js');
    $defer_scripts = array('sevenmagic-custom', 'ulike-theme-js');

    if (in_array($handle, $async_scripts)) {
        return str_replace('<script ', '<script async ', $tag);
    }

    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'sevenmagic_add_async_defer', 10, 3);

/**
 * Create Demo Content on Theme Activation
 */
function sevenmagic_create_demo_content() {
    // Check if demo content already exists
    if (get_option('sevenmagic_demo_created')) {
        return;
    }

    // Create demo products
    sevenmagic_create_demo_products();

    // Create demo pages
    sevenmagic_create_demo_pages();

    // Set homepage
    sevenmagic_set_homepage();

    // Mark demo content as created
    update_option('sevenmagic_demo_created', true);
}
add_action('after_switch_theme', 'sevenmagic_create_demo_content');

/**
 * Create Demo Products
 */
function sevenmagic_create_demo_products() {
    $demo_products = array(
        array(
            'title' => 'Sapphire Air 10 IPL Hair Removal',
            'content' => 'Our most advanced IPL device with 10 intensity levels and ice cooling technology. Features dual light technology, skin sensor, and sapphire ice cooling for comfortable, professional results at home.',
            'excerpt' => 'Advanced IPL device with 10 intensity levels and ice cooling technology.',
            'amazon_url' => 'https://amazon.com/ulike-sapphire-air-10',
            'price' => '$299.99',
            'features' => "• 10 Intensity Levels\n• Sapphire Ice Cooling\n• Dual Light Technology\n• Skin Sensor\n• 600,000 Flashes\n• FDA Cleared",
            'image' => 'Air_10_1_1740x3390.png'
        ),
        array(
            'title' => 'Sapphire Air 3 IPL Hair Removal',
            'content' => 'Perfect for beginners with 3 intensity levels and gentle treatment. Features the same professional technology in a user-friendly design.',
            'excerpt' => 'Perfect for beginners with 3 intensity levels and gentle treatment.',
            'amazon_url' => 'https://amazon.com/ulike-sapphire-air-3',
            'price' => '$199.99',
            'features' => "• 3 Intensity Levels\n• Sapphire Ice Cooling\n• Skin Sensor\n• 500,000 Flashes\n• Beginner Friendly",
            'image' => 'Air_3_SS_3ac65246-db43-4eb0-ab56-8862eb2e36e72e25.jpg'
        ),
        array(
            'title' => 'Ulike X IPL Hair Removal',
            'content' => 'Compact and powerful IPL device for precise hair removal treatment. Perfect for targeted areas and travel.',
            'excerpt' => 'Compact and powerful IPL device for precise hair removal treatment.',
            'amazon_url' => 'https://amazon.com/ulike-x-ipl',
            'price' => '$149.99',
            'features' => "• Compact Design\n• Precise Treatment\n• Travel Friendly\n• 300,000 Flashes\n• Quick Results",
            'image' => 'Ulike_X_SS_c330927a-fd9a-4761-8189-c416ee4eb52b_1500xe5aa.jpg'
        ),
        array(
            'title' => 'ReGlow LED Light Therapy Mask',
            'content' => 'Professional LED light therapy for anti-aging and skin rejuvenation. Features multiple light wavelengths for comprehensive skin treatment.',
            'excerpt' => 'Professional LED light therapy for anti-aging and skin rejuvenation.',
            'amazon_url' => 'https://amazon.com/ulike-reglow-led-mask',
            'price' => '$399.99',
            'features' => "• Multi-spectrum LED\n• Anti-aging Treatment\n• Skin Rejuvenation\n• Professional Results\n• Easy to Use",
            'image' => 'Reglow_SS_ec6bc491-aaa5-4813-ba0e-92da4d2069452e25.jpg'
        )
    );

    foreach ($demo_products as $product_data) {
        // Check if product already exists
        $existing = get_page_by_title($product_data['title'], OBJECT, 'product');
        if ($existing) {
            continue;
        }

        // Create product post
        $product_id = wp_insert_post(array(
            'post_title' => $product_data['title'],
            'post_content' => $product_data['content'],
            'post_excerpt' => $product_data['excerpt'],
            'post_status' => 'publish',
            'post_type' => 'product',
            'post_author' => 1
        ));

        if ($product_id) {
            // Add meta data
            update_post_meta($product_id, '_amazon_url', $product_data['amazon_url']);
            update_post_meta($product_id, '_price', $product_data['price']);
            update_post_meta($product_id, '_features', $product_data['features']);

            // Set featured image if exists
            $image_path = get_template_directory() . '/assets/images/shop/' . $product_data['image'];
            if (file_exists($image_path)) {
                sevenmagic_set_featured_image($product_id, $image_path, $product_data['title']);
            }
        }
    }
}

/**
 * Set Featured Image for Post
 */
function sevenmagic_set_featured_image($post_id, $image_path, $image_title) {
    $upload_dir = wp_upload_dir();
    $image_data = file_get_contents($image_path);
    $filename = basename($image_path);

    if (wp_mkdir_p($upload_dir['path'])) {
        $file = $upload_dir['path'] . '/' . $filename;
    } else {
        $file = $upload_dir['basedir'] . '/' . $filename;
    }

    file_put_contents($file, $image_data);

    $wp_filetype = wp_check_filetype($filename, null);
    $attachment = array(
        'post_mime_type' => $wp_filetype['type'],
        'post_title' => sanitize_file_name($image_title),
        'post_content' => '',
        'post_status' => 'inherit'
    );

    $attach_id = wp_insert_attachment($attachment, $file, $post_id);
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $file);
    wp_update_attachment_metadata($attach_id, $attach_data);

    set_post_thumbnail($post_id, $attach_id);
}

/**
 * Create Demo Pages
 */
function sevenmagic_create_demo_pages() {
    $demo_pages = array(
        array(
            'title' => 'About Us',
            'content' => '<h2>Leading the Revolution in At-Home Beauty Technology</h2>

            <p>At 7Magic, we believe that professional beauty treatments should be accessible to everyone, right from the comfort of your own home. Our mission is to democratize advanced beauty technology, making it safe, effective, and affordable for people worldwide.</p>

            <h3>Our Story</h3>
            <p>Founded with a vision to transform the beauty industry, 7Magic has become a trusted name in at-home IPL hair removal and LED light therapy. We combine cutting-edge technology with user-friendly design to deliver professional-grade results.</p>

            <h3>Why Choose 7Magic?</h3>
            <ul>
                <li><strong>FDA Cleared Technology:</strong> All our devices meet the highest safety standards</li>
                <li><strong>Dermatologist Approved:</strong> Recommended by skin care professionals</li>
                <li><strong>Proven Results:</strong> Trusted by millions of satisfied customers worldwide</li>
                <li><strong>2-Year Warranty:</strong> We stand behind our products with comprehensive coverage</li>
            </ul>

            <h3>Our Technology</h3>
            <p>We utilize the latest advances in IPL (Intense Pulsed Light) and LED light therapy to provide safe, effective treatments that rival professional salon results. Our devices feature:</p>

            <ul>
                <li>Sapphire Ice Cooling Technology</li>
                <li>Smart Skin Sensor</li>
                <li>Dual Light Technology</li>
                <li>Multiple Intensity Levels</li>
            </ul>'
        ),
        array(
            'title' => 'How It Works',
            'content' => '<h2>IPL Hair Removal Technology Explained</h2>

            <p>Our IPL (Intense Pulsed Light) technology uses gentle pulses of light to target hair follicles, gradually reducing hair growth for long-lasting smooth skin.</p>

            <h3>The Science Behind IPL</h3>
            <p>IPL works by emitting broad-spectrum light that is absorbed by the melanin in hair follicles. This light energy is converted to heat, which damages the follicle and inhibits future hair growth.</p>

            <h3>Treatment Process</h3>
            <ol>
                <li><strong>Preparation:</strong> Shave the treatment area and clean your skin</li>
                <li><strong>Skin Test:</strong> Our smart sensor automatically detects your skin tone</li>
                <li><strong>Treatment:</strong> Apply the device to your skin and activate</li>
                <li><strong>Cooling:</strong> Sapphire ice cooling keeps you comfortable</li>
                <li><strong>Results:</strong> See visible reduction in just 2 weeks</li>
            </ol>

            <h3>Safety Features</h3>
            <ul>
                <li>Automatic skin tone detection</li>
                <li>Built-in safety sensors</li>
                <li>Ice cooling technology</li>
                <li>FDA cleared for home use</li>
            </ul>

            <h3>Expected Timeline</h3>
            <p><strong>Week 1-2:</strong> Initial hair reduction<br>
            <strong>Week 3-4:</strong> Noticeable smoothness<br>
            <strong>Week 5-8:</strong> Significant hair reduction<br>
            <strong>Week 9-12:</strong> Long-lasting results</p>'
        ),
        array(
            'title' => 'Contact Us',
            'content' => '<h2>Get in Touch</h2>

            <p>Have questions about our products or need support? We\'re here to help!</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin: 2rem 0;">
                <div style="background: #f9f9f9; padding: 2rem; border-radius: 12px;">
                    <h3>Customer Support</h3>
                    <p><strong>Email:</strong> <EMAIL><br>
                    <strong>Phone:</strong> 1-800-7MAGIC<br>
                    <strong>Hours:</strong> Mon-Fri 9AM-6PM EST</p>
                </div>

                <div style="background: #f9f9f9; padding: 2rem; border-radius: 12px;">
                    <h3>Technical Support</h3>
                    <p><strong>Email:</strong> <EMAIL><br>
                    <strong>Live Chat:</strong> Available 24/7<br>
                    <strong>Response Time:</strong> Within 2 hours</p>
                </div>
            </div>

            <h3>Frequently Asked Questions</h3>
            <p>Before contacting us, check our <a href="/faqs">FAQ section</a> for quick answers to common questions.</p>

            <h3>Warranty & Returns</h3>
            <p>All our products come with a 2-year warranty and 90-day money-back guarantee. For warranty claims or returns, please contact our support team.</p>'
        ),
        array(
            'title' => 'FAQs',
            'content' => '<h2>Frequently Asked Questions</h2>

            <h3>General Questions</h3>

            <h4>Is IPL hair removal safe?</h4>
            <p>Yes, our IPL devices are FDA cleared and dermatologist approved. They use the same technology found in professional salons but are designed for safe home use.</p>

            <h4>How long does it take to see results?</h4>
            <p>Most users see visible hair reduction within 2 weeks of regular use. Full results typically appear after 8-12 weeks of consistent treatment.</p>

            <h4>Is the treatment painful?</h4>
            <p>Our devices feature sapphire ice cooling technology that keeps the treatment comfortable. Most users describe the sensation as a gentle warming feeling.</p>

            <h3>Device Usage</h3>

            <h4>How often should I use the device?</h4>
            <p>For the first 4 weeks, use 2-3 times per week. After that, use as needed for maintenance, typically once every 2-4 weeks.</p>

            <h4>Can I use it on all skin tones?</h4>
            <p>Our devices work best on light to medium skin tones with dark hair. The built-in skin sensor will automatically detect if your skin tone is suitable for treatment.</p>

            <h4>Which body areas can I treat?</h4>
            <p>You can safely treat legs, arms, underarms, bikini area, and face (below the cheekbone). Do not use on or around the eyes.</p>

            <h3>Technical Support</h3>

            <h4>What if my device stops working?</h4>
            <p>All devices come with a 2-year warranty. Contact our technical support team for troubleshooting or replacement.</p>

            <h4>How many flashes does the device have?</h4>
            <p>Our devices range from 300,000 to 600,000 flashes, which is enough for multiple years of full-body treatments.</p>'
        )
    );

    foreach ($demo_pages as $page_data) {
        // Check if page already exists
        $existing = get_page_by_title($page_data['title']);
        if ($existing) {
            continue;
        }

        // Create page
        wp_insert_post(array(
            'post_title' => $page_data['title'],
            'post_content' => $page_data['content'],
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ));
    }
}

/**
 * Set Homepage
 */
function sevenmagic_set_homepage() {
    // Create homepage if it doesn't exist
    $homepage = get_page_by_title('Home');
    if (!$homepage) {
        $homepage_id = wp_insert_post(array(
            'post_title' => 'Home',
            'post_content' => '<!-- This page uses the theme\'s built-in homepage template -->',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ));
    } else {
        $homepage_id = $homepage->ID;
    }

    // Set as homepage
    update_option('show_on_front', 'page');
    update_option('page_on_front', $homepage_id);
}
