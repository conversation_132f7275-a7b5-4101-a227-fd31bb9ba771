<?php
/**
 * 7Magic Theme Functions
 *
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function sevenmagic_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Add support for wide and full alignment
    add_theme_support('align-wide');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => esc_html__('Primary Menu', '7magic'),
        'footer'  => esc_html__('Footer Menu', '7magic'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'sevenmagic_setup');

/**
 * Enqueue Scripts and Styles
 */
function sevenmagic_scripts() {
    // Main theme stylesheet
    wp_enqueue_style('sevenmagic-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Ulike original CSS files
    wp_enqueue_style('ulike-theme', get_template_directory_uri() . '/assets/css/theme.min.css', array(), '1.0.0');
    wp_enqueue_style('ulike-common', get_template_directory_uri() . '/assets/css/common-ulike.min.css', array(), '1.0.0');
    wp_enqueue_style('ulike-custom', get_template_directory_uri() . '/assets/css/custom_css.css', array(), '1.0.0');
    wp_enqueue_style('swiper-css', get_template_directory_uri() . '/assets/css/swiper-bundle.min.css', array(), '1.0.0');
    
    // JavaScript files
    wp_enqueue_script('jquery');
    wp_enqueue_script('swiper-js', get_template_directory_uri() . '/assets/js/swiper-bundle.min.js', array('jquery'), '1.0.0', true);
    wp_enqueue_script('gsap', get_template_directory_uri() . '/assets/js/gsap.min.js', array(), '1.0.0', true);
    wp_enqueue_script('ulike-theme-js', get_template_directory_uri() . '/assets/js/theme.js', array('jquery'), '1.0.0', true);
    wp_enqueue_script('sevenmagic-custom', get_template_directory_uri() . '/assets/js/custom.js', array('jquery'), '1.0.0', true);
    
    // Localize script for AJAX
    wp_localize_script('sevenmagic-custom', 'sevenmagic_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('sevenmagic_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'sevenmagic_scripts');

/**
 * Register Widget Areas
 */
function sevenmagic_widgets_init() {
    register_sidebar(array(
        'name'          => esc_html__('Sidebar', '7magic'),
        'id'            => 'sidebar-1',
        'description'   => esc_html__('Add widgets here.', '7magic'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => esc_html__('Footer Widget Area', '7magic'),
        'id'            => 'footer-widgets',
        'description'   => esc_html__('Add widgets to the footer area.', '7magic'),
        'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="footer-widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'sevenmagic_widgets_init');

/**
 * Custom Post Types for Products
 */
function sevenmagic_register_post_types() {
    // Products Post Type
    register_post_type('product', array(
        'labels' => array(
            'name'               => 'Products',
            'singular_name'      => 'Product',
            'menu_name'          => 'Products',
            'add_new'            => 'Add New Product',
            'add_new_item'       => 'Add New Product',
            'edit_item'          => 'Edit Product',
            'new_item'           => 'New Product',
            'view_item'          => 'View Product',
            'search_items'       => 'Search Products',
            'not_found'          => 'No products found',
            'not_found_in_trash' => 'No products found in trash',
        ),
        'public'       => true,
        'has_archive'  => true,
        'supports'     => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'menu_icon'    => 'dashicons-products',
        'rewrite'      => array('slug' => 'products'),
    ));
}
add_action('init', 'sevenmagic_register_post_types');

/**
 * Add Custom Meta Boxes for Products
 */
function sevenmagic_add_product_meta_boxes() {
    add_meta_box(
        'product_details',
        'Product Details',
        'sevenmagic_product_details_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'sevenmagic_add_product_meta_boxes');

function sevenmagic_product_details_callback($post) {
    wp_nonce_field('sevenmagic_save_product_details', 'sevenmagic_product_nonce');
    
    $amazon_url = get_post_meta($post->ID, '_amazon_url', true);
    $price = get_post_meta($post->ID, '_price', true);
    $features = get_post_meta($post->ID, '_features', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="amazon_url">Amazon URL:</label></th>';
    echo '<td><input type="url" id="amazon_url" name="amazon_url" value="' . esc_attr($amazon_url) . '" style="width: 100%;" /></td></tr>';
    echo '<tr><th><label for="price">Price:</label></th>';
    echo '<td><input type="text" id="price" name="price" value="' . esc_attr($price) . '" /></td></tr>';
    echo '<tr><th><label for="features">Key Features:</label></th>';
    echo '<td><textarea id="features" name="features" rows="5" style="width: 100%;">' . esc_textarea($features) . '</textarea></td></tr>';
    echo '</table>';
}

function sevenmagic_save_product_details($post_id) {
    if (!isset($_POST['sevenmagic_product_nonce']) || !wp_verify_nonce($_POST['sevenmagic_product_nonce'], 'sevenmagic_save_product_details')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['amazon_url'])) {
        update_post_meta($post_id, '_amazon_url', sanitize_url($_POST['amazon_url']));
    }
    
    if (isset($_POST['price'])) {
        update_post_meta($post_id, '_price', sanitize_text_field($_POST['price']));
    }
    
    if (isset($_POST['features'])) {
        update_post_meta($post_id, '_features', sanitize_textarea_field($_POST['features']));
    }
}
add_action('save_post', 'sevenmagic_save_product_details');

/**
 * Elementor Compatibility
 */
function sevenmagic_elementor_support() {
    add_theme_support('elementor');
}
add_action('after_setup_theme', 'sevenmagic_elementor_support');

/**
 * Custom Shortcodes
 */
function sevenmagic_product_grid_shortcode($atts) {
    $atts = shortcode_atts(array(
        'limit' => 4,
        'category' => '',
    ), $atts);
    
    $args = array(
        'post_type' => 'product',
        'posts_per_page' => intval($atts['limit']),
        'post_status' => 'publish',
    );
    
    if (!empty($atts['category'])) {
        $args['meta_query'] = array(
            array(
                'key' => '_product_category',
                'value' => $atts['category'],
                'compare' => 'LIKE',
            ),
        );
    }
    
    $products = new WP_Query($args);
    
    ob_start();
    
    if ($products->have_posts()) {
        echo '<div class="product-grid">';
        while ($products->have_posts()) {
            $products->the_post();
            $amazon_url = get_post_meta(get_the_ID(), '_amazon_url', true);
            $price = get_post_meta(get_the_ID(), '_price', true);
            
            echo '<div class="product-card fade-in">';
            if (has_post_thumbnail()) {
                echo '<img src="' . get_the_post_thumbnail_url(get_the_ID(), 'medium') . '" alt="' . get_the_title() . '" class="product-image">';
            }
            echo '<div class="product-info">';
            echo '<h3 class="product-title">' . get_the_title() . '</h3>';
            echo '<p class="product-description">' . get_the_excerpt() . '</p>';
            if ($price) {
                echo '<p class="product-price">$' . esc_html($price) . '</p>';
            }
            if ($amazon_url) {
                echo '<a href="' . esc_url($amazon_url) . '" target="_blank" class="amazon-buy-btn">Buy on Amazon</a>';
            }
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
        wp_reset_postdata();
    }
    
    return ob_get_clean();
}
add_shortcode('product_grid', 'sevenmagic_product_grid_shortcode');

/**
 * Customize Login Page
 */
function sevenmagic_login_logo() {
    echo '<style type="text/css">
        #login h1 a, .login h1 a {
            background-image: url(' . get_template_directory_uri() . '/assets/images/logo.png);
            height: 80px;
            width: 200px;
            background-size: contain;
            background-repeat: no-repeat;
            padding-bottom: 30px;
        }
    </style>';
}
add_action('login_enqueue_scripts', 'sevenmagic_login_logo');

/**
 * Remove WordPress version from head
 */
remove_action('wp_head', 'wp_generator');

/**
 * Optimize WordPress
 */
function sevenmagic_optimize_wp() {
    // Remove unnecessary scripts
    wp_deregister_script('wp-embed');
    
    // Remove emoji scripts
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('wp_print_styles', 'print_emoji_styles');
}
add_action('init', 'sevenmagic_optimize_wp');

/**
 * Add async/defer to scripts
 */
function sevenmagic_add_async_defer($tag, $handle, $src) {
    $async_scripts = array('gsap', 'swiper-js');
    $defer_scripts = array('sevenmagic-custom', 'ulike-theme-js');
    
    if (in_array($handle, $async_scripts)) {
        return str_replace('<script ', '<script async ', $tag);
    }
    
    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'sevenmagic_add_async_defer', 10, 3);
