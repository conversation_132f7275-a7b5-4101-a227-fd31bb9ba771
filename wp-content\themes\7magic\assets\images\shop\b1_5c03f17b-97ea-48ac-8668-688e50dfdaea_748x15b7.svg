<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 28.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 748 175" style="enable-background:new 0 0 748 175;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#5C34DC;}
	.st1{fill:#FFFFFF;filter:url(#Adobe_OpacityMaskFilter);}
	.st2{mask:url(#mask0_116_16471_00000117661656478522788160000013873162845574016180_);}
	.st3{fill:#FFFFFF;}
</style>
<g>
	<path class="st0" d="M721.5,151.3H29.3c-4.2,0-7.7-3.4-7.7-7.7V33.1c0-4.2,3.4-7.7,7.7-7.7h692.2c4.2,0,7.7,3.4,7.7,7.7v110.5
		C729.2,147.8,725.8,151.3,721.5,151.3z"/>
	<g>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="62.6" y="61.4" width="84.9" height="51.7">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="62.6" y="61.4" width="84.9" height="51.7" id="mask0_116_16471_00000117661656478522788160000013873162845574016180_">
			<path class="st1" d="M147.5,61.4H62.6v51.8h84.9V61.4z"/>
		</mask>
		<g class="st2">
			<path class="st3" d="M131.8,62.3c-5.2,0-24.3,0-24.3,0v49.8c0,0,14,0,19.6,0c6.7,0,15.8-3.2,15.8-13.7c0-5.6-2.7-8.7-6.2-10.5
				c5.8-1.3,10.8-4.8,10.8-12.4C147.5,64.9,138.5,62.3,131.8,62.3z M125.2,102.4c-2.3,0-6.5,0-6.7,0V90.9l6.7,0.1
				c3.5,0,6.6,1.6,6.6,5.7C131.8,100.8,128.6,102.4,125.2,102.4z M129.8,83.4c-4.4,0-11.3,0-11.3,0V72c0,0,6.9,0,11.3,0
				c4.4,0,6.4,2.5,6.4,5.7C136.2,80.9,134.2,83.4,129.8,83.4z M62.6,77c0-6.1,3.6-15.7,21-15.7c18.4,0,20.4,15.2,20.4,15.2
				l-10.2,2.9C93,74.6,89.1,72,83.5,72c-6.5,0-9.6,1.9-9.6,5.2c0,10.2,29.7,1.9,29.7,21.3c0,8.4-6.5,14.5-19,14.5
				c-11.5,0-16.5-8-18-12.7l10.4-3c1.3,2.8,3.4,5,8.2,5c4.4,0,7.1-1.8,7.1-4.2C92.3,89.6,62.6,97.4,62.6,77z"/>
		</g>
		<rect x="166.7" y="62.3" class="st3" width="1.5" height="49.8"/>
		<g>
			<path class="st3" d="M202,105.4l-14.3-38.8h8.5l10,28.7l10.1-28.7h8.2l-14.2,38.8H202z"/>
			<path class="st3" d="M222.1,91.2c0-8.4,5.8-14.7,13.8-14.7c8.4,0,13,6.3,13,14.2v2.2h-19.5c0.5,4.3,3.2,6.9,6.9,6.9
				c2.9,0,5.2-1.4,6.1-4.2l6.4,2.3c-2.1,5.2-6.9,8.1-12.5,8.1C227.8,106,222.1,100,222.1,91.2z M229.6,87.7h11.8
				c-0.1-2.8-1.8-5.2-5.5-5.2C232.8,82.5,230.5,84.3,229.6,87.7z"/>
			<path class="st3" d="M269.5,84.3c-0.9-0.1-1.5-0.2-2.4-0.2c-3.9,0-7,2.4-7,6.8v14.4h-7.6V77h7.6v4c1.2-2.5,4-4.2,7.6-4.2
				c0.8,0,1.3,0.1,1.8,0.1V84.3z"/>
			<path class="st3" d="M272,66.6h7.7v7H272V66.6z M279.7,105.4h-7.6V77h7.6V105.4z"/>
			<path class="st3" d="M299.1,72.4c-0.7-0.1-1.1-0.1-1.9-0.1c-2.4,0-3.6,0.7-3.6,3.5V77h5.5v6.3h-5.5v22.1H286V83.3h-3.8V77h3.8
				v-1.6c0-6,3.2-9.3,10.2-9.3c1.1,0,1.7,0,2.9,0.1V72.4z"/>
			<path class="st3" d="M306.2,115.2c-1.5,0-2-0.1-3-0.2V109c0.8,0.1,1.3,0.1,2.2,0.1c2,0,3.1-0.5,3.9-2.6l0.9-2.3L300,77h7.8
				l6.4,18.7l6.3-18.7h7.6l-11.2,30C315.1,112,312.5,115.2,306.2,115.2z"/>
			<path class="st3" d="M344.1,93c2.9,4,7,6.3,11.3,6.3c4,0,7-1.8,7-5.1c0-3.6-3.7-4-9.5-5.3c-5.8-1.3-12-3.2-12-10.9
				c0-7.4,6.4-12,14.4-12c6.7,0,11.9,3,14.6,6.9l-5.2,5.1c-2.3-3.2-5.3-5.3-9.6-5.3c-3.8,0-6.4,1.8-6.4,4.6c0,3,2.8,3.6,7.9,4.8
				c6.2,1.3,13.7,2.9,13.7,11.4c0,7.8-6.9,12.5-15.2,12.5c-6.5,0-13.3-3-16.5-7.7L344.1,93z"/>
			<path class="st3" d="M374,83.3h-3.8V77h3.8v-8.1h7.5V77h5.8v6.3h-5.8v13.1c0,2.4,1.3,2.8,3.6,2.8c1.1,0,1.6-0.1,2.6-0.2v6.3
				c-1.3,0.3-3,0.4-4.9,0.4c-5.7,0-8.7-1.9-8.7-7.7V83.3z"/>
			<path class="st3" d="M415.4,105.4h-7.6v-2.9c-1.6,1.8-4,3.5-7.7,3.5c-5.9,0-9.7-4-9.7-10.2V77h7.6v17.1c0,3.1,1.4,5.2,4.5,5.2
				c2.6,0,5.2-1.9,5.2-5.6V77h7.6V105.4z"/>
			<path class="st3" d="M419.1,91.2c0-8,4.5-14.7,12.4-14.7c3.6,0,6.3,1.4,7.9,3.5V66.6h7.6v38.8h-7.6v-2.9
				c-1.7,2.1-4.4,3.5-7.9,3.5C423.6,106,419.1,99.3,419.1,91.2z M433.2,82.9c-4.3,0-6.5,3.4-6.5,8.3c0,4.9,2.3,8.3,6.5,8.3
				c3.6,0,6.3-2.7,6.3-7.5v-1.6C439.5,85.5,436.8,82.9,433.2,82.9z"/>
			<path class="st3" d="M450.5,91.2c0-8.4,5.8-14.7,13.8-14.7c8.4,0,13,6.3,13,14.2v2.2h-19.5c0.5,4.3,3.2,6.9,6.9,6.9
				c2.9,0,5.2-1.4,6.1-4.2l6.4,2.3c-2.1,5.2-6.9,8.1-12.5,8.1C456.2,106,450.5,100,450.5,91.2z M458,87.7h11.8
				c-0.1-2.8-1.8-5.2-5.5-5.2C461.2,82.5,458.9,84.3,458,87.7z"/>
			<path class="st3" d="M481,77h7.6v2.9c1.6-1.8,4.2-3.5,7.8-3.5c5.9,0,9.6,4,9.6,10.2v18.7h-7.6V88.8c0-3.2-1.3-5.5-4.5-5.5
				c-2.6,0-5.2,1.9-5.2,5.6v16.4H481V77z"/>
			<path class="st3" d="M511.7,83.3h-3.8V77h3.8v-8.1h7.5V77h5.8v6.3h-5.8v13.1c0,2.4,1.3,2.8,3.6,2.8c1.1,0,1.6-0.1,2.6-0.2v6.3
				c-1.3,0.3-3,0.4-4.9,0.4c-5.7,0-8.7-1.9-8.7-7.7V83.3z"/>
			<path class="st3" d="M542.5,93c2.9,4,7,6.3,11.3,6.3c4,0,7-1.8,7-5.1c0-3.6-3.7-4-9.5-5.3c-5.8-1.3-12-3.2-12-10.9
				c0-7.4,6.4-12,14.4-12c6.7,0,11.9,3,14.6,6.9l-5.2,5.1c-2.3-3.2-5.3-5.3-9.6-5.3c-3.8,0-6.4,1.8-6.4,4.6c0,3,2.8,3.6,7.9,4.8
				c6.2,1.3,13.7,2.9,13.7,11.4c0,7.8-6.9,12.5-15.2,12.5c-6.5,0-13.3-3-16.5-7.7L542.5,93z"/>
			<path class="st3" d="M572.5,83.3h-3.8V77h3.8v-8.1h7.5V77h5.8v6.3h-5.8v13.1c0,2.4,1.3,2.8,3.6,2.8c1.1,0,1.6-0.1,2.6-0.2v6.3
				c-1.3,0.3-3,0.4-4.9,0.4c-5.7,0-8.7-1.9-8.7-7.7V83.3z"/>
			<path class="st3" d="M588,97.5c0-5.7,4.5-8,10.3-9.1l7.1-1.3v-0.4c0-2.4-1.2-3.9-4.4-3.9c-2.8,0-4.3,1.2-4.9,3.8l-7.2-1.3
				c1.3-4.8,5.6-8.7,12.4-8.7c7.2,0,11.5,3.3,11.5,9.9v11.6c0,1.5,0.6,2,2.3,1.8v5.7c-4.8,0.7-7.7-0.3-9-2.6c-1.8,2-4.7,3.1-8.4,3.1
				C592.1,105.8,588,102.5,588,97.5z M605.3,92.3l-5.6,1.1c-2.5,0.5-4.3,1.3-4.3,3.6c0,1.9,1.5,3.1,3.7,3.1c3.2,0,6.2-1.7,6.2-4.9
				V92.3z"/>
			<path class="st3" d="M618.9,83.3h-3.8V77h3.8v-8.1h7.5V77h5.8v6.3h-5.8v13.1c0,2.4,1.3,2.8,3.6,2.8c1.1,0,1.6-0.1,2.6-0.2v6.3
				c-1.3,0.3-3,0.4-4.9,0.4c-5.7,0-8.7-1.9-8.7-7.7V83.3z"/>
			<path class="st3" d="M660.3,105.4h-7.6v-2.9c-1.6,1.8-4,3.5-7.7,3.5c-5.9,0-9.7-4-9.7-10.2V77h7.6v17.1c0,3.1,1.4,5.2,4.5,5.2
				c2.6,0,5.2-1.9,5.2-5.6V77h7.6V105.4z"/>
			<path class="st3" d="M667.5,95.8c1.9,2.6,5.4,4.3,8.6,4.3c2.5,0,4.8-0.9,4.8-2.9c0-2.2-2.1-2.4-6.6-3.3c-4.6-0.9-9.7-2.1-9.7-8.2
				c0-5.5,4.8-9.2,11.6-9.2c5,0,9.5,2.1,11.7,4.9l-4.3,4.8c-1.9-2.4-4.6-3.8-7.6-3.8c-2.4,0-3.9,1-3.9,2.6c0,1.8,1.9,2.2,5.4,2.9
				c5,1,10.9,2.2,10.9,8.6c0,5.9-5.5,9.6-12.3,9.6c-5.1,0-10.5-1.9-13.1-5.4L667.5,95.8z"/>
		</g>
	</g>
</g>
</svg>
