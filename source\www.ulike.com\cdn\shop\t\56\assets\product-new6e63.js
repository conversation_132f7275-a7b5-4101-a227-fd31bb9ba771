let variantId="";var g_product_name="";$(function(){g_product_name=isMobile()?"M-":"",location.pathname==="/products/sapphire-air-10-ipl-hair-removal"?g_product_name+="A10 ":location.pathname==="/products/sapphire-air-3-ipl-hair-removal"?g_product_name+="A3 ":location.pathname==="/products/ulike-x-ipl-hair-removal"&&(g_product_name+="X ");let disCode="";document.addEventListener("variant:changed",function(event){const variant=event.detail.variant;commonGtmEvent(`${g_product_name} Product-colour-${variant.title}`,"","Product"),$(".main-image-swiper .close-three-btn").click(),generateHtmlForVariant(variant.id)}),document.addEventListener("cart:refresh",function(event){autoDiscountCode(disCode)}),$(document).on("click",".product-info-new .btn-copy-block",function(){let ths=$(this),code=ths.find(".code").text();copyTextFallback(code),commonGtmEvent(`${g_product_name} Product-copy ${code}`,"","Product"),disCode=code,autoDiscountCode(code),ths.hide(),ths.nextAll(".copy-success").addClass("show"),setTimeout(function(){ths.nextAll(".copy-success").removeClass("show"),ths.show()},3e3)}),$(document).on("click",".product-sticky-form .combo-box__option-item",function(){$(this).attr("aria-selected",!0),$(this).siblings().attr("aria-selected",!1);let name=$(this).attr("value");$(`.color-swatch[value="${name}"]`).find('[type="radio"]').click(),commonGtmEvent(g_product_name+"Product-Floating-colour-"+name,"","Product")});const shareUrls={facebook:"https://www.facebook.com/sharer/sharer.php?u={url}&quote={title}",twitter:"https://twitter.com/intent/tweet?url={url}&text={title}",pinterest:"https://pinterest.com/pin/create/button/?url={url}&description={description}",email:"mailto:?subject={title}&body={description}%20{url}"};$(document).on("click",".main-image-swiper .share-button",function(){const network=$(this).data("network");commonGtmEvent(g_product_name+"Product-share-"+network,"","Product");const url=encodeURIComponent(window.location.href),title=encodeURIComponent(document.title),description=encodeURIComponent("This is exactly what I've been waiting for\u2014the Ulike Air 10 IPL device gives you long-lasting smooth skin in just 2 weeks. Professional hair removal results at home? Yes, please! Check it out:");let shareUrl=shareUrls[network].replace("{url}",url).replace("{title}",title).replace("{description}",description);if(network==="email"){const hiddenLink=document.getElementById("hiddenMailtoLink");hiddenLink.href=shareUrl,hiddenLink.click()}else window.open(shareUrl,"_blank")}),isMobile()&&$(document).on("click",".main-image-swiper .box-share",function(){navigator.share?navigator.share({title:"",text:"This is exactly what I've been waiting for\u2014the Ulike Air 10 IPL device gives you long-lasting smooth skin in just 2 weeks. Professional hair removal results at home? Yes, please! Check it out:",url:location.href}).then(()=>console.log("\u5206\u4EAB\u6210\u529F")).catch(error=>console.log("\u5206\u4EAB\u5931\u8D25",error)):console.log("\u5F53\u524D\u6D4F\u89C8\u5668\u4E0D\u652F\u6301 Web \u5206\u4EAB\u529F\u80FD")}),$(document).on("click",".product-sticky-up",function(){scrollToElement()}),$(window).scroll(function(){let targetSelector=isMobile()?".p-evaluate-carousel":".box-how-to-use",targetOffset=$(targetSelector).length?$(targetSelector).offset().top:$("#footer-new").offset().top-400;$(this).scrollTop()>targetOffset?$(".product-sticky-up").fadeIn():$(".product-sticky-up").fadeOut()});var urlParams=new URLSearchParams(window.location.search),pDiscount=urlParams.get("add-discount"),defDiscount=urlParams.get("discount");if(pDiscount?(setCookie("discount_code",pDiscount,1),addToCart(variantId),setTimeout(function(){autoDiscountCode(pDiscount)},3e3)):defDiscount?(setCookie("discount_code",defDiscount,1),setTimeout(function(){autoDiscountCode(defDiscount)},3e3)):setTimeout(function(){if($(".p-box-discount-code").attr("auto-apply")=="true"){let code=$(".p-box-discount-code").attr("auto-code");disCode=code,setCookie("discount_code",code,1),autoDiscountCode(code)}},2e3),$(document).on("click",".button-buy-now ",function(){let pid=$(".product-form__buy-buttons [name='id']").val(),code=$(".p-box-discount-code").attr("auto-code"),giftParam={};$(".button-buy-now").text("").attr("aria-busy","true"),buyProduct(pid,giftParam,code),window._conv_q=window._conv_q||[],_conv_q.push(["triggerConversion","100476462"]),setTimeout(function(){$(".button-buy-now").text("Buy Now").attr("aria-busy","false")},2e3)}),isMobile()){let animatePercentage2=function(element,start,end,duration){const increment=(end-start)/(duration/10);let current=start;const interval=setInterval(function(){current+=increment,(increment>0&&current>=end||increment<0&&current<=end)&&(current=end,clearInterval(interval)),element.text(current.toFixed(2)+"%")},10)};var animatePercentage=animatePercentage2;$(".collapsible-header").on("click",function(){const $content=$(this).next(".collapsible-content"),$toggleBtn=$(this).find(".toggle-btn");$content.is(":visible")?($content.slideUp(300),$toggleBtn.text("+")):($content.slideDown(300),$toggleBtn.text("\u2212"))});const reviweSwiper=new Swiper(".reviwe-swiper",{loop:!0,pagination:{el:".swiper-pagination",clickable:!0}});$(document).on("click",".product-form .see-all-btn",function(){scrollToElement("judgeme_product_reviews")}),$(".product-form").on("click",".box-hair-removal .tab",function(){$(this).addClass("active").siblings(".tab").removeClass("active");const time=$(this).attr("time"),timeText=$(this).attr("time_text"),percent=$(this).attr("percent"),percentText=$(this).attr("percent_text");let $name=$(".box-hair-removal .card").eq(0).find(".name"),$name2=$(".box-hair-removal .card").eq(1).find(".name");if($name.text(percent),$(".box-hair-removal .card").eq(0).find(".desc").text(percentText),$name2.text(time),$(".box-hair-removal .card").eq(1).find(".desc").text(timeText),$(this).attr("data-animated")===void 0){const numericEnd=parseFloat(percent.replace("%",""));let minNum=(Math.random()*10+50).toFixed(2);animatePercentage2($name,Number(minNum),numericEnd,1500),$(this).attr("data-animated","true")}}),$(".tabs .tab.active").click(),$(".product-form").on("click",".review-card .read-more",function(){const $desc=$(this).closest(".review-card");$desc.find(".desc-full").toggle(),$desc.find(".desc-short").toggle(),$desc.find(".read-more").toggle()})}setGaDataBuryingPoint()});let swiperproductNav=null,swiperMain=null;function initProductImages(){swiperproductNav!==null&&swiperproductNav.destroy(!0,!0),swiperproductNav=new Swiper(".product-media-new .swiper_nav",{slidesPerView:"auto",spaceBetween:10,freeMode:!0,simulateTouch:!0,mousewheel:!0,watchSlidesVisibility:!0,watchSlidesProgress:!0,breakpoints:{640:{direction:"horizontal"},1024:{direction:"vertical"}}}),swiperMain!==null&&swiperMain.destroy(!0,!0),swiperMain=new Swiper(".product-media-new .swiper_main",{spaceBetween:0,lazy:{loadPrevNext:!0},loop:!0,pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper_main .swiper-button-next",prevEl:".swiper_main .swiper-button-prev"},thumbs:{swiper:swiperproductNav},on:{slideChange:function(){typeof commonGtmEvent=="function"&&commonGtmEvent(g_product_name+"Product-indicator",this.activeIndex,"Product")}}})}function getUrlParameter(paramName){return new URLSearchParams(window.location.search).get(paramName)}function generateHtmlForVariant(vid){let currentVariant=vid||variantArr[0];variantId=currentVariant.vid;let variant=getUrlParameter("variant");variant&&(variantId=variant,currentVariant=variantArr.filter(item=>item.vid==variant)[0]);const{position,max}=currentVariant;let _productHtml="",_productnavHtml="";for(let i=position-1;i<max;i++){const ths=productMediaArr[i];let imageUrl=ths.src,itemMainHtml="";ths.alt=ths.alt?ths.alt:"Ulike Product Image";let isHighPriority=i==position-1,loadingAttr=isHighPriority?'fetchpriority="high"':'loading="lazy"';const pictureElement=`
      <picture>
          <source media="(max-width: 768px)" srcset="${convertShopifyImageUrl(imageUrl,isHighPriority?"1740x":"800x")}">
          <img class="product__media-image" ${loadingAttr} src="${convertShopifyImageUrl(imageUrl,"1740x")}" alt="${ths.alt}" media_type="${ths.media_type}" >
      </picture>
      `;ths.media_type=="video"?(imageUrl=ths.preview_image.src,itemMainHtml=generateVideoHtml(ths.sources)):itemMainHtml=`
        <a data-id="${i}" data-pswp-src="${imageUrl}" rel="nofollow" data-pswp-width="${ths.width}"
        data-pswp-height="${ths.height}" alt="${ths.alt}" >
          ${pictureElement}
        </a> 
      `,_productHtml+=`
       <div class="swiper-slide">${itemMainHtml}</div>`,_productnavHtml+=`
        <div class="swiper-slide">
            <div class="responsive-clip-path">
              <img class="product__media-image" width="68px" height="63px"  ${loadingAttr} src="${convertShopifyImageUrl(imageUrl,"80x")}"  alt="${ths.alt}" media_type="${ths.media_type}"  />
            </div>
        </div>`}setTimeout(function(){if($(".swiper_main .swiper-wrapper").html(_productHtml),$(".swiper_nav .swiper-wrapper").html(_productnavHtml),initProductImages(),currentVariant.url?$(".product-slider-btn-model").attr("attr-model-url",currentVariant.url).show():$(".product-slider-btn-model").attr("attr-model-url",currentVariant.url).hide(),currentVariant.isDiscount=="true"||currentVariant.isDiscount==""?$(".product-info-new .p-box-discount-code").show():$(".product-info-new .p-box-discount-code").hide(),currentVariant.hide_discount_text=="true"||currentVariant.hide_discount_text==""?$(".product-info-new .p-box-price .information").show():$(".product-info-new .p-box-price .information").hide(),currentVariant.isOriginalPrice=="true"){let _price=currentVariant.price;$(currentVariant.price).text().replace("$",""),$(".p-box-price .price").html("US"+_price),currentVariant.price==currentVariant.original_price?$(".p-box-price .original_price").html(""):$(".p-box-price .original_price").html(currentVariant.original_price);let curPiice=$(_price).text().replace("$","");$(".pay-button").attr("data-pp-amount",curPiice),$("klarna-placement").attr("data-purchase-amount",curPiice),$(".affirm-as-low-as").attr("data-amount",curPiice)}else currentVariant.price!=""&&currentVariant.page_price!=""?($(".p-box-price .price").html("US"+formatMoney(currentVariant.page_price)),$(".p-box-price .original_price").html("US"+currentVariant.price),$(".pay-button").attr("data-pp-amount",currentVariant.page_price),$(".affirm-as-low-as").attr("data-amount",currentVariant.page_price)):currentVariant.price!=""&&($(".p-box-price .price").html("US"+currentVariant.price),$(".p-box-price .original_price").html(""));currentVariant.variantTitle!=""&&$(".product-info-new .product-meta__title").html(currentVariant.variantTitle),currentVariant.describe!=""&&$(".product-info-new .product-subinfo").html(currentVariant.describe),currentVariant.discount_pictures!=""?($(".product-info-new .p-box-discount-code .abs-discount-detail").hide(),$(".product-info-new .p-box-discount-code .bg-image img").attr("srcset",currentVariant.discount_pictures),$(".product-info-new .p-box-discount-code .bg-image img").attr("src",currentVariant.discount_pictures)):($(".product-info-new .p-box-discount-code .abs-discount-detail").show(),$(".product-info-new .p-box-discount-code .bg-image img").attr("srcset",$(".product-info-new .p-box-discount-code .bg-image").attr("old-image")),$(".product-info-new .p-box-discount-code .bg-image img").attr("src",$(".product-info-new .p-box-discount-code .bg-image").attr("old-image")))},10)}function convertShopifyImageUrl(url,size){return url!=null?url.replace(/(\.[^/.]+)$/,`_${size}$1`):url}function generateVideoHtml(videoData){var filteredData=filterAndFindMaxWidth(videoData),videoHtml='<video id="dynamicVideo" autoplay muted loop playsinline controls>';return Object.values(filteredData).forEach(function(video){videoHtml+=`<source src="${video.url}" type="${video.mime_type}">`}),videoHtml+="Your browser does not support the video tag.</video>",videoHtml}function filterAndFindMaxWidth(data){var result={};return data.forEach(function(item){(!result[item.mime_type]||item.width>result[item.mime_type].width)&&(result[item.mime_type]=item)}),result}function setGaDataBuryingPoint(){$(".product-info-new").on("click",".shopify-payment-button",function(){commonGtmEvent(`${g_product_name} Product-checkout paypal`,"","Product")}),$(".product-info-new").on("click","#StickyAddToCart",function(){commonGtmEvent(`${g_product_name} Product-Floating-add to cart`,"","Product")})}function formatMoney(cents,format=""){typeof cents=="string"&&(cents=cents.replace(".",""));const placeholderRegex=/\{\{\s*(\w+)\s*\}\}/,formatString=format||window.themeVariables.settings.moneyFormat;function defaultTo(value2,defaultValue){return value2==null||value2!==value2?defaultValue:value2}function formatWithDelimiters(number,precision,thousands,decimal){if(precision=defaultTo(precision,2),thousands=defaultTo(thousands,","),decimal=defaultTo(decimal,"."),isNaN(number)||number==null)return 0;number=(number/100).toFixed(precision);let parts=number.split("."),dollarsAmount=parts[0].replace(/(\d)(?=(\d\d\d)+(?!\d))/g,"$1"+thousands),centsAmount=parts[1]?decimal+parts[1]:"";return dollarsAmount+centsAmount}let value="";switch(formatString.match(placeholderRegex)[1]){case"amount":value=formatWithDelimiters(cents,2);break;case"amount_no_decimals":value=formatWithDelimiters(cents,0);break;case"amount_with_space_separator":value=formatWithDelimiters(cents,2," ",".");break;case"amount_with_comma_separator":value=formatWithDelimiters(cents,2,".",",");break;case"amount_with_apostrophe_separator":value=formatWithDelimiters(cents,2,"'",".");break;case"amount_no_decimals_with_comma_separator":value=formatWithDelimiters(cents,0,".",",");break;case"amount_no_decimals_with_space_separator":value=formatWithDelimiters(cents,0," ");break;case"amount_no_decimals_with_apostrophe_separator":value=formatWithDelimiters(cents,0,"'");break}return formatString.indexOf("with_comma_separator")!==-1,formatString.replace(placeholderRegex,value)}
//# sourceMappingURL=/cdn/shop/t/56/assets/product-new.js.map?v=161574458692980442831751510555
