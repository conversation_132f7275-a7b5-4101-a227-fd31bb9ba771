/**
 * 7Magic Theme Custom JavaScript
 * 
 * @package 7Magic
 * <AUTHOR>
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        
        // Initialize theme features
        initScrollAnimations();
        initSmoothScrolling();
        initMobileMenu();
        initProductCarousel();
        initLazyLoading();
        
        // Amazon link tracking
        trackAmazonClicks();
        
    });

    /**
     * Scroll Animations
     */
    function initScrollAnimations() {
        // Intersection Observer for fade-in animations
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe all fade-in elements
            document.querySelectorAll('.fade-in').forEach(function(el) {
                observer.observe(el);
            });
        } else {
            // Fallback for older browsers
            $('.fade-in').addClass('visible');
        }
    }

    /**
     * Smooth Scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800, 'easeInOutQuart');
            }
        });
    }

    /**
     * Mobile Menu Toggle
     */
    function initMobileMenu() {
        $('.menu-toggle').on('click', function() {
            $('.nav-menu').toggleClass('active');
            $(this).toggleClass('active');
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.main-navigation').length) {
                $('.nav-menu').removeClass('active');
                $('.menu-toggle').removeClass('active');
            }
        });
    }

    /**
     * Product Carousel (if using Swiper)
     */
    function initProductCarousel() {
        if (typeof Swiper !== 'undefined' && $('.product-carousel').length) {
            new Swiper('.product-carousel', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                    },
                    768: {
                        slidesPerView: 3,
                    },
                    1024: {
                        slidesPerView: 4,
                    },
                }
            });
        }
    }

    /**
     * Lazy Loading for Images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(function(img) {
                imageObserver.observe(img);
            });
        }
    }

    /**
     * Track Amazon Button Clicks
     */
    function trackAmazonClicks() {
        $('.amazon-buy-btn').on('click', function(e) {
            const productName = $(this).closest('.product-card').find('.product-title').text();
            
            // Google Analytics tracking (if available)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'click', {
                    'event_category': 'Amazon Redirect',
                    'event_label': productName,
                    'value': 1
                });
            }
            
            // Console log for debugging
            console.log('Amazon redirect clicked:', productName);
        });
    }

    /**
     * Back to Top Button
     */
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
    });

    $('#back-to-top').on('click', function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    /**
     * Search Functionality
     */
    $('.search-icon').on('click', function() {
        // Toggle search overlay or redirect to search page
        if ($('.search-overlay').length) {
            $('.search-overlay').toggleClass('active');
        } else {
            window.location.href = '/search';
        }
    });

    /**
     * Newsletter Form
     */
    $('.newsletter-form').on('submit', function(e) {
        e.preventDefault();
        
        const email = $(this).find('input[type="email"]').val();
        const $button = $(this).find('button[type="submit"]');
        const originalText = $button.text();
        
        // Basic email validation
        if (!isValidEmail(email)) {
            alert('Please enter a valid email address.');
            return;
        }
        
        // Show loading state
        $button.text('Subscribing...').prop('disabled', true);
        
        // Simulate AJAX request (replace with actual endpoint)
        setTimeout(function() {
            $button.text('Subscribed!').removeClass('amazon-buy-btn').addClass('success-btn');
            
            // Reset after 3 seconds
            setTimeout(function() {
                $button.text(originalText).prop('disabled', false).removeClass('success-btn').addClass('amazon-buy-btn');
            }, 3000);
        }, 1000);
    });

    /**
     * Email validation helper
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Product Quick View (if needed)
     */
    $('.product-card').on('click', '.quick-view-btn', function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        
        // Open modal or redirect to product page
        console.log('Quick view for product:', productId);
    });

    /**
     * Parallax Effect for Hero Section
     */
    if ($('.hero-section').length) {
        $(window).scroll(function() {
            const scrolled = $(window).scrollTop();
            const parallax = $('.hero-section');
            const speed = 0.5;
            
            parallax.css('transform', 'translateY(' + (scrolled * speed) + 'px)');
        });
    }

    /**
     * GSAP Animations (if GSAP is loaded)
     */
    if (typeof gsap !== 'undefined') {
        // Hero section animation
        gsap.from('.hero-title', {duration: 1, y: 50, opacity: 0, delay: 0.2});
        gsap.from('.hero-subtitle', {duration: 1, y: 30, opacity: 0, delay: 0.4});
        gsap.from('.hero-section .amazon-buy-btn', {duration: 1, y: 20, opacity: 0, delay: 0.6});
        
        // Product cards stagger animation
        gsap.from('.product-card', {
            duration: 0.8,
            y: 50,
            opacity: 0,
            stagger: 0.2,
            scrollTrigger: {
                trigger: '.product-grid',
                start: 'top 80%'
            }
        });
    }

    /**
     * Resize handler
     */
    $(window).resize(function() {
        // Handle responsive adjustments
        if ($(window).width() > 768) {
            $('.nav-menu').removeClass('active');
            $('.menu-toggle').removeClass('active');
        }
    });

})(jQuery);
